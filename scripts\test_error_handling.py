#!/usr/bin/env python3
"""
Test runner for error handling validation

This script runs the error handling validation tests to ensure that
work units properly handle and propagate errors.
"""

import logging
import os
import subprocess
import sys

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def run_error_handling_tests():
    """Run the error handling validation tests"""
    logger.info("🧪 Running error handling validation tests...")

    test_file = os.path.join(project_root, "tests", "test_error_handling_validation.py")

    if not os.path.exists(test_file):
        logger.error(f"❌ Test file not found: {test_file}")
        return False

    try:
        # Run pytest on the error handling test file
        result = subprocess.run(
            [
                sys.executable,
                "-m",
                "pytest",
                test_file,
                "-v",
                "--tb=short",
                "--no-header",
            ],
            capture_output=True,
            text=True,
            cwd=project_root,
        )

        if result.returncode == 0:
            logger.info("✅ All error handling tests passed!")
            logger.info(f"Test output:\n{result.stdout}")
            return True
        else:
            logger.error("❌ Some error handling tests failed!")
            logger.error(f"Test output:\n{result.stdout}")
            if result.stderr:
                logger.error(f"Error output:\n{result.stderr}")
            return False

    except Exception as e:
        logger.error(f"❌ Failed to run tests: {e}")
        return False


def validate_work_unit_boundaries():
    """Validate that work unit boundaries are properly defined"""
    logger.info("🔍 Validating work unit boundaries...")

    work_units = [
        # Transaction work units
        ("erp.utils.middleware.transaction", "TransactionMiddleware"),
        ("erp.addons.managers.savepoint_manager", "SavepointManager"),
        ("erp.addons.utils.helpers", "transaction_context"),
        # Request processing work units
        ("erp.http.middleware.base", "ErrorHandlingMiddleware"),
        ("erp.utils.middleware.logging", "LoggingMiddleware"),
        # Addon management work units
        ("erp.addons.managers.addon_manager", "AddonManager"),
        ("erp.addons.installers.addon_installer", "AddonInstaller"),
        # Data loading work units
        ("erp.data.loader", "DataLoader"),
        ("erp.data.processors.base", "BaseProcessor"),
    ]

    missing_units = []

    for module_name, class_or_function in work_units:
        try:
            module = __import__(module_name, fromlist=[class_or_function])
            if not hasattr(module, class_or_function):
                missing_units.append(f"{module_name}.{class_or_function}")
        except ImportError:
            missing_units.append(f"{module_name}.{class_or_function}")

    if missing_units:
        logger.error(f"❌ Missing work units: {', '.join(missing_units)}")
        return False
    else:
        logger.info("✅ All work units are properly defined")
        return True


def validate_exception_hierarchy():
    """Validate that exception hierarchy is properly defined"""
    logger.info("🔍 Validating exception hierarchy...")

    try:
        from erp.addons.exceptions import (
            AddonError,
            AddonInstallationError,
            CircularDependencyError,
            DependencyError,
            MissingDependencyError,
        )

        # Verify inheritance hierarchy
        assert issubclass(DependencyError, AddonError)
        assert issubclass(CircularDependencyError, DependencyError)
        assert issubclass(MissingDependencyError, DependencyError)
        assert issubclass(AddonInstallationError, AddonError)

        logger.info("✅ Exception hierarchy is properly defined")
        return True

    except (ImportError, AssertionError) as e:
        logger.error(f"❌ Exception hierarchy validation failed: {e}")
        return False


def main():
    """Main test runner"""
    logger.info("🚀 Starting error handling validation...")

    success = True

    # Validate work unit boundaries
    if not validate_work_unit_boundaries():
        success = False

    # Validate exception hierarchy
    if not validate_exception_hierarchy():
        success = False

    # Run error handling tests
    if not run_error_handling_tests():
        success = False

    if success:
        logger.info("🎉 All error handling validations passed!")
        return 0
    else:
        logger.error("💥 Some error handling validations failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
