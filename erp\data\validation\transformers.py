"""
Data Transformation Utilities for ERP system

Provides comprehensive data transformation, type conversion, and field mapping
capabilities for data loading operations.
"""

import json
from dataclasses import dataclass, field
from datetime import date, datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Type

from ...logging import get_logger
from ..exceptions import DataLoadingError


class TransformationType(Enum):
    """Types of transformations"""

    TYPE_CONVERSION = "type_conversion"
    VALUE_MAPPING = "value_mapping"
    FIELD_MAPPING = "field_mapping"
    CUSTOM = "custom"


@dataclass
class TransformationResult:
    """Result of transformation operation"""

    success: bool = True
    transformed_value: Any = None
    original_value: Any = None
    transformation_type: Optional[TransformationType] = None
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def add_error(self, message: str):
        """Add an error message"""
        self.errors.append(message)
        self.success = False

    def add_warning(self, message: str):
        """Add a warning message"""
        self.warnings.append(message)


class TransformationError(DataLoadingError):
    """Exception raised when transformation fails"""

    def __init__(
        self, message: str, transformation_result: TransformationResult = None
    ):
        super().__init__(message)
        self.transformation_result = transformation_result


class TypeConverter:
    """Utility for type conversions"""

    def __init__(self):
        self.logger = get_logger(__name__)

        # Type conversion mappings
        self.converters = {
            str: self._to_string,
            int: self._to_integer,
            float: self._to_float,
            bool: self._to_boolean,
            datetime: self._to_datetime,
            date: self._to_date,
            Decimal: self._to_decimal,
            list: self._to_list,
            dict: self._to_dict,
        }

    def convert(
        self, value: Any, target_type: Type, strict: bool = False
    ) -> TransformationResult:
        """Convert value to target type"""
        result = TransformationResult(
            original_value=value, transformation_type=TransformationType.TYPE_CONVERSION
        )

        if value is None:
            result.transformed_value = None
            return result

        if isinstance(value, target_type):
            result.transformed_value = value
            return result

        try:
            if target_type in self.converters:
                result.transformed_value = self.converters[target_type](value, strict)
            else:
                # Try direct conversion
                result.transformed_value = target_type(value)

        except Exception as e:
            result.add_error(
                f"Failed to convert {type(value).__name__} to {target_type.__name__}: {e}"
            )

        return result

    def _to_string(self, value: Any, strict: bool = False) -> str:
        """Convert to string"""
        if isinstance(value, str):
            return value
        elif isinstance(value, (int, float, bool)):
            return str(value)
        elif isinstance(value, (list, dict)):
            return json.dumps(value)
        elif isinstance(value, (datetime, date)):
            return value.isoformat()
        else:
            return str(value)

    def _to_integer(self, value: Any, strict: bool = False) -> int:
        """Convert to integer"""
        if isinstance(value, int):
            return value
        elif isinstance(value, float):
            if strict and value != int(value):
                raise ValueError(
                    f"Float {value} cannot be converted to int without loss"
                )
            return int(value)
        elif isinstance(value, str):
            if value.strip() == "":
                return 0
            return int(float(value))  # Handle strings like "123.0"
        elif isinstance(value, bool):
            return int(value)
        else:
            raise ValueError(f"Cannot convert {type(value).__name__} to int")

    def _to_float(self, value: Any, strict: bool = False) -> float:
        """Convert to float"""
        if isinstance(value, float):
            return value
        elif isinstance(value, (int, bool)):
            return float(value)
        elif isinstance(value, str):
            if value.strip() == "":
                return 0.0
            return float(value)
        else:
            raise ValueError(f"Cannot convert {type(value).__name__} to float")

    def _to_boolean(self, value: Any, strict: bool = False) -> bool:
        """Convert to boolean"""
        if isinstance(value, bool):
            return value
        elif isinstance(value, (int, float)):
            return bool(value)
        elif isinstance(value, str):
            value_lower = value.lower().strip()
            if value_lower in ("true", "1", "yes", "on", "y"):
                return True
            elif value_lower in ("false", "0", "no", "off", "n", ""):
                return False
            else:
                if strict:
                    raise ValueError(f"Cannot convert string '{value}' to bool")
                return bool(value)
        else:
            return bool(value)

    def _to_datetime(self, value: Any, strict: bool = False) -> datetime:
        """Convert to datetime"""
        if isinstance(value, datetime):
            return value
        elif isinstance(value, date):
            return datetime.combine(value, datetime.min.time())
        elif isinstance(value, str):
            # Try common datetime formats
            formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d %H:%M:%S.%f",
                "%Y-%m-%dT%H:%M:%S",
                "%Y-%m-%dT%H:%M:%S.%f",
                "%Y-%m-%dT%H:%M:%SZ",
                "%Y-%m-%d",
                "%d/%m/%Y",
                "%m/%d/%Y",
            ]

            for fmt in formats:
                try:
                    return datetime.strptime(value, fmt)
                except ValueError:
                    continue

            raise ValueError(f"Cannot parse datetime from string: {value}")
        else:
            raise ValueError(f"Cannot convert {type(value).__name__} to datetime")

    def _to_date(self, value: Any, strict: bool = False) -> date:
        """Convert to date"""
        if isinstance(value, date):
            return value
        elif isinstance(value, datetime):
            return value.date()
        elif isinstance(value, str):
            # Try to convert to datetime first, then extract date
            dt = self._to_datetime(value, strict)
            return dt.date()
        else:
            raise ValueError(f"Cannot convert {type(value).__name__} to date")

    def _to_decimal(self, value: Any, strict: bool = False) -> Decimal:
        """Convert to Decimal"""
        if isinstance(value, Decimal):
            return value
        elif isinstance(value, (int, float)):
            return Decimal(str(value))
        elif isinstance(value, str):
            if value.strip() == "":
                return Decimal("0")
            return Decimal(value)
        else:
            raise ValueError(f"Cannot convert {type(value).__name__} to Decimal")

    def _to_list(self, value: Any, strict: bool = False) -> list:
        """Convert to list"""
        if isinstance(value, list):
            return value
        elif isinstance(value, tuple):
            return list(value)
        elif isinstance(value, str):
            # Try to parse as JSON
            try:
                parsed = json.loads(value)
                if isinstance(parsed, list):
                    return parsed
                else:
                    return [parsed]
            except json.JSONDecodeError:
                # Split by comma
                return [item.strip() for item in value.split(",") if item.strip()]
        else:
            return [value]

    def _to_dict(self, value: Any, strict: bool = False) -> dict:
        """Convert to dict"""
        if isinstance(value, dict):
            return value
        elif isinstance(value, str):
            try:
                parsed = json.loads(value)
                if isinstance(parsed, dict):
                    return parsed
                else:
                    raise ValueError("Parsed JSON is not a dictionary")
            except json.JSONDecodeError as e:
                raise ValueError(f"Cannot parse JSON: {e}")
        else:
            raise ValueError(f"Cannot convert {type(value).__name__} to dict")


class ValueMapper:
    """Utility for value mapping transformations"""

    def __init__(self):
        self.mappings: Dict[str, Dict[Any, Any]] = {}
        self.default_values: Dict[str, Any] = {}
        self.logger = get_logger(__name__)

    def add_mapping(
        self, mapping_name: str, value_map: Dict[Any, Any], default_value: Any = None
    ):
        """Add a value mapping"""
        self.mappings[mapping_name] = value_map
        if default_value is not None:
            self.default_values[mapping_name] = default_value

    def map_value(self, value: Any, mapping_name: str) -> TransformationResult:
        """Map a value using specified mapping"""
        result = TransformationResult(
            original_value=value, transformation_type=TransformationType.VALUE_MAPPING
        )

        if mapping_name not in self.mappings:
            result.add_error(f"Mapping '{mapping_name}' not found")
            return result

        mapping = self.mappings[mapping_name]

        if value in mapping:
            result.transformed_value = mapping[value]
        elif mapping_name in self.default_values:
            result.transformed_value = self.default_values[mapping_name]
            result.add_warning(f"Used default value for unmapped value: {value}")
        else:
            result.transformed_value = value
            result.add_warning(f"No mapping found for value: {value}")

        return result


class FieldTransformer:
    """Transformer for individual fields"""

    def __init__(self, field_name: str):
        self.field_name = field_name
        self.type_converter = TypeConverter()
        self.value_mapper = ValueMapper()
        self.custom_transformers: List[Callable] = []
        self.logger = get_logger(__name__)

    def add_type_conversion(self, target_type: Type, strict: bool = False):
        """Add type conversion"""

        def converter(value, context=None):
            return self.type_converter.convert(value, target_type, strict)

        self.custom_transformers.append(converter)
        return self

    def add_value_mapping(self, value_map: Dict[Any, Any], default_value: Any = None):
        """Add value mapping"""
        mapping_name = f"{self.field_name}_mapping"
        self.value_mapper.add_mapping(mapping_name, value_map, default_value)

        def mapper(value, context=None):
            return self.value_mapper.map_value(value, mapping_name)

        self.custom_transformers.append(mapper)
        return self

    def add_custom_transformer(self, transformer: Callable):
        """Add custom transformer function"""
        self.custom_transformers.append(transformer)
        return self

    def transform(
        self, value: Any, context: Dict[str, Any] = None
    ) -> TransformationResult:
        """Transform field value"""
        current_value = value
        final_result = TransformationResult(original_value=value)

        for transformer in self.custom_transformers:
            try:
                result = transformer(current_value, context)
                if isinstance(result, TransformationResult):
                    if result.success:
                        current_value = result.transformed_value
                    final_result.errors.extend(result.errors)
                    final_result.warnings.extend(result.warnings)
                    if not result.success:
                        final_result.success = False
                        break
                else:
                    # Assume the result is the transformed value
                    current_value = result
            except Exception as e:
                final_result.add_error(f"Transformer error: {e}")
                break

        final_result.transformed_value = current_value
        return final_result


class DataTransformer:
    """Main data transformer"""

    def __init__(self):
        self.field_transformers: Dict[str, FieldTransformer] = {}
        self.model_transformers: Dict[str, Dict[str, FieldTransformer]] = {}
        self.global_transformers: List[Callable] = []
        self.logger = get_logger(__name__)

    def field(self, field_name: str) -> FieldTransformer:
        """Get or create field transformer"""
        if field_name not in self.field_transformers:
            self.field_transformers[field_name] = FieldTransformer(field_name)
        return self.field_transformers[field_name]

    def model_field(self, model_name: str, field_name: str) -> FieldTransformer:
        """Get or create model-specific field transformer"""
        if model_name not in self.model_transformers:
            self.model_transformers[model_name] = {}
        if field_name not in self.model_transformers[model_name]:
            self.model_transformers[model_name][field_name] = FieldTransformer(
                field_name
            )
        return self.model_transformers[model_name][field_name]

    def add_global_transformer(self, transformer: Callable):
        """Add global transformer"""
        self.global_transformers.append(transformer)
        return self

    def transform_data(
        self, data: List[Dict[str, Any]], context: Dict[str, Any] = None
    ) -> List[TransformationResult]:
        """Transform a list of data records"""
        results = []
        context = context or {}

        for i, record in enumerate(data):
            try:
                result = self.transform_record(record, context)
                results.append(result)
            except Exception as e:
                error_result = TransformationResult(original_value=record)
                error_result.add_error(f"Error transforming record {i}: {e}")
                results.append(error_result)

        return results

    def transform_record(
        self, record: Dict[str, Any], context: Dict[str, Any] = None
    ) -> TransformationResult:
        """Transform a single record"""
        result = TransformationResult(original_value=record)
        context = context or {}

        try:
            transformed_record = record.copy()
            model_name = record.get("model")
            values = record.get("values", {})
            transformed_values = {}

            # Transform field values
            for field_name, field_value in values.items():
                field_result = self._transform_field_value(
                    field_name, field_value, model_name, context
                )

                if field_result.success:
                    transformed_values[field_name] = field_result.transformed_value
                else:
                    transformed_values[field_name] = field_value

                result.errors.extend(field_result.errors)
                result.warnings.extend(field_result.warnings)
                if not field_result.success:
                    result.success = False

            transformed_record["values"] = transformed_values

            # Apply global transformers
            for transformer in self.global_transformers:
                try:
                    global_result = transformer(transformed_record, context)
                    if isinstance(global_result, TransformationResult):
                        if global_result.success:
                            transformed_record = global_result.transformed_value
                        result.errors.extend(global_result.errors)
                        result.warnings.extend(global_result.warnings)
                        if not global_result.success:
                            result.success = False
                    else:
                        transformed_record = global_result
                except Exception as e:
                    result.add_error(f"Global transformer error: {e}")

            result.transformed_value = transformed_record

        except Exception as e:
            result.add_error(f"Error transforming record: {e}")

        return result

    def _transform_field_value(
        self,
        field_name: str,
        field_value: Any,
        model_name: str,
        context: Dict[str, Any],
    ) -> TransformationResult:
        """Transform a single field value"""
        # Check for model-specific transformer first
        if model_name and model_name in self.model_transformers:
            if field_name in self.model_transformers[model_name]:
                return self.model_transformers[model_name][field_name].transform(
                    field_value, context
                )

        # Check for global field transformer
        if field_name in self.field_transformers:
            return self.field_transformers[field_name].transform(field_value, context)

        # No transformation needed
        return TransformationResult(
            original_value=field_value, transformed_value=field_value
        )
