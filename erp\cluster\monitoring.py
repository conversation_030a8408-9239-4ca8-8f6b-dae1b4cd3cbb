"""
Cluster monitoring functionality
"""

from typing import Any, Dict

from ..logging import get_logger


class ClusterMonitoring:
    """Handles cluster-specific monitoring"""

    def __init__(self):
        self.logger = get_logger(__name__)
        self.cluster_info = {}

    def setup_cluster_monitoring(self) -> Dict[str, Any]:
        """Setup cluster-specific monitoring"""
        try:
            from ..logging.monitoring import get_memory_usage

            # Get initial memory usage for cluster monitoring
            memory_info = get_memory_usage()

            monitoring_info = {
                "initial_memory": memory_info,
                "monitoring_enabled": True,
            }

            self.cluster_info.update(monitoring_info)
            self.logger.info("📊 Cluster memory monitoring enabled")

            return monitoring_info

        except Exception as e:
            self.logger.warning(f"Failed to setup cluster monitoring: {e}")
            return {}

    def get_cluster_memory_details(
        self, cluster_id: str, node_id: str
    ) -> Dict[str, Any]:
        """Get detailed cluster memory information"""
        try:
            from ..logging.monitoring import get_memory_usage

            current_memory = get_memory_usage()
            initial_memory = self.cluster_info.get("initial_memory", {})

            memory_details = {
                "node_id": node_id,
                "cluster_id": cluster_id,
                "current_memory": current_memory,
                "initial_memory": initial_memory,
                "memory_delta": {},
            }

            # Calculate memory delta if initial memory is available
            if initial_memory:
                memory_details["memory_delta"] = {
                    "process_rss_mb": current_memory.get("process_rss_mb", 0)
                    - initial_memory.get("process_rss_mb", 0),
                    "system_used_mb": current_memory.get("system_used_mb", 0)
                    - initial_memory.get("system_used_mb", 0),
                    "system_percent": current_memory.get("system_percent", 0)
                    - initial_memory.get("system_percent", 0),
                }

            return memory_details

        except Exception as e:
            self.logger.error(f"Failed to get cluster memory details: {e}")
            return {"node_id": node_id, "cluster_id": cluster_id, "error": str(e)}

    def log_cluster_shutdown_info(self, cluster_id: str, node_id: str):
        """Log cluster-specific information during shutdown"""
        self.logger.info("🔗 Cluster node shutting down...")

        # Get and log cluster memory details
        memory_details = self.get_cluster_memory_details(cluster_id, node_id)

        if "error" not in memory_details:
            current_mem = memory_details.get("current_memory", {})
            initial_mem = memory_details.get("initial_memory", {})
            delta_mem = memory_details.get("memory_delta", {})

            self.logger.info(
                f"🏷️  Cluster Node: {memory_details['node_id']} "
                f"(Cluster: {memory_details['cluster_id'][:8]}...)"
            )

            self.logger.info(
                f"📊 Node Memory Usage: "
                f"Process RSS: {current_mem.get('process_rss_mb', 0):.2f} MB, "
                f"System: {current_mem.get('system_used_mb', 0):.2f} MB "
                f"({current_mem.get('system_percent', 0):.1f}%)"
            )

            if delta_mem:
                self.logger.info(
                    f"📈 Memory Delta (since startup): "
                    f"Process RSS: {delta_mem.get('process_rss_mb', 0):+.2f} MB, "
                    f"System: {delta_mem.get('system_used_mb', 0):+.2f} MB "
                    f"({delta_mem.get('system_percent', 0):+.1f}%)"
                )
        else:
            self.logger.error(
                f"❌ Failed to get cluster memory details: {memory_details['error']}"
            )

        self.logger.info(f"🔗 Cluster node {node_id} shutdown complete")
