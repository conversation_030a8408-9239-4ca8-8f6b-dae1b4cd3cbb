"""
Controller registry
Manages registration and discovery of controller classes
"""

from collections import defaultdict
from typing import Any, Dict, List, Optional, Type

from ...logging import get_logger
from .base import BaseController

logger = get_logger(__name__)


class ControllerRegistry:
    """Registry for managing controller classes"""

    def __init__(self):
        self._controllers: Dict[str, Type[BaseController]] = {}
        self._controllers_by_module: Dict[str, List[str]] = defaultdict(list)
        self._controller_metadata: Dict[str, Dict[str, Any]] = {}

    def register_controller(
        self,
        name: str,
        controller_class: Type[BaseController],
        module: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Register a controller class

        Args:
            name: Controller name
            controller_class: Controller class
            module: Module name (optional)
            metadata: Additional metadata (optional)
        """
        if not issubclass(controller_class, BaseController):
            raise ValueError(f"Controller {name} must inherit from BaseController")

        self._controllers[name] = controller_class

        # Track by module
        if module:
            self._controllers_by_module[module].append(name)
        else:
            module = getattr(controller_class, "__module__", "unknown")
            self._controllers_by_module[module].append(name)

        # Store metadata
        if metadata:
            self._controller_metadata[name] = metadata

        logger.debug(f"Registered controller: {name} from module {module}")

    def unregister_controller(self, name: str) -> bool:
        """
        Unregister a controller class

        Args:
            name: Controller name

        Returns:
            True if controller was unregistered, False if not found
        """
        if name not in self._controllers:
            return False

        # Remove from main registry
        controller_class = self._controllers[name]
        del self._controllers[name]

        # Remove from module tracking
        module = getattr(controller_class, "__module__", "unknown")
        if module in self._controllers_by_module:
            try:
                self._controllers_by_module[module].remove(name)
                if not self._controllers_by_module[module]:
                    del self._controllers_by_module[module]
            except ValueError:
                pass

        # Remove metadata
        self._controller_metadata.pop(name, None)

        logger.debug(f"Unregistered controller: {name}")
        return True

    def get_controller(self, name: str) -> Optional[Type[BaseController]]:
        """
        Get a controller class by name

        Args:
            name: Controller name

        Returns:
            Controller class or None if not found
        """
        return self._controllers.get(name)

    def get_all_controllers(self) -> Dict[str, Type[BaseController]]:
        """Get all registered controllers"""
        return self._controllers.copy()

    def get_controllers_by_module(self, module: str) -> List[str]:
        """
        Get controller names by module

        Args:
            module: Module name

        Returns:
            List of controller names in the module
        """
        return self._controllers_by_module.get(module, []).copy()

    def get_controller_metadata(self, name: str) -> Dict[str, Any]:
        """
        Get metadata for a controller

        Args:
            name: Controller name

        Returns:
            Controller metadata or empty dict if not found
        """
        return self._controller_metadata.get(name, {}).copy()

    def list_controllers(self) -> List[str]:
        """Get list of all controller names"""
        return list(self._controllers.keys())

    def list_modules(self) -> List[str]:
        """Get list of all modules with controllers"""
        return list(self._controllers_by_module.keys())

    def create_controller(self, name: str, *args, **kwargs) -> Optional[BaseController]:
        """
        Create an instance of a controller

        Args:
            name: Controller name
            *args: Arguments to pass to controller constructor
            **kwargs: Keyword arguments to pass to controller constructor

        Returns:
            Controller instance or None if not found
        """
        controller_class = self.get_controller(name)
        if not controller_class:
            return None

        try:
            return controller_class(*args, **kwargs)
        except Exception as e:
            logger.error(f"Error creating controller {name}: {e}")
            return None

    def clear(self) -> None:
        """Clear all registered controllers"""
        self._controllers.clear()
        self._controllers_by_module.clear()
        self._controller_metadata.clear()
        logger.debug("Cleared all controllers from registry")

    def get_stats(self) -> Dict[str, int]:
        """Get registry statistics"""
        return {
            "total_controllers": len(self._controllers),
            "total_modules": len(self._controllers_by_module),
        }


class ControllerDiscovery:
    """Discovers and auto-registers controllers"""

    def __init__(self, registry: ControllerRegistry):
        self.registry = registry
        self.logger = get_logger(__name__)

    def discover_controllers_in_module(self, module_name: str) -> List[str]:
        """
        Discover controllers in a module

        Args:
            module_name: Module name to scan

        Returns:
            List of discovered controller names
        """
        try:
            import importlib
            import inspect

            module = importlib.import_module(module_name)
            discovered = []

            for name, obj in inspect.getmembers(module, inspect.isclass):
                # Check if it's a controller class
                if (
                    issubclass(obj, BaseController)
                    and obj != BaseController
                    and obj.__module__ == module_name
                ):

                    # Auto-register the controller
                    controller_name = name.lower().replace("controller", "")
                    if not controller_name:
                        controller_name = name.lower()

                    self.registry.register_controller(
                        controller_name, obj, module_name, {"auto_discovered": True}
                    )
                    discovered.append(controller_name)

            self.logger.debug(
                f"Discovered {len(discovered)} controllers in {module_name}"
            )
            return discovered

        except Exception as e:
            self.logger.error(f"Error discovering controllers in {module_name}: {e}")
            return []

    def discover_controllers_in_package(self, package_name: str) -> List[str]:
        """
        Discover controllers in a package

        Args:
            package_name: Package name to scan

        Returns:
            List of discovered controller names
        """
        try:
            import importlib
            import pkgutil

            package = importlib.import_module(package_name)
            discovered = []

            for importer, modname, ispkg in pkgutil.iter_modules(
                package.__path__, package_name + "."
            ):
                if not ispkg:  # Only scan modules, not sub-packages
                    module_controllers = self.discover_controllers_in_module(modname)
                    discovered.extend(module_controllers)

            self.logger.debug(
                f"Discovered {len(discovered)} controllers in package {package_name}"
            )
            return discovered

        except Exception as e:
            self.logger.error(
                f"Error discovering controllers in package {package_name}: {e}"
            )
            return []


# Global controller registry
_controller_registry = ControllerRegistry()


def get_controller_registry() -> ControllerRegistry:
    """Get the global controller registry"""
    return _controller_registry


def register_controller(
    name: str, controller_class: Type[BaseController], **kwargs
) -> None:
    """Register a controller with the global registry"""
    _controller_registry.register_controller(name, controller_class, **kwargs)


def get_controller(name: str) -> Optional[Type[BaseController]]:
    """Get a controller from the global registry"""
    return _controller_registry.get_controller(name)
