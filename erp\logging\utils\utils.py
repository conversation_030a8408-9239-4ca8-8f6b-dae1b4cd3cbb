"""
Logging Utilities - Helper functions and decorators for logging
"""

import asyncio
import functools
import json
import logging
import time
from contextlib import asynccontextmanager, contextmanager
from typing import Any, Callable, Optional


class LogContext:
    """Context manager for adding context to log records"""

    def __init__(self, logger: logging.Logger, **context):
        self.logger = logger
        self.context = context
        self.old_factory = None

    def __enter__(self):
        """Enter context and set up log record factory"""
        self.old_factory = logging.getLogRecordFactory()

        def record_factory(*args, **kwargs):
            record = self.old_factory(*args, **kwargs)
            for key, value in self.context.items():
                setattr(record, key, value)
            return record

        logging.setLogRecordFactory(record_factory)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context and restore log record factory"""
        if self.old_factory:
            logging.setLogRecordFactory(self.old_factory)


@contextmanager
def log_context(**context):
    """Context manager for adding context to all log records"""
    old_factory = logging.getLogRecordFactory()

    def record_factory(*args, **kwargs):
        record = old_factory(*args, **kwargs)
        for key, value in context.items():
            if not hasattr(record, key):
                setattr(record, key, value)
        return record

    logging.setLogRecordFactory(record_factory)
    try:
        yield
    finally:
        logging.setLogRecordFactory(old_factory)


def log_performance(
    logger: Optional[logging.Logger] = None,
    level: int = logging.INFO,
    message: Optional[str] = None,
    include_args: bool = False,
):
    """Decorator for logging function performance"""

    def decorator(func: Callable) -> Callable:
        nonlocal logger
        if logger is None:
            logger = logging.getLogger(func.__module__)

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            func_name = func.__name__

            try:
                result = func(*args, **kwargs)
                duration = time.perf_counter() - start_time

                log_message = message or f"Function {func_name} completed"
                if include_args:
                    log_message += f" with args={args}, kwargs={kwargs}"

                logger.log(
                    level,
                    log_message,
                    extra={
                        "duration": duration,
                        "function": func_name,
                        "operation": "function_call",
                    },
                )

                return result

            except Exception as e:
                duration = time.perf_counter() - start_time
                logger.error(
                    f"Function {func_name} failed after {duration:.3f}s: {e}",
                    extra={"duration": duration, "function": func_name},
                )
                raise

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            func_name = func.__name__

            try:
                result = await func(*args, **kwargs)
                duration = time.perf_counter() - start_time

                log_message = message or f"Async function {func_name} completed"
                if include_args:
                    log_message += f" with args={args}, kwargs={kwargs}"

                logger.log(
                    level,
                    log_message,
                    extra={
                        "duration": duration,
                        "function": func_name,
                        "operation": "async_function_call",
                    },
                )

                return result

            except Exception as e:
                duration = time.perf_counter() - start_time
                logger.error(
                    f"Async function {func_name} failed after {duration:.3f}s: {e}",
                    extra={"duration": duration, "function": func_name},
                )
                raise

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


def log_structured(logger: logging.Logger, level: int, message: str, **structured_data):
    """Log a structured message with additional data"""
    logger.log(level, message, extra=structured_data)


def log_exception(
    logger: logging.Logger,
    message: str = "An exception occurred",
    level: int = logging.ERROR,
    **context,
):
    """Log an exception with context"""
    logger.log(level, message, exc_info=True, extra=context)


class PerformanceTimer:
    """Context manager for timing operations"""

    def __init__(
        self,
        logger: logging.Logger,
        operation: str,
        level: int = logging.INFO,
        threshold: Optional[float] = None,
    ):
        self.logger = logger
        self.operation = operation
        self.level = level
        self.threshold = threshold
        self.start_time = None
        self.duration = None

    def __enter__(self):
        self.start_time = time.perf_counter()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.duration = time.perf_counter() - self.start_time

        # Only log if above threshold (if set)
        if self.threshold is None or self.duration >= self.threshold:
            if exc_type is None:
                self.logger.log(
                    self.level,
                    f"{self.operation} completed in {self.duration:.3f}s",
                    extra={"duration": self.duration, "operation": self.operation},
                )
            else:
                self.logger.error(
                    f"{self.operation} failed after {self.duration:.3f}s: {exc_val}",
                    extra={"duration": self.duration, "operation": self.operation},
                )


@asynccontextmanager
async def async_performance_timer(
    logger: logging.Logger,
    operation: str,
    level: int = logging.INFO,
    threshold: Optional[float] = None,
):
    """Async context manager for timing operations"""
    start_time = time.perf_counter()

    try:
        yield
        duration = time.perf_counter() - start_time

        # Only log if above threshold (if set)
        if threshold is None or duration >= threshold:
            logger.log(
                level,
                f"{operation} completed in {duration:.3f}s",
                extra={"duration": duration, "operation": operation},
            )

    except Exception as e:
        duration = time.perf_counter() - start_time
        logger.error(
            f"{operation} failed after {duration:.3f}s: {e}",
            extra={"duration": duration, "operation": operation},
        )
        raise


class LoggingMixin:
    """Mixin class to add logging capabilities to any class"""

    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class"""
        if not hasattr(self, "_logger"):
            self._logger = logging.getLogger(
                f"{self.__class__.__module__}.{self.__class__.__name__}"
            )
        return self._logger

    def log_info(self, message: str, **context):
        """Log info message with context"""
        self.logger.info(message, extra=context)

    def log_warning(self, message: str, **context):
        """Log warning message with context"""
        self.logger.warning(message, extra=context)

    def log_error(self, message: str, **context):
        """Log error message with context"""
        self.logger.error(message, extra=context)

    def log_debug(self, message: str, **context):
        """Log debug message with context"""
        self.logger.debug(message, extra=context)

    def log_exception(self, message: str = "An exception occurred", **context):
        """Log exception with context"""
        self.logger.error(message, exc_info=True, extra=context)


def setup_request_logging(logger: logging.Logger):
    """Setup request-specific logging context"""

    def log_request(request_id: str, method: str, url: str, **context):
        """Log request start"""
        with log_context(request_id=request_id, **context):
            logger.info(f"Request started: {method} {url}")

    def log_response(request_id: str, status_code: int, duration: float, **context):
        """Log request completion"""
        with log_context(request_id=request_id, **context):
            logger.info(f"Request completed: {status_code} ({duration:.3f}s)")

    return log_request, log_response


def create_audit_logger(name: str = "audit") -> logging.Logger:
    """Create a specialized audit logger"""
    logger = logging.getLogger(name)

    # Add audit-specific formatting
    handler = logging.StreamHandler()
    formatter = logging.Formatter(
        "%(asctime)s [AUDIT] %(levelname)s: %(message)s | User: %(user_id)s | Action: %(action)s"
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

    return logger


def log_database_query(
    logger: logging.Logger,
    query: str,
    params: Optional[tuple] = None,
    duration: Optional[float] = None,
    **context,
):
    """Log database query with context"""
    # Truncate long queries for readability
    display_query = query[:200] + "..." if len(query) > 200 else query

    extra_context = {
        "query": query,
        "query_display": display_query,
        "query_params": params,
        **context,
    }

    if duration is not None:
        extra_context["query_duration"] = duration
        logger.debug(
            f"Database query executed in {duration:.3f}s: {display_query}",
            extra=extra_context,
        )
    else:
        logger.debug(f"Database query: {display_query}", extra=extra_context)


def log_security_event(
    logger: logging.Logger,
    event_type: str,
    user_id: str,
    client_ip: str,
    message: str,
    **context,
):
    """Log security event"""
    logger.warning(
        f"Security event: {message}",
        extra={
            "security_event": event_type,
            "user_id": user_id,
            "client_ip": client_ip,
            **context,
        },
    )


def format_log_data(data: Any) -> str:
    """Format data for logging (handles complex objects)"""
    try:
        if isinstance(data, (dict, list)):
            return json.dumps(data, default=str, indent=2)
        else:
            return str(data)
    except Exception:
        return repr(data)


class LogBuffer:
    """Buffer for collecting and batch-processing log records"""

    def __init__(self, max_size: int = 100, flush_interval: float = 30.0):
        self.max_size = max_size
        self.flush_interval = flush_interval
        self.buffer = []
        self.last_flush = time.time()

    def add(self, record: logging.LogRecord):
        """Add record to buffer"""
        self.buffer.append(record)

        # Auto-flush if needed
        if (
            len(self.buffer) >= self.max_size
            or time.time() - self.last_flush >= self.flush_interval
        ):
            self.flush()

    def flush(self, handler: Optional[logging.Handler] = None):
        """Flush buffer to handler"""
        if not self.buffer:
            return

        if handler:
            for record in self.buffer:
                handler.emit(record)

        self.buffer.clear()
        self.last_flush = time.time()
