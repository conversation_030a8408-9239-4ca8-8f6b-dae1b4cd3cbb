"""
Comprehensive tests for fail-fast error handling in addon installation

This test suite verifies that addon installation fails properly when any component
(hooks, XML loading, IR population) encounters errors, ensuring fail-fast behavior
works correctly throughout the entire installation pipeline.
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch

# Configure pytest-asyncio
pytest_asyncio.auto_mode = True

from erp.addons.hooks import HookType
from erp.addons.hooks.hook_registry import get_hook_registry
from erp.addons.hooks.hook_context import Hook<PERSON>ontext
from erp.addons.installers.addon_installer import <PERSON>donInstaller
from erp.addons.managers.addon_manager import AddonManager
from erp.addons.managers.savepoint_manager import SavepointManager
from erp.environment import Environment


class TestFailFastHookExecution:
    """Test fail-fast behavior in hook execution"""

    @pytest.fixture
    def mock_env(self):
        """Create mock environment"""
        env = MagicMock(spec=Environment)
        env.cr = MagicMock()
        env.cr._db_manager = MagicMock()
        return env

    @pytest.fixture
    def hook_registry(self):
        """Get clean hook registry for testing"""
        registry = get_hook_registry()
        # Clear any existing hooks
        for hook_type in HookType:
            registry._hooks[hook_type] = []
        return registry

    @pytest.mark.asyncio
    async def test_hook_returning_false_fails_fast(self, hook_registry, mock_env):
        """Test that hook returning False causes immediate failure"""
        
        # Register a hook that returns False
        @hook_registry.register_hook
        async def failing_hook(context: HookContext):
            return False
            
        hook_registry.register_hook(
            failing_hook, HookType.POST_INSTALL, "test_addon", priority=50
        )

        # Execute hooks with fail_fast=True (default)
        with pytest.raises(RuntimeError) as exc_info:
            await hook_registry.execute_hooks(
                HookType.POST_INSTALL, "test_addon", mock_env
            )
        
        assert "returned False" in str(exc_info.value)
        assert "test_addon" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_hook_exception_fails_fast(self, hook_registry, mock_env):
        """Test that hook raising exception causes immediate failure"""

        # Register a hook that raises an exception
        @hook_registry.register_hook
        async def exception_hook(context: HookContext):
            raise ValueError("Test hook failure")

        hook_registry.register_hook(
            exception_hook, HookType.POST_INSTALL, "test_addon", priority=50
        )

        # Execute hooks with fail_fast=True (default)
        with pytest.raises(RuntimeError) as exc_info:
            await hook_registry.execute_hooks(
                HookType.POST_INSTALL, "test_addon", mock_env
            )

        assert "Test hook failure" in str(exc_info.value)
        assert "test_addon" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_multiple_hooks_stop_on_first_failure(self, hook_registry, mock_env):
        """Test that execution stops on first hook failure"""
        
        execution_order = []
        
        # Register multiple hooks
        async def first_hook(context: HookContext):
            execution_order.append("first")
            return True
            
        async def failing_hook(context: HookContext):
            execution_order.append("failing")
            return False
            
        async def third_hook(context: HookContext):
            execution_order.append("third")
            return True
            
        hook_registry.register_hook(
            first_hook, HookType.POST_INSTALL, "test_addon", priority=10
        )
        hook_registry.register_hook(
            failing_hook, HookType.POST_INSTALL, "test_addon", priority=20
        )
        hook_registry.register_hook(
            third_hook, HookType.POST_INSTALL, "test_addon", priority=30
        )

        # Execute hooks - should stop at failing hook
        with pytest.raises(RuntimeError):
            await hook_registry.execute_hooks(
                HookType.POST_INSTALL, "test_addon", mock_env
            )
        
        # Verify only first two hooks executed
        assert execution_order == ["first", "failing"]

    @pytest.mark.asyncio
    async def test_non_fail_fast_mode_continues_on_failure(self, hook_registry, mock_env):
        """Test that fail_fast=False continues execution after failures"""
        
        execution_order = []
        
        # Register multiple hooks with one failing
        async def first_hook(context: HookContext):
            execution_order.append("first")
            return True
            
        async def failing_hook(context: HookContext):
            execution_order.append("failing")
            return False
            
        async def third_hook(context: HookContext):
            execution_order.append("third")
            return True
            
        hook_registry.register_hook(
            first_hook, HookType.POST_INSTALL, "test_addon", priority=10
        )
        hook_registry.register_hook(
            failing_hook, HookType.POST_INSTALL, "test_addon", priority=20
        )
        hook_registry.register_hook(
            third_hook, HookType.POST_INSTALL, "test_addon", priority=30
        )

        # Execute hooks with fail_fast=False
        results = await hook_registry.execute_hooks(
            HookType.POST_INSTALL, "test_addon", mock_env, fail_fast=False
        )
        
        # Verify all hooks executed
        assert execution_order == ["first", "failing", "third"]
        assert len(results) == 3
        assert results[0] is True
        assert results[1] is False
        assert results[2] is True


class TestAddonInstallerFailFast:
    """Test fail-fast behavior in addon installer"""

    @pytest.fixture
    def mock_env(self):
        """Create mock environment"""
        env = MagicMock(spec=Environment)
        env.cr = MagicMock()
        env.cr._db_manager = MagicMock()
        return env

    @pytest.fixture
    def addon_installer(self):
        """Create addon installer instance"""
        return AddonInstaller()

    @pytest.mark.asyncio
    async def test_installer_fails_on_hook_failure(self, addon_installer, mock_env):
        """Test that installer returns False when hooks fail"""

        with patch('erp.addons.installers.addon_installer.get_hook_registry') as mock_registry:
            # Mock hook registry to raise RuntimeError
            mock_registry.return_value.execute_hooks.side_effect = RuntimeError("Hook failed")

            # Mock utilities
            with patch.object(addon_installer, 'utilities') as mock_utilities:
                mock_utilities.update_registry_after_action = AsyncMock()

                # Install should return False
                result = await addon_installer.install_addon("test_addon", mock_env)
                assert result is False

    @pytest.mark.asyncio
    async def test_installer_fails_on_unexpected_exception(self, addon_installer, mock_env):
        """Test that installer returns False on unexpected exceptions"""
        
        with patch('erp.addons.installers.addon_installer.get_hook_registry') as mock_registry:
            # Mock hook registry to raise unexpected exception
            mock_registry.return_value.execute_hooks.side_effect = ValueError("Unexpected error")
            
            # Install should return False
            result = await addon_installer.install_addon("test_addon", mock_env)
            assert result is False


class TestSavepointManagerFailFast:
    """Test fail-fast behavior in savepoint manager"""

    @pytest.fixture
    def mock_env(self):
        """Create mock environment"""
        env = MagicMock(spec=Environment)
        env.cr = MagicMock()
        env.cr.savepoint = AsyncMock()
        env.cr.rollback_to_savepoint = AsyncMock()
        env.cr.release_savepoint = AsyncMock()
        return env

    @pytest.fixture
    def savepoint_manager(self):
        """Create savepoint manager instance"""
        return SavepointManager()

    @pytest.mark.asyncio
    async def test_savepoint_rollback_on_installation_failure(self, savepoint_manager, mock_env):
        """Test that savepoint is rolled back when installation function returns False"""

        # Mock installation function that returns False
        async def failing_installation():
            return False

        # Execute with savepoint
        result = await savepoint_manager.execute_with_savepoint(
            "test_addon", mock_env, failing_installation, is_dependency=False
        )

        # Should return False and rollback savepoint
        assert result is False
        mock_env.cr.rollback_to_savepoint.assert_called_once()

    @pytest.mark.asyncio
    async def test_savepoint_rollback_on_installation_exception(self, savepoint_manager, mock_env):
        """Test that savepoint is rolled back when installation function raises exception"""

        # Mock installation function that raises exception
        async def exception_installation():
            raise ValueError("Installation failed")

        # Execute with savepoint
        result = await savepoint_manager.execute_with_savepoint(
            "test_addon", mock_env, exception_installation, is_dependency=False
        )

        # Should return False and rollback savepoint
        assert result is False
        mock_env.cr.rollback_to_savepoint.assert_called_once()


class TestEndToEndFailFast:
    """Test end-to-end fail-fast behavior"""

    @pytest.fixture
    def mock_env(self):
        """Create mock environment"""
        env = MagicMock(spec=Environment)
        env.cr = MagicMock()
        env.cr._db_manager = MagicMock()
        env.cr.savepoint = AsyncMock()
        env.cr.rollback_to_savepoint = AsyncMock()
        env.cr.release_savepoint = AsyncMock()
        return env

    @pytest.mark.asyncio
    async def test_xml_loading_failure_propagates_to_installation_failure(self, mock_env):
        """Test that XML loading failures cause complete installation failure"""
        
        # Mock the core XML data loading hook to return False
        with patch('erp.addons.core.core_hooks.XMLDataLoader') as mock_xml_loader:
            mock_xml_loader.return_value.load_addon_data_files.return_value = {
                "success": False,
                "error": "XML loading failed",
                "total_loaded": 0,
                "files_processed": 0,
            }
            
            # Mock addon manager components
            with patch('erp.addons.managers.addon_manager.AddonManager') as mock_manager_class:
                mock_manager = mock_manager_class.return_value
                mock_manager.installer = MagicMock()
                mock_manager.installer.install_addon = AsyncMock(return_value=False)
                mock_manager.savepoint_manager = MagicMock()
                mock_manager.savepoint_manager.execute_with_savepoint = AsyncMock(return_value=False)
                
                # Installation should fail
                result = await mock_manager.savepoint_manager.execute_with_savepoint(
                    "test_addon", mock_env, mock_manager.installer.install_addon, False
                )
                
                assert result is False
