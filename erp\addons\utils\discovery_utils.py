"""
Addon discovery utilities

This module provides shared utility functions for discovering and loading
addon manifests. These functions are extracted to eliminate code duplication
and provide a centralized location for addon discovery operations.
"""

import os
from typing import Dict, List, Set

from ...logging import get_logger
from ..manifest import AddonManifest
from .constants import FileNames

logger = get_logger(__name__)


def discover_addons_in_path(addons_path: str) -> Dict[str, AddonManifest]:
    """
    Discover all addons in a given path

    Args:
        addons_path: Path to search for addons

    Returns:
        Dictionary mapping addon names to their manifests
    """
    discovered = {}

    if not os.path.exists(addons_path):
        logger.warning(f"Addons path does not exist: {addons_path}")
        return discovered

    try:
        for item in os.listdir(addons_path):
            item_path = os.path.join(addons_path, item)

            # Skip files, only process directories
            if not os.path.isdir(item_path):
                continue

            # Skip hidden directories
            if item.startswith("."):
                continue

            # Check if it's a valid addon (has __manifest__.py)
            manifest_file = os.path.join(item_path, FileNames.MANIFEST)
            if os.path.exists(manifest_file):
                try:
                    manifest = AddonManifest(item_path)
                    discovered[item] = manifest
                    logger.debug(f"Discovered addon: {item}")
                except Exception as e:
                    logger.warning(f"Failed to load manifest for {item}: {e}")

    except Exception as e:
        logger.error(f"Failed to discover addons in {addons_path}: {e}")

    return discovered


def discover_addons_in_multiple_paths(
    addons_paths: List[str],
) -> Dict[str, AddonManifest]:
    """
    Discover all addons in multiple paths

    Args:
        addons_paths: List of paths to search for addons

    Returns:
        Dictionary mapping addon names to their manifests
    """
    all_discovered = {}

    for path in addons_paths:
        discovered = discover_addons_in_path(path)

        # Check for conflicts (same addon name in multiple paths)
        for addon_name, manifest in discovered.items():
            if addon_name in all_discovered:
                existing_path = all_discovered[addon_name].addon_path
                logger.warning(
                    f"Addon '{addon_name}' found in multiple paths: "
                    f"{existing_path} and {manifest.addon_path}. "
                    f"Using the first one found."
                )
            else:
                all_discovered[addon_name] = manifest

    logger.info(
        f"Discovered {len(all_discovered)} addons across {len(addons_paths)} paths"
    )
    return all_discovered


def validate_addon_structure(addon_path: str) -> List[str]:
    """
    Validate basic addon directory structure

    Args:
        addon_path: Path to the addon directory

    Returns:
        List of validation errors (empty if valid)
    """
    errors = []

    if not os.path.exists(addon_path):
        errors.append(f"Addon directory does not exist: {addon_path}")
        return errors

    if not os.path.isdir(addon_path):
        errors.append(f"Addon path is not a directory: {addon_path}")
        return errors

    # Check for required files
    manifest_file = os.path.join(addon_path, FileNames.MANIFEST)
    if not os.path.exists(manifest_file):
        errors.append(f"Missing required file: {FileNames.MANIFEST}")

    init_file = os.path.join(addon_path, FileNames.INIT)
    if not os.path.exists(init_file):
        errors.append(f"Missing required file: {FileNames.INIT}")

    return errors


def get_addon_dependencies(addon_manifest: AddonManifest) -> Set[str]:
    """
    Get all dependencies for an addon (including transitive dependencies)

    Args:
        addon_manifest: The addon manifest

    Returns:
        Set of dependency addon names
    """
    return set(addon_manifest.depends)


def build_dependency_graph(addons: Dict[str, AddonManifest]) -> Dict[str, Set[str]]:
    """
    Build a dependency graph for a set of addons

    Args:
        addons: Dictionary mapping addon names to their manifests

    Returns:
        Dictionary mapping addon names to their direct dependencies
    """
    dependency_graph = {}

    for addon_name, manifest in addons.items():
        dependencies = get_addon_dependencies(manifest)
        dependency_graph[addon_name] = dependencies

    return dependency_graph


def get_installation_order(
    addons: List[str], dependency_graph: Dict[str, Set[str]]
) -> List[str]:
    """
    Get the correct installation order for addons based on dependencies

    Args:
        addons: List of addon names to install
        dependency_graph: Dictionary mapping addon names to their dependencies

    Returns:
        List of addon names in installation order

    Raises:
        ValueError: If circular dependencies are detected
    """
    # Check for circular dependencies using dependency analyzer
    from ...data.dependency_analyzer import DependencyAnalyzer

    analyzer = DependencyAnalyzer()
    for addon, deps in dependency_graph.items():
        analyzer.add_node(addon)
        for dep in deps:
            analyzer.add_dependency(addon, dep)

    cycles = analyzer.detect_circular_dependencies()
    if cycles:
        raise ValueError(f"Circular dependencies detected: {cycles}")

    # Topological sort
    visited = set()
    temp_visited = set()
    result = []

    def visit(node: str):
        if node in temp_visited:
            raise ValueError(f"Circular dependency detected involving {node}")
        if node in visited:
            return

        temp_visited.add(node)

        # Visit dependencies first
        for dep in dependency_graph.get(node, set()):
            if dep in addons:  # Only consider dependencies that are in our install list
                visit(dep)

        temp_visited.remove(node)
        visited.add(node)
        result.append(node)

    # Visit all addons
    for addon in addons:
        if addon not in visited:
            visit(addon)

    return result


def find_missing_dependencies(
    addons: List[str], available_addons: Set[str]
) -> Set[str]:
    """
    Find missing dependencies for a list of addons

    Args:
        addons: List of addon names to check
        available_addons: Set of available addon names

    Returns:
        Set of missing dependency names
    """
    missing = set()

    for addon_name in addons:
        if addon_name not in available_addons:
            missing.add(addon_name)

    return missing
