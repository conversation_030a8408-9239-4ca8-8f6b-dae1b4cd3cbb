"""
Addon-specific exceptions

This module contains all exception classes related to addon operations,
including dependency management, installation, and lifecycle management.
"""


class AddonError(Exception):
    """Base exception for all addon-related errors"""

    pass


class DependencyError(AddonError):
    """Exception raised for dependency-related errors"""

    pass


class CircularDependencyError(DependencyError):
    """Exception raised for circular dependencies"""

    pass


class MissingDependencyError(DependencyError):
    """Exception raised for missing dependencies"""

    pass


class AddonInstallationError(AddonError):
    """Exception raised during addon installation"""

    pass


class AddonUninstallationError(AddonError):
    """Exception raised during addon uninstallation"""

    pass


class AddonUpgradeError(AddonError):
    """Exception raised during addon upgrade"""

    pass


class AddonManifestError(AddonError):
    """Exception raised for manifest-related errors"""

    pass


class AddonNotFoundError(AddonError):
    """Exception raised when an addon cannot be found"""

    pass


class AddonStateError(AddonError):
    """Exception raised for invalid addon state transitions"""

    pass


class AddonRegistryError(AddonError):
    """Exception raised for addon registry operations"""

    pass


class AddonHookError(AddonError):
    """Exception raised during hook execution"""

    pass


class AddonLifecycleError(AddonError):
    """Exception raised during addon lifecycle operations"""

    pass
