"""
Utilities package for ERP system
Contains various utility functions and classes organized in modular subdirectories
"""

# Core utilities
from .core import (
    APIResponse,
    DomainFilter,
    ModelResponse,
    SystemInfo,
    get_database_info,
    handle_database_error,
    handle_generic_error,
    print_database_info,
    print_startup_banner,
)

# Handler utilities
from .handlers import ModelRequestHandler, RequestValidator

# IR utilities
from .ir import (
    IRMetadataOperations,
    IRPopulationManager,
    IRValidationManager,
    ir_population_manager,
)

# Middleware (keep existing structure)
from .middleware import (
    database_middleware,
    environment_middleware,
    logging_middleware,
    timing_middleware,
)

# Schema utilities
from .schema import SchemaComparator, SchemaGenerator, camel_to_snake_case

# Timezone utilities
from .timezone import (
    convert_timezone,
    get_all_timezones,
    get_common_timezones,
    get_timezone_offset,
    get_timezone_selection,
    get_timezone_selection_lambda,
    get_user_timezone_display,
    validate_timezone,
)

# Validation utilities
from .validation import (
    RegistryUpdater,
    ValidationManager,
    get_registry_updater,
    get_validation_manager,
)

__all__ = [
    # Core utilities
    "APIResponse",
    "ModelResponse",
    "handle_database_error",
    "handle_generic_error",
    "DomainFilter",
    "SystemInfo",
    "print_startup_banner",
    "get_database_info",
    "print_database_info",
    # Schema utilities
    "SchemaGenerator",
    "SchemaComparator",
    "camel_to_snake_case",
    # IR utilities
    "IRPopulationManager",
    "ir_population_manager",
    "IRMetadataOperations",
    "IRValidationManager",
    # Validation utilities
    "ValidationManager",
    "get_validation_manager",
    "RegistryUpdater",
    "get_registry_updater",
    # Timezone utilities
    "get_all_timezones",
    "get_common_timezones",
    "validate_timezone",
    "get_timezone_offset",
    "convert_timezone",
    "get_user_timezone_display",
    "get_timezone_selection",
    "get_timezone_selection_lambda",
    # Handler utilities
    "ModelRequestHandler",
    "RequestValidator",
    # Middleware
    "database_middleware",
    "timing_middleware",
    "logging_middleware",
    "environment_middleware",
]
