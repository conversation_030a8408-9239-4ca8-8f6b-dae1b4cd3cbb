"""
Test suite for validating error handling across work unit boundaries

This module tests that errors propagate correctly through work unit boundaries
and that each work unit interrupts immediately when errors occur unless
explicit catch is needed for program logic.
"""

import asyncio
from unittest.mock import AsyncMock, Mock, patch

import pytest

from erp.addons.exceptions import (
    <PERSON>donError,
    AddonInstallationError,
    CircularDependencyError,
    DependencyError,
    MissingDependencyError,
)


class TestTransactionWorkUnitErrorHandling:
    """Test error handling in transaction work units"""

    @pytest.mark.asyncio
    async def test_transaction_middleware_error_propagation(self):
        """Test that transaction middleware properly propagates errors after rollback"""
        from erp.utils.middleware.transaction import TransactionMiddleware

        # Mock environment and request
        mock_env = Mock()
        mock_env.cr = Mock()
        mock_env.cr._get_connection = AsyncMock()
        mock_env.cr.begin = AsyncMock()
        mock_env.cr.commit = AsyncMock()
        mock_env.cr.rollback = AsyncMock()

        mock_request = Mock()
        mock_request.method = "POST"
        mock_request.url.path = "/test"

        # Mock call_next to raise an exception
        async def failing_call_next(request):
            raise ValueError("Test error")

        middleware = TransactionMiddleware()

        # Patch get_environment to return our mock
        with patch(
            "erp.utils.middleware.transaction.get_environment", return_value=mock_env
        ):
            # Should propagate the error after rollback
            with pytest.raises(ValueError, match="Test error"):
                await middleware.process_request(mock_request, failing_call_next)

            # Verify rollback was called
            mock_env.cr.rollback.assert_called_once()
            # Verify commit was not called
            mock_env.cr.commit.assert_not_called()

    @pytest.mark.asyncio
    async def test_transaction_context_error_propagation(self):
        """Test that transaction context properly propagates errors after rollback"""
        from erp.addons.utils.helpers import transaction_context

        mock_db_manager = Mock()
        mock_db_manager.begin = AsyncMock()
        mock_db_manager.commit = AsyncMock()
        mock_db_manager.rollback = AsyncMock()

        # Test that exception is propagated after rollback
        with pytest.raises(RuntimeError, match="Test transaction error"):
            async with transaction_context(mock_db_manager, "test_operation"):
                raise RuntimeError("Test transaction error")

        # Verify rollback was called
        mock_db_manager.rollback.assert_called_once()
        # Verify commit was not called
        mock_db_manager.commit.assert_not_called()


class TestAddonInstallationErrorHandling:
    """Test error handling in addon installation work units"""

    @pytest.mark.asyncio
    async def test_savepoint_manager_error_handling(self):
        """Test that savepoint manager handles errors correctly"""
        from erp.addons.managers.savepoint_manager import SavepointManager

        savepoint_manager = SavepointManager()

        # Mock environment
        mock_env = Mock()
        mock_env.cr = Mock()
        mock_env.cr.in_transaction = True  # Mock being in a transaction
        mock_env.cr.savepoint = AsyncMock()
        mock_env.cr.rollback_to_savepoint = AsyncMock()
        mock_env.cr.release_savepoint = AsyncMock()

        # Mock installation function that fails
        async def failing_installation():
            raise AddonInstallationError("Installation failed")

        # Should return False and handle rollback
        result = await savepoint_manager.execute_with_savepoint(
            "test_addon", mock_env, failing_installation, is_dependency=False
        )

        assert result is False
        # Verify rollback was called
        mock_env.cr.rollback_to_savepoint.assert_called_once()

    @pytest.mark.asyncio
    async def test_addon_manager_error_propagation(self):
        """Test that addon manager propagates dependency errors immediately"""
        from erp.addons.managers.addon_manager import AddonManager
        from erp.addons.managers.dependency_manager import DependencyManager

        addon_manager = AddonManager()

        # Mock dependency manager to raise circular dependency error
        with patch.object(addon_manager, "dependency_manager") as mock_dep_manager:
            mock_dep_manager.get_enhanced_install_order.side_effect = (
                CircularDependencyError("Circular dependency detected")
            )

            # Should propagate the error immediately
            with pytest.raises(
                CircularDependencyError, match="Circular dependency detected"
            ):
                await addon_manager.install_addon("test_addon")


class TestDataLoadingErrorHandling:
    """Test error handling in data loading work units"""

    @pytest.mark.asyncio
    async def test_data_loader_error_propagation(self):
        """Test that data loader propagates errors correctly"""
        from erp.data.exceptions import XMLParsingError
        from erp.data.loader import DataLoader

        loader = DataLoader()

        # Mock parser to raise XML parsing error
        with patch.object(loader, "parser") as mock_parser:
            mock_parser.parse_file.side_effect = XMLParsingError("Invalid XML")

            # Should propagate the error
            with pytest.raises(XMLParsingError, match="Invalid XML"):
                await loader.load_data_file("/fake/path.xml", "test_addon")

    @pytest.mark.asyncio
    async def test_security_processor_error_handling(self):
        """Test that security processor handles XML ID resolution errors correctly"""
        from erp.data.processors.security_processor import SecurityProcessor

        processor = SecurityProcessor()

        # Mock context and result
        processor.context = {"current_xml_id": "test.record"}
        processor.result = Mock()
        processor.result.add_error = Mock()

        # Mock XML ID manager to return None (not found)
        with patch(
            "erp.data.processors.security_processor.get_xml_id_manager"
        ) as mock_manager:
            mock_xml_manager = Mock()
            mock_xml_manager.resolve_xml_id = AsyncMock(return_value=None)
            mock_manager.return_value = mock_xml_manager

            # Should return None and add error to result
            result = await processor._resolve_xml_id_reference("missing.xml.id")

            assert result is None
            processor.result.add_error.assert_called_once()


class TestErrorPropagationIntegration:
    """Test end-to-end error propagation across work unit boundaries"""

    @pytest.mark.asyncio
    async def test_addon_installation_error_chain(self):
        """Test that errors propagate correctly through the entire addon installation chain"""
        from erp.addons.managers.addon_manager import AddonManager

        addon_manager = AddonManager()

        # Mock state manager to return valid addon info
        mock_addon_info = Mock()
        mock_addon_info.state = Mock()
        mock_addon_info.state.INSTALLED = "installed"
        mock_addon_info.manifest = Mock()
        mock_addon_info.manifest.installable = True

        with patch.object(addon_manager, "state_manager") as mock_state_manager:
            mock_state_manager.get_addon_info.return_value = mock_addon_info

            # Mock dependency manager to raise missing dependency error
            with patch.object(addon_manager, "dependency_manager") as mock_dep_manager:
                mock_dep_manager.get_enhanced_install_order.side_effect = (
                    MissingDependencyError("Required dependency 'base' not found")
                )

                # Should propagate the error immediately without attempting installation
                with pytest.raises(
                    MissingDependencyError, match="Required dependency 'base' not found"
                ):
                    await addon_manager.install_addon("test_addon")

                # Verify that no installation was attempted
                assert not hasattr(
                    addon_manager, "_install_with_dependency_savepoints_called"
                )


class TestWorkUnitBoundaryValidation:
    """Test that work unit boundaries are properly defined and respected"""

    def test_transaction_work_unit_boundaries(self):
        """Test that transaction work units have clear boundaries"""
        # Transaction work units should be:
        # 1. HTTP request processing (TransactionMiddleware)
        # 2. Addon installation (SavepointManager)
        # 3. Data loading operations (DataLoader)
        # 4. Manual transaction contexts

        # Verify middleware is properly configured
        from erp.utils.middleware.transaction import TransactionMiddleware

        assert hasattr(TransactionMiddleware, "process_request")

        # Verify savepoint manager exists
        from erp.addons.managers.savepoint_manager import SavepointManager

        assert hasattr(SavepointManager, "execute_with_savepoint")

        # Verify transaction context helper exists
        from erp.addons.utils.helpers import transaction_context

        assert callable(transaction_context)

    def test_error_handling_consistency(self):
        """Test that error handling is consistent across work units"""
        # All work units should either:
        # 1. Propagate errors immediately, or
        # 2. Handle cleanup and then propagate/return error info

        # Check that proper exception types are defined
        from erp.addons.exceptions import (
            AddonError,
            AddonInstallationError,
            DependencyError,
        )

        # Verify exception hierarchy
        assert issubclass(DependencyError, AddonError)
        assert issubclass(AddonInstallationError, AddonError)
        assert issubclass(CircularDependencyError, DependencyError)
        assert issubclass(MissingDependencyError, DependencyError)


class TestCleanupErrorHandling:
    """Test that cleanup operations handle errors correctly"""

    @pytest.mark.asyncio
    async def test_savepoint_rollback_error_handling(self):
        """Test that savepoint rollback errors are handled correctly"""
        from erp.addons.managers.savepoint_manager import SavepointManager

        savepoint_manager = SavepointManager()

        # Mock environment with failing rollback
        mock_env = Mock()
        mock_env.cr = Mock()
        mock_env.cr.savepoint = AsyncMock()
        mock_env.cr.rollback_to_savepoint = AsyncMock(
            side_effect=Exception("Rollback failed")
        )
        mock_env.cr.release_savepoint = AsyncMock()

        # Mock installation function that fails
        async def failing_installation():
            raise AddonInstallationError("Installation failed")

        # Should handle rollback error and still return False
        result = await savepoint_manager.execute_with_savepoint(
            "test_addon", mock_env, failing_installation, is_dependency=False
        )

        assert result is False
        # Verify rollback was attempted
        mock_env.cr.rollback_to_savepoint.assert_called_once()

    @pytest.mark.asyncio
    async def test_transaction_rollback_propagation(self):
        """Test that transaction rollback errors are propagated"""
        from erp.addons.utils.helpers import transaction_context

        mock_db_manager = Mock()
        mock_db_manager.begin = AsyncMock()
        mock_db_manager.commit = AsyncMock()
        mock_db_manager.rollback = AsyncMock(side_effect=Exception("Rollback failed"))

        # Should propagate rollback error
        with pytest.raises(Exception, match="Rollback failed"):
            async with transaction_context(mock_db_manager, "test_operation"):
                raise RuntimeError("Original error")


class TestErrorLoggingValidation:
    """Test that errors are properly logged before propagation"""

    @pytest.mark.asyncio
    async def test_addon_manager_error_logging(self):
        """Test that addon manager logs errors before propagation"""
        from erp.addons.managers.addon_manager import AddonManager

        addon_manager = AddonManager()

        with patch.object(addon_manager, "logger") as mock_logger:
            # Mock to raise error during validation
            with patch.object(addon_manager, "state_manager") as mock_state_manager:
                mock_state_manager.get_addon_info.return_value = None

                # Should log error and propagate
                with pytest.raises(ValueError, match="Addon test_addon not found"):
                    await addon_manager.install_addon("test_addon")

    def test_processor_error_logging(self):
        """Test that processors log errors correctly"""
        from erp.data.processors.base import BaseProcessor, ProcessorError

        class TestProcessor(BaseProcessor):
            def __init__(self):
                super().__init__("test_processor")

            async def _process_data(self, data, context):
                # Ignore unused parameters for test
                _ = data, context
                raise ValueError("Test processor error")

        processor = TestProcessor()

        with patch.object(processor, "logger") as mock_logger:
            with pytest.raises(ProcessorError):
                asyncio.run(processor.process([], {}))

            # Verify error was logged
            mock_logger.error.assert_called()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
