"""
Template Security - XSS protection, content sanitization, and security policies
"""

import html
import re
from typing import Any, Dict, List, Optional, Set
from urllib.parse import urlparse

from .exceptions import TemplateSecurityError


class SecurityPolicy:
    """Template security policy configuration"""

    def __init__(self):
        # Allowed HTML tags for t-raw directive
        self.allowed_tags: Set[str] = {
            "p",
            "br",
            "strong",
            "em",
            "b",
            "i",
            "u",
            "span",
            "div",
            "h1",
            "h2",
            "h3",
            "h4",
            "h5",
            "h6",
            "ul",
            "ol",
            "li",
            "dl",
            "dt",
            "dd",
            "table",
            "thead",
            "tbody",
            "tr",
            "th",
            "td",
            "a",
            "img",
            "code",
            "pre",
            "blockquote",
            "script",
            "style",
            "link",
            "meta",  # Allow for template rendering
        }

        # Allowed HTML attributes
        self.allowed_attributes: Dict[str, Set[str]] = {
            "a": {"href", "title", "target"},
            "img": {"src", "alt", "title", "width", "height"},
            "div": {"class", "id"},
            "span": {"class", "id"},
            "p": {"class", "id"},
            "h1": {"class", "id"},
            "h2": {"class", "id"},
            "h3": {"class", "id"},
            "h4": {"class", "id"},
            "h5": {"class", "id"},
            "h6": {"class", "id"},
            "table": {"class", "id"},
            "tr": {"class", "id"},
            "td": {"class", "id"},
            "th": {"class", "id"},
            "ul": {"class", "id"},
            "ol": {"class", "id"},
            "li": {"class", "id"},
            "script": {"src", "type", "async", "defer"},
            "style": {"type"},
            "link": {"rel", "href", "type", "media"},
            "meta": {"charset", "name", "content", "http-equiv"},
        }

        # Allowed URL schemes
        self.allowed_url_schemes: Set[str] = {"http", "https", "mailto", "tel"}

        # Dangerous expressions to block
        self.blocked_expressions: List[str] = [
            "__import__",
            "eval",
            "exec",
            "compile",
            "open",
            "file",
            "input",
            "raw_input",
            "reload",
            "vars",
            "dir",
            "globals",
            "locals",
            "getattr",
            "setattr",
            "delattr",
            "hasattr",
        ]

        # Maximum expression complexity (to prevent DoS)
        self.max_expression_length = 1000
        self.max_loop_iterations = 10000

        # Content Security Policy
        self.enable_csp = True
        self.strict_mode = False  # If True, be more restrictive

        # CSP directives - Allow common CDNs for ERP system
        self.csp_directives = {
            "default-src": "'self'",
            "script-src": "'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com",
            "style-src": "'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com https://fonts.googleapis.com",
            "img-src": "'self' data: https:",
            "font-src": "'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com",
            "connect-src": "'self'",
            "frame-src": "'none'",
            "object-src": "'none'",
            "base-uri": "'self'",
            "form-action": "'self'",
        }

    def is_tag_allowed(self, tag: str) -> bool:
        """Check if HTML tag is allowed"""
        return tag.lower() in self.allowed_tags

    def is_attribute_allowed(self, tag: str, attribute: str) -> bool:
        """Check if HTML attribute is allowed for given tag"""
        tag = tag.lower()
        attribute = attribute.lower()

        # Global allowed attributes
        global_attrs = {"class", "id", "style", "title"}
        if attribute in global_attrs:
            return True

        # Tag-specific attributes
        return attribute in self.allowed_attributes.get(tag, set())

    def is_url_safe(self, url: str) -> bool:
        """Check if URL is safe"""
        if not url:
            return True

        try:
            parsed = urlparse(url)

            # Check scheme
            if parsed.scheme and parsed.scheme.lower() not in self.allowed_url_schemes:
                return False

            # Block javascript: and data: URLs for certain contexts
            if parsed.scheme.lower() in ("javascript", "vbscript"):
                return False

            return True
        except Exception:
            return False

    def get_csp_header(self) -> str:
        """Generate Content Security Policy header value"""
        if not self.enable_csp:
            return ""

        directives = []
        for directive, value in self.csp_directives.items():
            directives.append(f"{directive} {value}")

        return "; ".join(directives)

    def update_csp_directive(self, directive: str, value: str) -> None:
        """Update a CSP directive"""
        self.csp_directives[directive] = value

    def is_expression_safe(self, expression: str) -> bool:
        """Check if template expression is safe"""
        if len(expression) > self.max_expression_length:
            return False

        # Check for blocked expressions (more precise matching)
        import re

        expr_lower = expression.lower()
        for blocked in self.blocked_expressions:
            # Use word boundaries to avoid false positives
            # Match function calls like "eval(" or standalone words
            pattern = rf"\b{re.escape(blocked)}\s*\("
            if re.search(pattern, expr_lower):
                return False
            # Also check for standalone dangerous words at word boundaries
            if blocked in ["__import__", "globals", "locals", "vars", "dir"]:
                pattern = rf"\b{re.escape(blocked)}\b"
                if re.search(pattern, expr_lower):
                    return False

        return True


class ContentSanitizer:
    """Sanitizes content to prevent XSS attacks"""

    def __init__(self, policy: SecurityPolicy):
        self.policy = policy

        # Regex patterns for HTML parsing
        self.tag_pattern = re.compile(r"<(/?)([a-zA-Z][a-zA-Z0-9]*)(.*?)>", re.DOTALL)
        self.attr_pattern = re.compile(
            r'([a-zA-Z][a-zA-Z0-9-]*)\s*=\s*["\']([^"\']*)["\']'
        )

        # Dangerous patterns
        self.script_pattern = re.compile(
            r"<script[^>]*>.*?</script>", re.IGNORECASE | re.DOTALL
        )
        self.style_script_pattern = re.compile(
            r"<style[^>]*>.*?expression\s*\(.*?\).*?</style>", re.IGNORECASE | re.DOTALL
        )
        self.javascript_pattern = re.compile(r"javascript:", re.IGNORECASE)
        self.vbscript_pattern = re.compile(r"vbscript:", re.IGNORECASE)
        self.data_pattern = re.compile(r"data:[^;]*;base64", re.IGNORECASE)
        self.on_event_pattern = re.compile(r"\bon\w+\s*=", re.IGNORECASE)
        self.expression_pattern = re.compile(r"expression\s*\(", re.IGNORECASE)
        self.import_pattern = re.compile(r"@import", re.IGNORECASE)

        # Additional dangerous patterns
        self.meta_refresh_pattern = re.compile(
            r"<meta[^>]*http-equiv[^>]*refresh[^>]*>", re.IGNORECASE
        )
        self.iframe_pattern = re.compile(r"<iframe[^>]*>", re.IGNORECASE)
        self.object_pattern = re.compile(
            r"<object[^>]*>.*?</object>", re.IGNORECASE | re.DOTALL
        )
        self.embed_pattern = re.compile(r"<embed[^>]*>", re.IGNORECASE)

    def sanitize_html(self, content: str) -> str:
        """Sanitize HTML content"""
        if not content:
            return content

        # For template rendering, we need to be less aggressive with script/style removal
        # Only remove obviously dangerous patterns, not all scripts/styles

        # Remove dangerous meta refresh
        content = self.meta_refresh_pattern.sub("", content)
        content = self.object_pattern.sub("", content)
        content = self.embed_pattern.sub("", content)

        # Remove dangerous URLs in attributes (but not in script/style content)
        # This is handled in attribute sanitization

        # Remove event handlers in attributes
        content = self.on_event_pattern.sub("", content)

        # Handle iframe tags based on policy
        if not self.policy.is_tag_allowed("iframe"):
            content = self.iframe_pattern.sub("", content)

        # Process HTML tags
        def replace_tag(match):
            is_closing = bool(match.group(1))
            tag_name = match.group(2).lower()
            attributes = match.group(3)

            # Check if tag is allowed
            if not self.policy.is_tag_allowed(tag_name):
                return ""  # Remove disallowed tags

            if is_closing:
                return f"</{tag_name}>"

            # Process attributes
            clean_attrs = self._sanitize_attributes(tag_name, attributes)
            attr_str = " ".join(
                f'{name}="{value}"' for name, value in clean_attrs.items()
            )

            if attr_str:
                return f"<{tag_name} {attr_str}>"
            else:
                return f"<{tag_name}>"

        return self.tag_pattern.sub(replace_tag, content)

    def _sanitize_attributes(self, tag: str, attr_string: str) -> Dict[str, str]:
        """Sanitize HTML attributes"""
        clean_attrs = {}

        for match in self.attr_pattern.finditer(attr_string):
            attr_name = match.group(1).lower()
            attr_value = match.group(2)

            # Check if attribute is allowed
            if not self.policy.is_attribute_allowed(tag, attr_name):
                continue

            # Sanitize attribute value
            if attr_name in ("href", "src"):
                if not self.policy.is_url_safe(attr_value):
                    continue

            # Escape attribute value
            clean_attrs[attr_name] = html.escape(attr_value)

        return clean_attrs

    def sanitize_text(self, text: str) -> str:
        """Sanitize plain text content"""
        return html.escape(str(text))

    def validate_expression(self, expression: str) -> None:
        """Validate template expression for security"""
        if not self.policy.is_expression_safe(expression):
            raise TemplateSecurityError(
                f"Unsafe expression detected: {expression[:100]}..."
            )


class SecurityManager:
    """Manages template security policies and enforcement"""

    def __init__(self, policy: Optional[SecurityPolicy] = None):
        self.policy = policy or SecurityPolicy()
        self.sanitizer = ContentSanitizer(self.policy)
        self.enabled = True

        # Security event logging
        self.security_events: List[Dict[str, Any]] = []
        self.max_events = 1000

    def log_security_event(self, event_type: str, details: Dict[str, Any]) -> None:
        """Log a security event"""
        import time

        event = {"timestamp": time.time(), "type": event_type, "details": details}

        self.security_events.append(event)

        # Keep events list manageable
        if len(self.security_events) > self.max_events:
            self.security_events = self.security_events[-self.max_events // 2 :]

    def validate_expression(self, expression: str, context: Dict[str, Any]) -> None:
        """Validate template expression"""
        if not self.enabled:
            return

        try:
            self.sanitizer.validate_expression(expression)
        except TemplateSecurityError as e:
            self.log_security_event(
                "blocked_expression",
                {
                    "expression": expression[:200],
                    "error": str(e),
                    "context_keys": list(context.keys()),
                },
            )
            raise

    def sanitize_output(self, content: str, output_type: str = "html") -> str:
        """Sanitize template output"""
        if not self.enabled:
            return content

        if output_type == "html":
            return self.sanitizer.sanitize_html(content)
        elif output_type == "text":
            return self.sanitizer.sanitize_text(content)
        else:
            return content

    def check_context_safety(self, context: Dict[str, Any]) -> None:
        """Check if context contains potentially dangerous objects"""
        if not self.enabled:
            return

        dangerous_types = {
            "module",
            "function",
            "builtin_function_or_method",
            "type",
            "classmethod",
            "staticmethod",
        }

        for key, value in context.items():
            if type(value).__name__ in dangerous_types:
                self.log_security_event(
                    "dangerous_context",
                    {
                        "key": key,
                        "type": type(value).__name__,
                        "repr": repr(value)[:100],
                    },
                )

                if self.policy.strict_mode:
                    raise TemplateSecurityError(f"Dangerous object in context: {key}")

    def get_security_report(self) -> Dict[str, Any]:
        """Get security report"""
        event_types = {}
        for event in self.security_events:
            event_type = event["type"]
            event_types[event_type] = event_types.get(event_type, 0) + 1

        return {
            "enabled": self.enabled,
            "strict_mode": self.policy.strict_mode,
            "total_events": len(self.security_events),
            "event_types": event_types,
            "recent_events": self.security_events[-10:] if self.security_events else [],
        }

    def clear_events(self) -> None:
        """Clear security events log"""
        self.security_events.clear()

    def set_enabled(self, enabled: bool) -> None:
        """Enable or disable security enforcement"""
        self.enabled = enabled

    def set_strict_mode(self, strict: bool) -> None:
        """Enable or disable strict security mode"""
        self.policy.strict_mode = strict

    def get_security_headers(self) -> Dict[str, str]:
        """Get security headers for HTTP responses"""
        headers = {}

        if not self.enabled:
            return headers

        # Content Security Policy
        csp_header = self.policy.get_csp_header()
        if csp_header:
            headers["Content-Security-Policy"] = csp_header

        # Additional security headers
        headers.update(
            {
                "X-Content-Type-Options": "nosniff",
                "X-Frame-Options": "DENY",
                "X-XSS-Protection": "1; mode=block",
                "Referrer-Policy": "strict-origin-when-cross-origin",
                "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
            }
        )

        if self.policy.strict_mode:
            headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"

        return headers

    def validate_template_content(self, content: str) -> List[str]:
        """Validate template content and return list of security issues"""
        issues = []

        if not self.enabled:
            return issues

        # Check for dangerous patterns in template content
        dangerous_patterns = [
            (r"<script[^>]*>", "Script tags detected"),
            (r"javascript:", "JavaScript URLs detected"),
            (r"vbscript:", "VBScript URLs detected"),
            (r"on\w+\s*=", "Event handlers detected"),
            (r"expression\s*\(", "CSS expressions detected"),
            (r"__import__", "Import statements detected"),
            (r"eval\s*\(", "Eval statements detected"),
            (r"exec\s*\(", "Exec statements detected"),
        ]

        for pattern, message in dangerous_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                issues.append(message)

        return issues


# Global security manager instance
security_manager = SecurityManager()


def get_security_manager() -> SecurityManager:
    """Get the global security manager instance"""
    return security_manager


def enable_security(enabled: bool = True) -> None:
    """Enable or disable template security globally"""
    security_manager.set_enabled(enabled)


def set_strict_mode(strict: bool = True) -> None:
    """Enable or disable strict security mode globally"""
    security_manager.set_strict_mode(strict)


def is_security_enabled() -> bool:
    """Check if security is enabled"""
    return security_manager.enabled
