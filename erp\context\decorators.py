"""
Context decorators for ERP system
"""

import asyncio
from functools import wraps
from typing import Callable

from .manager import Context<PERSON><PERSON><PERSON>


def with_environment(func: Callable) -> Callable:
    """
    Decorator to ensure a function has access to the current environment
    """
    if asyncio.iscoroutinefunction(func):

        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            env = ContextManager.get_environment()
            if env is None:
                raise RuntimeError(
                    "No environment found in context. Use ContextManager.with_context() or set environment."
                )
            return await func(*args, **kwargs)

        return async_wrapper
    else:

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            env = ContextManager.get_environment()
            if env is None:
                raise RuntimeError(
                    "No environment found in context. Use ContextManager.with_context() or set environment."
                )
            return func(*args, **kwargs)

        return sync_wrapper


def require_database(func: Callable) -> Callable:
    """
    Decorator to ensure a function has access to a database context
    """
    if asyncio.iscoroutinefunction(func):

        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            db_name = ContextManager.get_database()
            if db_name is None:
                raise RuntimeError("No database found in context.")
            return await func(*args, **kwargs)

        return async_wrapper
    else:

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            db_name = ContextManager.get_database()
            if db_name is None:
                raise RuntimeError("No database found in context.")
            return func(*args, **kwargs)

        return sync_wrapper


def require_user(func: Callable) -> Callable:
    """
    Decorator to ensure a function has access to a user context
    """
    if asyncio.iscoroutinefunction(func):

        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            user_id = ContextManager.get_user()
            if user_id is None:
                raise RuntimeError("No user found in context.")
            return await func(*args, **kwargs)

        return async_wrapper
    else:

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            user_id = ContextManager.get_user()
            if user_id is None:
                raise RuntimeError("No user found in context.")
            return func(*args, **kwargs)

        return sync_wrapper
