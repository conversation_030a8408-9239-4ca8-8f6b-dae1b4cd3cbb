"""
Comprehensive tests for enhanced SQL-based XML loading system

This test suite verifies that the enhanced XML loading system works correctly
with raw SQL validation, proper transactional context, and improved field validation.
"""

import os
import tempfile
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from erp.data.enhanced_loader import EnhancedSQLDataLoader
from erp.data.validation.sql_validators import SQLFieldValidator
from erp.database.connection.manager import DatabaseManager


@pytest.fixture
async def mock_db_manager():
    """Create a mock database manager for testing."""
    db_manager = MagicMock(spec=DatabaseManager)

    # Mock basic database operations
    db_manager.execute = AsyncMock()
    db_manager.fetch = AsyncMock()
    db_manager.fetchrow = AsyncMock()
    db_manager.fetchval = AsyncMock()
    db_manager.acquire_connection = MagicMock()

    # Mock connection context manager
    mock_conn = MagicMock()
    mock_conn.execute = AsyncMock()
    mock_conn.fetch = AsyncMock()
    mock_conn.fetchrow = AsyncMock()
    mock_conn.fetchval = AsyncMock()

    db_manager.acquire_connection.return_value.__aenter__ = AsyncMock(
        return_value=mock_conn
    )
    db_manager.acquire_connection.return_value.__aexit__ = AsyncMock(return_value=None)

    return db_manager


@pytest.fixture
def sample_xml_content():
    """Sample XML content for testing."""
    return """<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="test_partner" model="res.partner">
            <field name="name">Test Partner</field>
            <field name="email"><EMAIL></field>
            <field name="is_company" type="eval">True</field>
            <field name="category_id" type="ref">base.res_partner_category_1</field>
        </record>
        
        <record id="test_user" model="res.users">
            <field name="name">Test User</field>
            <field name="login">testuser</field>
            <field name="partner_id" type="ref">test_partner</field>
            <field name="active" type="eval">True</field>
        </record>
    </data>
</odoo>"""


@pytest.fixture
def temp_xml_file(sample_xml_content):
    """Create a temporary XML file for testing."""
    with tempfile.NamedTemporaryFile(mode="w", suffix=".xml", delete=False) as f:
        f.write(sample_xml_content)
        temp_path = f.name

    yield temp_path

    # Cleanup
    if os.path.exists(temp_path):
        os.unlink(temp_path)


class TestSQLFieldValidator:
    """Test the SQL field validator."""

    async def test_validate_boolean_field(self, mock_db_manager):
        """Test boolean field validation."""
        validator = SQLFieldValidator(mock_db_manager)

        # Test valid boolean values
        test_cases = [
            (True, True),
            (False, False),
            ("true", True),
            ("false", False),
            ("1", True),
            ("0", False),
            ("yes", True),
            ("no", False),
            (1, True),
            (0, False),
            (None, None),
        ]

        for input_val, expected in test_cases:
            result = await validator.validate_boolean_field(input_val)
            assert result["valid"] is True
            assert result["value"] == expected
            assert result["error"] is None

        # Test invalid boolean values
        invalid_cases = ["invalid", "maybe", 2.5]
        for input_val in invalid_cases:
            result = await validator.validate_boolean_field(input_val)
            assert result["valid"] is False
            assert result["error"] is not None

    async def test_validate_char_field(self, mock_db_manager):
        """Test character field validation."""
        validator = SQLFieldValidator(mock_db_manager)

        # Test valid character values
        result = await validator.validate_char_field("test string")
        assert result["valid"] is True
        assert result["value"] == "test string"

        # Test size constraint
        result = await validator.validate_char_field("long string", size=5)
        assert result["valid"] is False
        assert "too long" in result["error"]

        # Test required field
        result = await validator.validate_char_field("", required=True)
        assert result["valid"] is False
        assert "required" in result["error"]

        # Test None with required
        result = await validator.validate_char_field(None, required=True)
        assert result["valid"] is False
        assert "required" in result["error"]

    async def test_validate_many2one_field(self, mock_db_manager):
        """Test Many2one field validation."""
        validator = SQLFieldValidator(mock_db_manager)

        # Mock XML ID validation
        validator.xmlid_sql.validate_xmlid_reference = AsyncMock(
            return_value={"valid": True, "res_id": "123", "error": None}
        )

        # Mock record existence check
        validator.model_sql.validate_record_exists = AsyncMock(return_value=True)

        # Test XML ID reference
        result = await validator.validate_many2one_field(
            "base.partner_1", "res.partner"
        )
        assert result["valid"] is True
        assert result["value"] == "123"

        # Test direct ID
        result = await validator.validate_many2one_field("456", "res.partner")
        assert result["valid"] is True
        assert result["value"] == "456"

        # Test None value
        result = await validator.validate_many2one_field(None, "res.partner")
        assert result["valid"] is True
        assert result["value"] is None


class TestEnhancedSQLDataLoader:
    """Test the enhanced SQL data loader."""

    async def test_transaction_context(self, mock_db_manager):
        """Test transaction context management."""
        loader = EnhancedSQLDataLoader(mock_db_manager)

        # Test successful transaction
        async with loader.transaction_context("test_operation") as tx_db:
            assert tx_db is not None
            # Verify transaction started
            mock_db_manager.acquire_connection.return_value.__aenter__.assert_called_once()

    async def test_validate_record_structure(self, mock_db_manager):
        """Test record structure validation."""
        loader = EnhancedSQLDataLoader(mock_db_manager)

        # Test valid record
        valid_record = {"model": "res.partner", "values": {"name": "Test"}}
        result = await loader._validate_record_structure(valid_record)
        assert result["valid"] is True

        # Test invalid record - missing model
        invalid_record = {"values": {"name": "Test"}}
        result = await loader._validate_record_structure(invalid_record)
        assert result["valid"] is False
        assert "model" in result["error"]

        # Test invalid record - missing values
        invalid_record = {"model": "res.partner"}
        result = await loader._validate_record_structure(invalid_record)
        assert result["valid"] is False
        assert "values" in result["error"]

    async def test_validate_model_and_fields(self, mock_db_manager):
        """Test model and field validation."""
        loader = EnhancedSQLDataLoader(mock_db_manager)

        # Mock model validation
        loader.sql.validate_model_exists = AsyncMock(return_value=True)
        loader.model_sql.validate_model_table_exists = AsyncMock(return_value=True)
        loader.model_sql.get_model_fields = AsyncMock(
            return_value=[
                {"name": "name", "ttype": "char"},
                {"name": "email", "ttype": "char"},
            ]
        )

        # Test valid model and fields
        record = {
            "model": "res.partner",
            "values": {"name": "Test", "email": "<EMAIL>"},
        }
        result = await loader._validate_model_and_fields(record)
        assert result["valid"] is True

        # Test invalid field
        record = {"model": "res.partner", "values": {"invalid_field": "value"}}
        result = await loader._validate_model_and_fields(record)
        assert result["valid"] is False
        assert "invalid_field" in result["error"]

    async def test_load_data_file_success(self, mock_db_manager, temp_xml_file):
        """Test successful data file loading."""
        loader = EnhancedSQLDataLoader(mock_db_manager)

        # Mock all validation methods to return success
        loader.sql.validate_model_exists = AsyncMock(return_value=True)
        loader.model_sql.validate_model_table_exists = AsyncMock(return_value=True)
        loader.model_sql.get_model_fields = AsyncMock(
            return_value=[
                {"name": "name", "ttype": "char"},
                {"name": "email", "ttype": "char"},
                {"name": "is_company", "ttype": "boolean"},
                {
                    "name": "category_id",
                    "ttype": "many2one",
                    "relation": "res.partner.category",
                },
                {"name": "login", "ttype": "char"},
                {"name": "partner_id", "ttype": "many2one", "relation": "res.partner"},
                {"name": "active", "ttype": "boolean"},
            ]
        )

        # Mock field validation
        loader.field_validator.validate_field_by_type = AsyncMock(
            return_value={"valid": True, "value": "test_value", "error": None}
        )

        # Mock XML ID operations
        loader.xmlid_sql.xmlid_lookup = AsyncMock(return_value=None)
        loader.xmlid_sql.create_or_update_xmlid = AsyncMock(return_value=True)
        loader.xmlid_sql.validate_xmlid_reference = AsyncMock(
            return_value={"valid": True, "res_id": "123", "error": None}
        )

        # Mock record creation
        loader.model_sql.create_record = AsyncMock(return_value="new_id_123")

        # Test loading
        result = await loader.load_data_file(temp_xml_file, "test_addon")

        assert result["success"] is True
        assert result["statistics"]["total_records"] > 0
        assert result["statistics"]["successful_records"] > 0
        assert len(result["errors"]) == 0

    async def test_load_data_file_validation_error(
        self, mock_db_manager, temp_xml_file
    ):
        """Test data file loading with validation errors."""
        loader = EnhancedSQLDataLoader(mock_db_manager)

        # Mock model validation to fail
        loader.sql.validate_model_exists = AsyncMock(return_value=False)

        result = await loader.load_data_file(temp_xml_file, "test_addon")

        assert result["success"] is False
        assert result["statistics"]["failed_records"] > 0
        assert len(result["errors"]) > 0

    async def test_process_special_field_values(self, mock_db_manager):
        """Test processing of special field value formats."""
        loader = EnhancedSQLDataLoader(mock_db_manager)

        # Mock XML ID validation
        loader.xmlid_sql.validate_xmlid_reference = AsyncMock(
            return_value={"valid": True, "res_id": "123", "error": None}
        )

        # Test ref type
        field_def = {"type": "ref", "value": "base.partner_1"}
        result = await loader._process_special_field_value(
            field_def, "many2one", {"relation": "res.partner"}
        )
        assert result == "123"

        # Test eval type - boolean
        field_def = {"type": "eval", "value": "True"}
        result = await loader._process_special_field_value(field_def, "boolean", {})
        assert result is True

        # Test eval type - integer
        field_def = {"type": "eval", "value": "42"}
        result = await loader._process_special_field_value(field_def, "integer", {})
        assert result == 42


@pytest.mark.asyncio
async def test_integration_xml_loading():
    """Integration test for the complete XML loading process."""
    # This would be a more comprehensive integration test
    # that tests the entire flow with a real database
    pass


if __name__ == "__main__":
    pytest.main([__file__])
