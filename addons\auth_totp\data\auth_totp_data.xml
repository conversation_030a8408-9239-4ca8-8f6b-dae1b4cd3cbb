<?xml version="1.0" encoding="utf-8"?>
<data>
    <!-- TOTP Authentication Module Data -->
    
    <!-- Module Category for TOTP -->
    <record id="module_category_totp" model="ir.module.category">
        <field name="name">Two-Factor Authentication</field>
        <field name="description">Time-based One-Time Password (TOTP) authentication</field>
        <field name="sequence">15</field>
    </record>

    <!-- TOTP User Group -->
    <record id="group_totp_user" model="res.groups">
        <field name="name">TOTP User</field>
        <field name="category_id" ref="module_category_totp"/>
        <field name="comment">Users who can use two-factor authentication</field>
    </record>

    <!-- TOTP Admin Group -->
    <record id="group_totp_admin" model="res.groups">
        <field name="name">TOTP Administrator</field>
        <field name="category_id" ref="module_category_totp"/>
        <field name="comment">Users who can manage two-factor authentication settings</field>
        <field name="implied_ids" eval="[(4, ref('group_totp_user'))]"/>
    </record>

</data>
