"""
Test suite to verify that functionality is preserved after modular reorganization

This module tests:
- All original functionality still works
- No regressions introduced
- Field validation still works
- Model operations still work
- Registry operations still work
"""

import uuid
from datetime import datetime
from unittest.mock import AsyncMock, patch

import pytest


class TestFunctionalityPreservation:
    """Test that all functionality is preserved after reorganization"""

    def test_field_validation_preserved(self):
        """Test that field validation still works correctly"""
        from erp.fields import Char, FieldValidationError, Many2One

        # Test Char field validation
        char_field = Char(string="Name", required=True, size=50)

        # Valid value
        assert char_field.validate("Test") == "Test"

        # Required field validation
        with pytest.raises(FieldValidationError):
            char_field.validate(None)

        # Test Many2One validation
        many2one_field = Many2One("res.partner")

        # Valid UUID
        test_uuid = str(uuid.uuid4())
        assert many2one_field.validate(test_uuid) == test_uuid

        # Invalid UUID
        with pytest.raises(FieldValidationError):
            many2one_field.validate("invalid-uuid")

    def test_model_field_collection_preserved(self):
        """Test that model field collection via metaclass still works"""
        from erp.fields import <PERSON>olean, Char, Integer
        from erp.models import Model

        class TestModel(Model):
            _name = "test.model"

            custom_name = Char(string="Custom Name")
            custom_value = Integer(string="Custom Value")
            custom_flag = Boolean(string="Custom Flag")

        # Check that fields are collected properly
        assert hasattr(TestModel, "_fields")
        assert "custom_name" in TestModel._fields
        assert "custom_value" in TestModel._fields
        assert "custom_flag" in TestModel._fields

        # Check that inherited fields are also present
        assert "id" in TestModel._fields
        assert "name" in TestModel._fields
        assert "createAt" in TestModel._fields

    def test_model_instance_creation_preserved(self):
        """Test that model instance creation still works"""
        from erp.fields import Char, Integer
        from erp.models import Model

        class TestModel(Model):
            _name = "test.model"

            custom_field = Char(string="Custom Field")
            number_field = Integer(string="Number Field")

        # Test instance creation with values
        instance = TestModel(
            name="Test Instance", custom_field="Custom Value", number_field=42
        )

        assert instance.name == "Test Instance"
        assert instance.custom_field == "Custom Value"
        assert instance.number_field == 42
        assert instance._is_new_record is True

    def test_model_types_behavior_preserved(self):
        """Test that different model types behave correctly"""
        from erp.models import AbstractModel, Model, TransientModel

        # Test AbstractModel
        class TestAbstractModel(AbstractModel):
            _name = "test.abstract"

        assert TestAbstractModel._abstract is True
        assert TestAbstractModel._auto_create_table is False

        # Test TransientModel
        class TestTransientModel(TransientModel):
            _name = "test.transient"

        assert TestTransientModel._transient is True
        assert TestTransientModel._auto_create_table is True
        assert TestTransientModel._transient_max_hours == 1

        # Test standard Model
        class TestStandardModel(Model):
            _name = "test.standard"

        assert TestStandardModel._auto_create_table is True
        assert TestStandardModel._transient is False

    def test_relational_field_functionality_preserved(self):
        """Test that relational fields still work correctly"""
        from erp.fields import Many2Many, Many2One, One2Many, One2One

        # Test Many2One
        many2one = Many2One("res.partner", ondelete="cascade")
        assert many2one.comodel_name == "res.partner"
        assert many2one.ondelete == "cascade"

        # Test One2Many
        one2many = One2Many("res.partner", "parent_id")
        assert one2many.comodel_name == "res.partner"
        assert one2many.inverse_name == "parent_id"
        assert one2many.store is False

        # Test Many2Many
        many2many = Many2Many("res.partner")
        assert many2many.comodel_name == "res.partner"
        assert many2many.store is False

        # Test One2One
        one2one = One2One("res.partner")
        assert one2one.comodel_name == "res.partner"
        assert one2one.unique is True

    def test_registry_functionality_preserved(self):
        """Test that model registry functionality is preserved"""
        from erp.models import ModelRegistry

        # Create registry
        registry = ModelRegistry("test_addon")
        assert registry.addon_name == "test_addon"

        # Test registry methods exist
        assert hasattr(registry, "discover_models")
        assert hasattr(registry, "all")
        assert hasattr(registry, "get")
        assert hasattr(registry, "get_models_by_type")
        assert hasattr(registry, "get_model_names")
        assert hasattr(registry, "has_models")
        assert hasattr(registry, "clear")

    @patch("erp.database.registry.DatabaseRegistry.get_current_database")
    async def test_crud_operations_preserved(self, mock_db):
        """Test that CRUD operations are preserved"""
        from erp.fields import Char
        from erp.models import Model

        # Mock database
        mock_db_instance = AsyncMock()
        mock_db_instance.insert.return_value = "test-id"
        mock_db.return_value = mock_db_instance

        class TestModel(Model):
            _name = "test.model"
            test_field = Char(string="Test Field")

        # Test that CRUD methods exist
        assert hasattr(TestModel, "create")
        assert hasattr(TestModel, "search")
        assert hasattr(TestModel, "browse")

        # Test instance methods
        instance = TestModel(name="Test")
        assert hasattr(instance, "write")
        assert hasattr(instance, "unlink")

    def test_field_sql_types_preserved(self):
        """Test that field SQL types are preserved"""
        from erp.fields import Boolean, Char, Date, Datetime, Float, Integer, Many2One

        # Test basic field SQL types
        assert Char().get_sql_type() == "TEXT"
        assert Char(size=100).get_sql_type() == "VARCHAR(100)"
        assert Integer().get_sql_type() == "INTEGER"
        assert Float().get_sql_type() == "REAL"
        assert Boolean().get_sql_type() == "BOOLEAN"
        assert Date().get_sql_type() == "DATE"
        assert Datetime().get_sql_type() == "TIMESTAMP"
        assert Many2One("res.partner").get_sql_type() == "UUID"

    def test_field_default_values_preserved(self):
        """Test that field default values work correctly"""
        from erp.fields import Boolean, Char, Datetime, Integer

        # Test static defaults
        char_field = Char(default="default_value")
        assert char_field.get_default_value() == "default_value"

        int_field = Integer(default=42)
        assert int_field.get_default_value() == 42

        bool_field = Boolean(default=True)
        assert bool_field.get_default_value() is True

        # Test callable defaults
        datetime_field = Datetime(default=lambda: datetime.now())
        default_value = datetime_field.get_default_value()
        assert isinstance(default_value, datetime)

    def test_command_operations_preserved(self):
        """Test that command operations for relational fields are preserved"""
        from erp.fields import Command

        # Test command constants
        assert Command.CREATE == 0
        assert Command.UPDATE == 1
        assert Command.DELETE == 2
        assert Command.UNLINK == 3
        assert Command.LINK == 4
        assert Command.CLEAR == 5
        assert Command.SET == 6

        # Test command creation methods
        create_cmd = Command.create({"name": "Test"})
        assert create_cmd == (0, 0, {"name": "Test"})

        update_cmd = Command.update("test-id", {"name": "Updated"})
        assert update_cmd == (1, "test-id", {"name": "Updated"})

    def test_model_inheritance_preserved(self):
        """Test that model inheritance still works correctly"""
        from erp.fields import Char, Integer
        from erp.models import AbstractModel, Model

        # Create abstract model
        class BaseTestModel(AbstractModel):
            _name = "base.test"

            base_field = Char(string="Base Field")
            base_number = Integer(string="Base Number")

        # Create concrete model inheriting from abstract
        class ConcreteTestModel(BaseTestModel, Model):
            _name = "concrete.test"

            concrete_field = Char(string="Concrete Field")

        # Check field inheritance
        assert "base_field" in ConcreteTestModel._fields
        assert "base_number" in ConcreteTestModel._fields
        assert "concrete_field" in ConcreteTestModel._fields

        # Check that abstract model properties are inherited
        assert ConcreteTestModel._auto_create_table is True  # From Model
        assert ConcreteTestModel._transient is False  # From Model
