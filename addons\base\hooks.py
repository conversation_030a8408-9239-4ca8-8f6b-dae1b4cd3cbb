"""
Base addon hooks

Standard addon installation hooks for the base addon.
The base addon now uses the same installation process as regular addons.
"""

from erp.addons.hooks import post_install_hook
from erp.addons.utils import populate_ir_module_table_with_all_addons
from erp.logging import get_logger

logger = get_logger(__name__)


@post_install_hook('base', priority=10)
async def base_post_install(context):
    """
    Post-installation hook for base addon.

    This hook runs after the base addon installation and ensures
    that core functionality is properly initialized.

    Specifically, this hook populates the ir.module.module table with
    all valid addons from all configured addon paths with default state.
    """
    logger.info("Base addon post-install hook executed")

    # The core IR population and schema sync is handled automatically
    # by the core_hooks.py post-install hook that runs for all addons

    # Populate ir.module.module table with all discovered addons
    try:
        logger.info("Populating ir.module.module table with all discovered addons")

        results = await populate_ir_module_table_with_all_addons(context.env.cr)

        if results.get("status") == "success":
            logger.info(
                "✅ Successfully populated ir.module.module table: "
                "%d addons processed, %d skipped",
                results.get('addons_processed', 0),
                results.get('addons_skipped', 0)
            )

            if results.get("errors"):
                logger.warning(
                    "Encountered %d errors during population",
                    len(results['errors'])
                )
        else:
            error_msg = results.get('message', 'Unknown error')
            logger.error(
                "Failed to populate ir.module.module table: %s", error_msg
            )
            return False

    except Exception as e:
        logger.error("Error during ir.module.module population: %s", e)
        return False

    return True
