"""
Hook context classes

This module defines the context objects passed to hooks during execution.
"""

from typing import TYPE_CHECKING, Any, Dict, Optional

if TYPE_CHECKING:
    from ...environment import Environment
else:
    # For runtime, we'll use a string annotation
    Environment = "Environment"

from .hook_types import HookType


class HookContext:
    """Context passed to lifecycle hooks"""

    # Type annotations for instance attributes
    addon_name: str
    hook_type: HookType
    env: Optional["Environment"]
    data: Dict[str, Any]

    def __init__(
        self,
        addon_name: str,
        hook_type: HookType,
        env: Optional["Environment"] = None,
        **kwargs: Any,
    ) -> None:
        self.addon_name = addon_name
        self.hook_type = hook_type
        self.env = env
        self.data = kwargs

    def __repr__(self) -> str:
        return f"HookContext(addon={self.addon_name}, type={self.hook_type.value})"
