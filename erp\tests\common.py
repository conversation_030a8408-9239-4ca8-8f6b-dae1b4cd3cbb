"""
Common test base classes and utilities for the ERP test framework
"""

import asyncio
import unittest
from unittest.mock import MagicMock

from erp.addons import <PERSON>donManager
from erp.database.memory import AppRegistry
from erp.environment.core import Environment


class BaseTestCase(unittest.TestCase):
    """Base test case with common ERP test utilities"""

    def setUp(self):
        """Set up test case"""
        super().setUp()
        self._setup_test_environment()

    def tearDown(self):
        """Clean up after test"""
        self._cleanup_test_environment()
        super().tearDown()

    def _setup_test_environment(self):
        """Set up test environment"""
        # Create test environment
        self.test_db_name = f"test_db_{id(self)}"
        self.env = None

    def _cleanup_test_environment(self):
        """Clean up test environment"""
        # Clean up any test data
        pass

    def assertRaisesRegex(self, exception_class, regex, *args, **kwargs):
        """Compatibility method for assertRaisesRegex"""
        return super().assertRaisesRegex(exception_class, regex, *args, **kwargs)


class TransactionCase(BaseTestCase):
    """Test case with database transaction support"""

    def setUp(self):
        """Set up test case with transaction"""
        super().setUp()
        self._setup_transaction()

    def tearDown(self):
        """Clean up transaction"""
        self._cleanup_transaction()
        super().tearDown()

    def _setup_transaction(self):
        """Set up database transaction"""
        # Mock transaction setup
        pass

    def _cleanup_transaction(self):
        """Clean up database transaction"""
        # Mock transaction cleanup
        pass


class SingleTransactionCase(TransactionCase):
    """Test case that uses a single transaction for all tests"""

    @classmethod
    def setUpClass(cls):
        """Set up class-level transaction"""
        super().setUpClass()
        cls._setup_class_transaction()

    @classmethod
    def tearDownClass(cls):
        """Clean up class-level transaction"""
        cls._cleanup_class_transaction()
        super().tearDownClass()

    @classmethod
    def _setup_class_transaction(cls):
        """Set up class-level transaction"""
        pass

    @classmethod
    def _cleanup_class_transaction(cls):
        """Clean up class-level transaction"""
        pass


class AsyncTestCase(BaseTestCase):
    """Base async test case"""

    def setUp(self):
        """Set up async test case"""
        super().setUp()
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def tearDown(self):
        """Clean up async test case"""
        self.loop.close()
        super().tearDown()

    async def asyncSetUp(self):
        """Async setup method"""
        await self._setup_async_environment()

    async def asyncTearDown(self):
        """Async teardown method"""
        await self._cleanup_async_environment()

    async def _setup_async_environment(self):
        """Set up async test environment"""
        # Create mock environment
        self.env = MagicMock()
        self.env.__getitem__ = MagicMock()

    async def _cleanup_async_environment(self):
        """Clean up async test environment"""
        pass


class AsyncTransactionCase(AsyncTestCase):
    """Async test case with transaction support"""

    async def asyncSetUp(self):
        """Set up async test case with transaction"""
        await super().asyncSetUp()
        await self._setup_async_transaction()

    async def asyncTearDown(self):
        """Clean up async transaction"""
        await self._cleanup_async_transaction()
        await super().asyncTearDown()

    async def _setup_async_transaction(self):
        """Set up async database transaction"""
        # Mock async transaction setup
        pass

    async def _cleanup_async_transaction(self):
        """Clean up async database transaction"""
        # Mock async transaction cleanup
        pass


class AsyncSingleTransactionCase(AsyncTransactionCase):
    """Async test case that uses a single transaction for all tests"""

    @classmethod
    async def asyncSetUpClass(cls):
        """Set up class-level async transaction"""
        await cls._setup_async_class_transaction()

    @classmethod
    async def asyncTearDownClass(cls):
        """Clean up class-level async transaction"""
        await cls._cleanup_async_class_transaction()

    @classmethod
    async def _setup_async_class_transaction(cls):
        """Set up class-level async transaction"""
        pass

    @classmethod
    async def _cleanup_async_class_transaction(cls):
        """Clean up class-level async transaction"""
        pass


# Test utilities and helpers


def create_test_registry(name: str = None) -> AppRegistry:
    """Create a test registry for testing"""
    if name is None:
        name = f"test_registry_{id(object())}"

    registry = AppRegistry(name)
    return registry


async def create_test_environment(registry_name: str = None) -> Environment:
    """Create a test environment"""
    if registry_name is None:
        registry_name = f"test_env_{id(object())}"

    # Create mock environment
    env = MagicMock(spec=Environment)
    env.registry_name = registry_name
    return env


def mock_addon_manager() -> AddonManager:
    """Create a mock addon manager for testing"""
    manager = MagicMock(spec=AddonManager)
    manager.get_installed_addons.return_value = []
    manager.get_addon_manifest.return_value = {}
    return manager


class AsyncTestEnvironment:
    """Async test environment for testing"""

    def __init__(self, registry_name: str = None):
        self.registry_name = registry_name or f"test_env_{id(self)}"
        self.env = MagicMock()
        self.env.__getitem__ = MagicMock()

    async def __aenter__(self):
        return self.env

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass
