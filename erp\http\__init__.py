"""
HTTP routing system for ERP
Provides modular, extensible HTTP routing with Odoo-style decorators and JSON RPC 2.0 support
"""

# Adapters
from .adapters import BaseRouteAdapter, FastAPIResponseTransformer, FastAPIRouteAdapter

# Authentication
from .auth import AuthMiddleware
from .auth import AuthType as AuthTypeEnum
from .auth import require_auth

# Controllers
from .controllers import (
    APIController,
    BaseController,
    Controller,
    ControllerRegistry,
    DatabaseController,
    MinimalController,
    TemplateController,
    get_controller_registry,
)

# Core functionality
from .core import JsonRpcError, JsonRpcHandler, JsonRpcRequest, JsonRpcResponse

# Decorators
from .decorators import route, systemRoute

# Integration
from .integration import setup_http_routes

# Core interfaces and types
from .interfaces import (
    IHealthCheck,
    IMiddleware,
    IRouteAdapter,
    IRouteDiscovery,
    IRouteHandler,
    IRouteLifecycle,
    IRouteRegistry,
    IRouteValidator,
    MiddlewareError,
    RouteInfo,
    RouteNotFoundError,
    RouteRegistrationError,
    RouteScope,
    RouteValidationError,
)

# Metadata and enums
from .metadata import (
    AuthType,
    HttpMethod,
    RouteMetadata,
    RouteType,
    create_route_metadata,
)

# Middleware
from .middleware import (
    BaseMiddleware,
    CorsMiddleware,
    MiddlewarePipeline,
    RequestProcessingMiddleware,
    ResponseProcessingMiddleware,
    get_middleware_pipeline,
)

# Registries
from .registries import (
    DatabaseRouteManager,
    DatabaseRouteRegistry,
    SystemRouteRegistry,
    get_database_route_manager,
    get_system_route_registry,
    reset_system_route_registry,
)

# Services
from .services import (
    HealthStatus,
    RouteDiscoveryService,
    RouteHandlerFactory,
    RouteHealthChecker,
    RouteInfoFactory,
    RouteOrganizer,
    RouteValidator,
    get_health_checker,
    get_route_discovery_service,
    get_route_handler_factory,
    get_route_info_factory,
    get_route_organizer,
    get_route_validator,
)

__all__ = [
    # Core interfaces
    "IRouteRegistry",
    "IRouteHandler",
    "IRouteDiscovery",
    "IRouteValidator",
    "IMiddleware",
    "IRouteAdapter",
    "IRouteLifecycle",
    "IHealthCheck",
    "RouteInfo",
    "RouteScope",
    # Exceptions
    "RouteRegistrationError",
    "RouteValidationError",
    "RouteNotFoundError",
    "MiddlewareError",
    # Metadata and enums
    "RouteType",
    "HttpMethod",
    "AuthType",
    "RouteMetadata",
    "create_route_metadata",
    # Decorators
    "route",
    "systemRoute",
    # Registries
    "SystemRouteRegistry",
    "get_system_route_registry",
    "reset_system_route_registry",
    "DatabaseRouteRegistry",
    "DatabaseRouteManager",
    "get_database_route_manager",
    # Controllers
    "BaseController",
    "Controller",
    "MinimalController",
    "APIController",
    "TemplateController",
    "DatabaseController",
    "ControllerRegistry",
    "get_controller_registry",
    # Factories
    "RouteHandlerFactory",
    "RouteInfoFactory",
    "get_route_handler_factory",
    "get_route_info_factory",
    # Middleware
    "MiddlewarePipeline",
    "get_middleware_pipeline",
    "BaseMiddleware",
    "AuthMiddleware",
    "CorsMiddleware",
    "RequestProcessingMiddleware",
    "ResponseProcessingMiddleware",
    # Adapters
    "FastAPIRouteAdapter",
    "FastAPIResponseTransformer",
    "BaseRouteAdapter",
    # Services
    "RouteDiscoveryService",
    "RouteOrganizer",
    "get_route_discovery_service",
    "get_route_organizer",
    "RouteValidator",
    "get_route_validator",
    "RouteHealthChecker",
    "HealthStatus",
    "get_health_checker",
    # JSON RPC
    "JsonRpcHandler",
    "JsonRpcError",
    "JsonRpcRequest",
    "JsonRpcResponse",
    # Authentication
    "require_auth",
]
