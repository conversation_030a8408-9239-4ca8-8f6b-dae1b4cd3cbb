"""
Model and field definition models for base addon
"""
import os

# Import base model
from erp.models import Model
from erp import fields

# Ensure this addon is available via standardized import
from erp.addons import ensure_addon_import
addon_path = os.path.join(os.path.dirname(__file__), '..')
ensure_addon_import('base', addon_path)


class IrModel(Model):
    """Model for storing model definitions"""

    _name = 'ir.model'
    _description = 'Model'

    # Define SQL constraints for data integrity
    _sql_constraints = [
        ('unique_model', 'UNIQUE (model)', 'Model name must be unique'),
        ('check_model_format',
         "CHECK (model ~ '^[a-z][a-z0-9_]*\\.[a-z][a-z0-9_]*$')",
         'Model name must follow format: module.name'),
        ('check_state_valid',
         "CHECK (state IN ('manual', 'base'))",
         'State must be manual or base'),
        ('check_table_name_format',
         "CHECK (table_name_db ~ '^[a-z][a-z0-9_]*$')",
         'Table name must be valid SQL identifier'),
    ]

    # Override name field to be more specific
    name = fields.Char(
        string='Model Name', required=True, index=True,
        help='Technical name of the model (e.g., res.partner)'
    )
    model = fields.Char(
        string='Model', required=True, unique=True, index=True,
        help='Model technical name'
    )
    info = fields.Text(string='Information', help='Model description')
    description = fields.Text(
        string='Description', help='Model description (alias for info)'
    )
    table_name_db = fields.Char(
        string='Table Name', index=True, help='Database table name'
    )
    state = fields.Selection([
        ('manual', 'Custom Object'),
        ('base', 'Base Object'),
    ], string='Type', default='manual', required=True, index=True)

    transient = fields.Boolean(
        string='Transient Model', default=False, required=True,
        help='Whether this model is transient (temporary)'
    )

    # Computed fields
    display_name = fields.Char(
        string='Display Name', compute='_compute_display_name', store=False,
        help='Display name in format: model_name (description)'
    )
    field_count = fields.Integer(
        string='Field Count', compute='_compute_field_count', store=False,
        help='Number of fields defined for this model'
    )

    # One2many relationship to model fields
    field_ids = fields.One2Many(
        'ir.model.fields', 'model_id', string='Fields', readonly=True,
        help='Fields defined for this model'
    )

    def _compute_display_name(self):
        """Compute display name for the model"""
        for record in self:
            if record.model and record.description:
                record.display_name = f"{record.model} ({record.description})"
            elif record.model:
                record.display_name = record.model
            else:
                record.display_name = record.name or "New Model"

    def _compute_field_count(self):
        """Compute the number of fields for this model"""
        for record in self:
            record.field_count = len(record.field_ids) if record.field_ids else 0

    async def _get_model_class(self):
        """Get the actual model class for this model"""
        try:
            from erp.database.memory import get_memory_registry_manager
            from erp.database.registry import DatabaseRegistry

            # Get current database
            current_db = DatabaseRegistry._current_db
            if not current_db:
                return None

            # Get memory registry for current database
            memory_manager = get_memory_registry_manager()
            registry = await memory_manager.get_registry(current_db)
            models = await registry.get_all_models()

            return models.get(self.model)
        except Exception:
            return None


class IrModelFields(Model):
    """Model for storing field definitions"""

    _name = 'ir.model.fields'
    _description = 'Model Fields'

    # Define composite unique constraints and checks
    _sql_constraints = [
        ('unique_model_name', 'UNIQUE (model, name)',
         'Field name must be unique per model'),
        ('check_field_name_format',
         "CHECK (name ~ '^[a-z][a-z0-9_]*$')",
         'Field name must be valid Python identifier'),
        ('check_ttype_valid',
         "CHECK (ttype IN ('char', 'text', 'integer', 'float', 'boolean', "
         "'date', 'datetime', 'selection', 'many2one', 'one2many', 'many2many'))",
         'Field type must be valid'),
        ('check_state_valid',
         "CHECK (state IN ('manual', 'base'))",
         'State must be manual or base'),
        ('check_size_positive',
         "CHECK (size IS NULL OR size > 0)",
         'Size must be positive if specified'),
    ]

    # Override name field to be more specific
    name = fields.Char(
        string='Field Name', required=True, index=True,
        help='Technical name of the field'
    )
    field_description = fields.Char(
        string='Field Label', required=True,
        help='Human readable label'
    )
    help = fields.Text(string='Field Help', help='Help text for the field')
    model = fields.Char(
        string='Model', required=True, index=True,
        help='Model technical name'
    )
    model_id = fields.Many2One(
        'ir.model', string='Model', required=True, index=True,
        help='Reference to ir.model record', ondelete='cascade'
    )

    ttype = fields.Selection([
        ('char', 'Char'),
        ('text', 'Text'),
        ('integer', 'Integer'),
        ('float', 'Float'),
        ('boolean', 'Boolean'),
        ('date', 'Date'),
        ('datetime', 'Datetime'),
        ('selection', 'Selection'),
        ('many2one', 'Many2one'),
        ('one2many', 'One2many'),
        ('many2many', 'Many2many'),
    ], string='Field Type', required=True, index=True)

    required = fields.Boolean(string='Required', default=False, required=True)
    readonly = fields.Boolean(string='Readonly', default=False, required=True)
    is_indexed = fields.Boolean(string='Indexed', default=False, required=True)
    store = fields.Boolean(
        string='Store', default=True, required=True,
        help='Whether the field is stored in database'
    )
    translate = fields.Boolean(
        string='Translate', default=False, required=True,
        help='Whether the field is translatable'
    )

    size = fields.Integer(string='Size', help='Field size (for char fields)')
    digits = fields.Text(
        string='Digits',
        help='Precision and scale for float fields (e.g., "16,2")'
    )

    relation = fields.Char(
        string='Relation', index=True,
        help='Related model for relational fields'
    )
    relation_field = fields.Char(
        string='Relation Field', help='Field name in related model'
    )

    selection = fields.Text(
        string='Selection Options',
        help='Selection options as string representation'
    )
    domain = fields.Text(
        string='Domain', default='[]', help='Domain filter for the field'
    )
    context = fields.Text(
        string='Context', default='{}', help='Context for the field'
    )

    state = fields.Selection([
        ('manual', 'Custom Field'),
        ('base', 'Base Field'),
    ], string='Type', default='manual', required=True, index=True)

    # Computed fields
    display_name = fields.Char(
        string='Display Name', compute='_compute_display_name', store=False,
        help='Display name in format: model.field_name (Field Label)'
    )
    complete_name = fields.Char(
        string='Complete Name', compute='_compute_complete_name', store=False,
        help='Complete field name in format: model.field_name'
    )

    def _compute_display_name(self):
        """Compute display name for the field"""
        for record in self:
            if record.model and record.name and record.field_description:
                record.display_name = (
                    f"{record.model}.{record.name} ({record.field_description})"
                )
            elif record.model and record.name:
                record.display_name = f"{record.model}.{record.name}"
            else:
                record.display_name = record.name or "New Field"

    def _compute_complete_name(self):
        """Compute complete field name"""
        for record in self:
            if record.model and record.name:
                record.complete_name = f"{record.model}.{record.name}"
            else:
                record.complete_name = record.name or ""

    def _get_field_definition(self):
        """Get field definition for this field"""
        from erp import fields as erp_fields

        field_classes = {
            'char': erp_fields.Char,
            'text': erp_fields.Text,
            'integer': erp_fields.Integer,
            'float': erp_fields.Float,
            'boolean': erp_fields.Boolean,
            'date': erp_fields.Date,
            'datetime': erp_fields.Datetime,
            'selection': erp_fields.Selection,
            'many2one': erp_fields.Many2One,
            'one2many': erp_fields.One2Many,
            'many2many': erp_fields.Many2Many,
        }

        field_class = field_classes.get(self.ttype)
        if not field_class:
            return None

        kwargs = {
            'string': self.field_description,
            'required': self.required,
            'readonly': self.readonly,
            'help': self.help,
            'index': self.index,
            'store': self.store,
            'translate': self.translate,
        }

        # Add type-specific parameters
        if self.ttype == 'char' and self.size:
            kwargs['size'] = self.size
        elif self.ttype == 'float' and self.digits:
            # Parse digits string (e.g., "16,2" -> (16, 2))
            try:
                precision, scale = map(int, self.digits.split(','))
                kwargs['digits'] = (precision, scale)
            except (ValueError, AttributeError):
                pass
        elif self.ttype == 'selection' and self.selection:
            # Parse selection string (simplified)
            kwargs['selection'] = eval(self.selection) if self.selection else []
        elif self.ttype in ('many2one', 'one2many', 'many2many') and self.relation:
            kwargs['comodel_name'] = self.relation
            if self.ttype == 'one2many' and self.relation_field:
                kwargs['inverse_name'] = self.relation_field

        # Add domain and context if provided
        if self.domain and self.domain != '[]':
            try:
                kwargs['domain'] = eval(self.domain)
            except (ValueError, SyntaxError):
                pass

        if self.context and self.context != '{}':
            try:
                kwargs['context'] = eval(self.context)
            except (ValueError, SyntaxError):
                pass

        return field_class(**kwargs)

# The models are automatically registered via the metaclass
