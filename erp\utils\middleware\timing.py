import logging
import time
from typing import Any, Callable

from fastapi import Request

from ...logging.utils import log_structured

logger = logging.getLogger(__name__)


class TimingMiddleware:
    """Handles request timing and logs slow requests."""

    @staticmethod
    async def process_request(request: Request, call_next: Callable) -> Any:
        """Processes the request, measures time, and logs slow requests."""
        start_time = time.perf_counter()

        response = await call_next(request)

        process_time = time.perf_counter() - start_time
        response.headers["X-Process-Time"] = str(process_time)

        if process_time > 1.0:
            log_structured(
                logger,
                logging.WARNING,
                f"Slow request detected: {request.method} {request.url}",
                duration=process_time,
                method=request.method,
                url=str(request.url),
                status_code=response.status_code,
                operation="slow_request",
            )

        return response
