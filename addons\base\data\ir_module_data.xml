<?xml version="1.0" encoding="utf-8"?>
<data noupdate="1">
    
    <!-- Base Module Record -->
    <record id="module_base" model="ir.module.module">
        <field name="name">base</field>
        <field name="display_name">Base</field>
        <field name="summary">Base module providing core functionality</field>
        <field name="description">Base module providing core functionality including partners, users, groups, and essential system models</field>
        <field name="author">ERP System</field>
        <field name="version">1.0.0</field>
        <field name="category">Hidden</field>
        <field name="website"></field>
        <field name="state">installed</field>
        <field name="installable">True</field>
        <field name="auto_install">True</field>
        <field name="application">False</field>
        <field name="sort_order">1</field>
    </record>
    
</data>
