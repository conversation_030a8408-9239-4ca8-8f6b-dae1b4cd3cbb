"""
ERP Addons Package

Provides standardized addon import system ensuring erp.addons.addon_name is available
regardless of addon paths configuration.

This package also exposes the modular addon system components:
- Installers: AddonInstaller
- Managers: AddonManager, DependencyManager, AddonStateManager
- Hooks: Hook system for addon lifecycle events
- Utils: Shared utilities for addon operations
- Exceptions: Addon-specific exception classes
"""

import importlib
import importlib.util
import os
import sys
from typing import Any, Dict, Optional

from ..logging import get_logger

# Core functionality
from .core import AddonIRManager, addon_ir_manager, core_ir_population_hook
from .exceptions import (
    AddonError,
    AddonInstallationError,
    CircularDependencyError,
    DependencyError,
    MissingDependencyError,
)
from .hooks import HookContext, HookType, get_hook_registry

# Modular components (for convenience imports)
from .installers import AddonInstaller
from .managers import AddonManager, AddonStateManager, DependencyManager

# Core addon components
from .manifest import AddonManifest

__all__ = [
    # Core components
    "AddonManifest",
    "get_addon_module",
    "ensure_addon_import",
    # Core functionality
    "core_ir_population_hook",
    "AddonIRManager",
    "addon_ir_manager",
    # Installers
    "AddonInstaller",
    # Managers
    "AddonManager",
    "DependencyManager",
    "AddonStateManager",
    # Hooks
    "HookType",
    "HookContext",
    "get_hook_registry",
    # Exceptions
    "AddonError",
    "DependencyError",
    "CircularDependencyError",
    "MissingDependencyError",
    "AddonInstallationError",
]


class AddonImportManager:
    """Manages standardized addon imports for erp.addons.addon_name pattern"""

    _instance: Optional["AddonImportManager"] = None
    _addon_modules: Dict[str, Any] = {}
    _addon_paths: Dict[str, str] = {}
    _module_specs: Dict[str, Any] = {}

    def __init__(self):
        if not hasattr(self, "_initialized"):
            self._initialized = True
            self.logger = get_logger(__name__)
            self._setup_addon_namespace()
            self._register_meta_path_finder()

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def _register_meta_path_finder(self):
        """Register this manager as a meta path finder"""
        if self not in sys.meta_path:
            sys.meta_path.insert(0, self)
            self.logger.debug("Registered AddonImportManager as meta path finder")

    def find_spec(self, fullname: str, path=None, target=None):
        """Find spec for addon modules - required for meta path finder"""
        # Only handle erp.addons.* modules
        if not fullname.startswith("erp.addons."):
            return None

        # Extract addon name
        parts = fullname.split(".")
        if len(parts) != 3:  # erp.addons.addon_name
            return None

        addon_name = parts[2]

        # Check if we have this addon registered
        if addon_name in self._addon_paths:
            addon_path = self._addon_paths[addon_name]
            init_file = os.path.join(addon_path, "__init__.py")

            if os.path.exists(init_file):
                # Create and cache the spec
                spec = importlib.util.spec_from_file_location(fullname, init_file)
                if spec:
                    self._module_specs[fullname] = spec
                    self.logger.debug(f"Meta path finder: Found spec for {fullname}")
                    return spec

        # Only show debug message for actual addon attempts, not framework modules
        framework_modules = {"manager", "registry", "installer", "hooks", "manifest"}
        if addon_name not in framework_modules:
            self.logger.debug(f"Meta path finder: No spec found for {fullname}")
        return None

    def _setup_addon_namespace(self):
        """Setup the erp.addons namespace for standardized imports"""
        # Ensure erp.addons is properly set up as a namespace package
        if "erp.addons" not in sys.modules:
            # Create the namespace module
            namespace_spec = importlib.util.spec_from_loader("erp.addons", loader=None)
            if namespace_spec:
                namespace_module = importlib.util.module_from_spec(namespace_spec)
                sys.modules["erp.addons"] = namespace_module
            else:
                # Fallback: create a simple module
                import types

                namespace_module = types.ModuleType("erp.addons")
                namespace_module.__path__ = []
                sys.modules["erp.addons"] = namespace_module

    def register_addon_path(self, addon_name: str, addon_path: str):
        """Register an addon path for standardized import"""
        self._addon_paths[addon_name] = addon_path
        self.logger.debug(f"Registered addon path: {addon_name} -> {addon_path}")

        # Create the standardized module path
        module_name = f"erp.addons.{addon_name}"

        # Check if init file exists
        init_file = os.path.join(addon_path, "__init__.py")
        if not os.path.exists(init_file):
            self.logger.warning(f"Init file does not exist: {init_file}")
            return None

        self.logger.debug(f"Init file exists for {addon_name}: {init_file}")

        # Don't manually load the module here - let the import system handle it
        # through our meta path finder. Just register the path.
        return True

    def get_addon_module(self, addon_name: str) -> Optional[Any]:
        """Get an addon module by name"""
        # Try standardized import first
        module_name = f"erp.addons.{addon_name}"
        if module_name in sys.modules:
            return sys.modules[module_name]

        # Try direct addon name
        if addon_name in sys.modules:
            return sys.modules[addon_name]

        # If we have the addon path registered, try to import it
        if addon_name in self._addon_paths:
            try:
                # Use importlib to import through our meta path finder
                module = importlib.import_module(module_name)
                self._addon_modules[addon_name] = module
                return module
            except ImportError as e:
                self.logger.error(f"Failed to import {module_name}: {e}")
                return None

        return None

    def ensure_addon_import(self, addon_name: str, addon_path: str = None) -> bool:
        """Ensure an addon can be imported via erp.addons.addon_name"""
        if addon_path:
            self.register_addon_path(addon_name, addon_path)

        module = self.get_addon_module(addon_name)
        return module is not None

    def list_registered_addons(self) -> Dict[str, str]:
        """List all registered addon paths"""
        return self._addon_paths.copy()


# Global instance
_addon_import_manager = AddonImportManager()


def get_addon_module(addon_name: str) -> Optional[Any]:
    """Get an addon module by name using standardized import system"""
    return _addon_import_manager.get_addon_module(addon_name)


def ensure_addon_import(addon_name: str, addon_path: str = None) -> bool:
    """Ensure an addon can be imported via erp.addons.addon_name"""
    return _addon_import_manager.ensure_addon_import(addon_name, addon_path)
