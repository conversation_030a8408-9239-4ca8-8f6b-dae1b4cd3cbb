"""
Middleware pipeline implementation
Manages the execution order and flow of middleware components
"""

import asyncio
from collections import defaultdict
from typing import Any, Callable, Dict, List, Optional

from ...logging import get_logger
from ..interfaces import IMiddleware, MiddlewareError, RouteInfo

logger = get_logger(__name__)


class MiddlewarePipeline:
    """Pipeline for executing middleware in order"""

    def __init__(self):
        self._middleware: List[IMiddleware] = []
        self._middleware_by_priority: Dict[int, List[IMiddleware]] = defaultdict(list)
        self._sorted_middleware: Optional[List[IMiddleware]] = None
        self._lock = asyncio.Lock()

    async def add_middleware(self, middleware: IMiddleware) -> None:
        """Add middleware to the pipeline"""
        async with self._lock:
            priority = middleware.get_priority()
            self._middleware_by_priority[priority].append(middleware)
            self._middleware.append(middleware)
            self._sorted_middleware = None  # Force re-sort

            logger.debug(
                f"Added middleware {middleware.__class__.__name__} with priority {priority}"
            )

    async def remove_middleware(self, middleware: IMiddleware) -> bool:
        """Remove middleware from the pipeline"""
        async with self._lock:
            if middleware in self._middleware:
                priority = middleware.get_priority()
                self._middleware_by_priority[priority].remove(middleware)
                self._middleware.remove(middleware)
                self._sorted_middleware = None  # Force re-sort

                logger.debug(f"Removed middleware {middleware.__class__.__name__}")
                return True
            return False

    async def process_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Process request through middleware pipeline"""
        middleware_list = await self._get_sorted_middleware()

        current_request = request

        for middleware in middleware_list:
            try:
                current_request = await middleware.process_request(
                    current_request, route_info
                )
                if current_request is None:
                    raise MiddlewareError(
                        f"Middleware {middleware.__class__.__name__} returned None for request"
                    )
            except Exception as e:
                logger.error(
                    f"Error in middleware {middleware.__class__.__name__} during request processing: {e}"
                )
                raise MiddlewareError(
                    f"Request processing failed in {middleware.__class__.__name__}: {e}"
                )

        return current_request

    async def process_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Process response through middleware pipeline (in reverse order)"""
        middleware_list = await self._get_sorted_middleware()

        current_response = response

        # Process in reverse order for response
        for middleware in reversed(middleware_list):
            try:
                current_response = await middleware.process_response(
                    current_response, route_info
                )
                if current_response is None:
                    raise MiddlewareError(
                        f"Middleware {middleware.__class__.__name__} returned None for response"
                    )
            except Exception as e:
                logger.error(
                    f"Error in middleware {middleware.__class__.__name__} during response processing: {e}"
                )
                raise MiddlewareError(
                    f"Response processing failed in {middleware.__class__.__name__}: {e}"
                )

        return current_response

    async def execute_pipeline(
        self, request: Any, route_info: RouteInfo, handler: Callable
    ) -> Any:
        """Execute the complete pipeline: request -> handler -> response"""
        try:
            # Process request
            processed_request = await self.process_request(request, route_info)

            # Execute handler
            if asyncio.iscoroutinefunction(handler):
                response = await handler(processed_request)
            else:
                response = handler(processed_request)

            # Process response
            processed_response = await self.process_response(response, route_info)

            return processed_response

        except MiddlewareError:
            raise
        except Exception as e:
            logger.error(f"Error in pipeline execution for {route_info.path}: {e}")
            raise MiddlewareError(f"Pipeline execution failed: {e}")

    async def _get_sorted_middleware(self) -> List[IMiddleware]:
        """Get middleware sorted by priority"""
        if self._sorted_middleware is None:
            # Sort by priority (lower number = higher priority)
            sorted_priorities = sorted(self._middleware_by_priority.keys())
            sorted_middleware = []

            for priority in sorted_priorities:
                sorted_middleware.extend(self._middleware_by_priority[priority])

            self._sorted_middleware = sorted_middleware

        return self._sorted_middleware

    async def get_middleware_info(self) -> List[Dict[str, Any]]:
        """Get information about registered middleware"""
        middleware_list = await self._get_sorted_middleware()

        return [
            {
                "name": middleware.__class__.__name__,
                "priority": middleware.get_priority(),
                "module": middleware.__class__.__module__,
            }
            for middleware in middleware_list
        ]

    async def clear(self) -> None:
        """Clear all middleware"""
        async with self._lock:
            self._middleware.clear()
            self._middleware_by_priority.clear()
            self._sorted_middleware = None
            logger.debug("Cleared all middleware from pipeline")

    def get_stats(self) -> Dict[str, int]:
        """Get pipeline statistics"""
        return {
            "total_middleware": len(self._middleware),
            "unique_priorities": len(self._middleware_by_priority),
        }


class ConditionalMiddleware:
    """Wrapper for middleware that should only run under certain conditions"""

    def __init__(self, middleware: IMiddleware, condition: Callable[[RouteInfo], bool]):
        self.middleware = middleware
        self.condition = condition

    async def process_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Process request if condition is met"""
        if self.condition(route_info):
            return await self.middleware.process_request(request, route_info)
        return request

    async def process_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Process response if condition is met"""
        if self.condition(route_info):
            return await self.middleware.process_response(response, route_info)
        return response

    def get_priority(self) -> int:
        """Get priority from wrapped middleware"""
        return self.middleware.get_priority()


class MiddlewareGroup:
    """Group of middleware that can be enabled/disabled together"""

    def __init__(self, name: str):
        self.name = name
        self._middleware: List[IMiddleware] = []
        self._enabled = True

    def add_middleware(self, middleware: IMiddleware) -> None:
        """Add middleware to the group"""
        self._middleware.append(middleware)

    def enable(self) -> None:
        """Enable the middleware group"""
        self._enabled = True

    def disable(self) -> None:
        """Disable the middleware group"""
        self._enabled = False

    def is_enabled(self) -> bool:
        """Check if group is enabled"""
        return self._enabled

    def get_middleware(self) -> List[IMiddleware]:
        """Get middleware in the group"""
        return self._middleware.copy() if self._enabled else []


# Global middleware pipeline
_middleware_pipeline = MiddlewarePipeline()


def get_middleware_pipeline() -> MiddlewarePipeline:
    """Get the global middleware pipeline"""
    return _middleware_pipeline


async def reset_middleware_pipeline():
    """Reset the global middleware pipeline (mainly for testing)"""
    global _middleware_pipeline
    await _middleware_pipeline.clear()
    _middleware_pipeline = MiddlewarePipeline()
