"""
Streamlined AppRegistry Route Manager
Discovers and registers routes only from already loaded addons in AppRegistry
"""

import asyncio
import inspect
import sys
from typing import Any, Dict, Optional

from ...logging import get_logger


class AppRegistryRouteManager:
    """
    Streamlined route manager for AppRegistry
    Discovers routes only from loaded addons - no filesystem scanning
    """

    def __init__(self, db_name: str, addon_manager):
        self.db_name = db_name
        self.addon_manager = addon_manager
        self._discovered_routes: Dict[str, Dict[str, Any]] = {}
        self._registered_with_fastapi = False
        self._routes_registered = False
        self._lock = asyncio.Lock()
        self._logger = get_logger(f"{__name__}.{db_name}")

    async def discover_and_register_routes(self) -> None:
        """
        Main entry point: discover routes from loaded addons and register with FastAPI
        """
        async with self._lock:
            if self._registered_with_fastapi:
                self._logger.debug(f"Routes already registered for {self.db_name}")
                return

            try:
                # Discover routes from loaded addons only
                routes = await self._discover_routes_from_loaded_addons()

                # Store discovered routes
                self._discovered_routes = routes

                if routes:
                    # Register with FastAPI
                    await self._register_routes_with_fastapi(routes)
                    self._registered_with_fastapi = True
                    self._routes_registered = True
                    self._logger.info(
                        f"Registered {len(routes)} routes for database: {self.db_name}"
                    )
                else:
                    self._logger.debug(
                        f"No routes discovered for database: {self.db_name}"
                    )

            except Exception as e:
                self._logger.error(f"Route registration failed for {self.db_name}: {e}")
                raise

    async def _discover_routes_from_loaded_addons(self) -> Dict[str, Dict[str, Any]]:
        """
        Discover routes only from addons already loaded in addon_manager
        No filesystem scanning, no additional imports
        """
        routes = {}

        # Get loaded addons from addon manager (single source of truth)
        addon_load_order = getattr(self.addon_manager, "addon_load_order", [])

        if not addon_load_order:
            self._logger.debug(f"No loaded addons found for {self.db_name}")
            return routes

        self._logger.debug(
            f"Discovering routes from {len(addon_load_order)} loaded addons"
        )

        for addon_name in addon_load_order:
            try:
                addon_routes = self._extract_routes_from_loaded_addon(addon_name)
                routes.update(addon_routes)

                if addon_routes:
                    self._logger.debug(
                        f"Found {len(addon_routes)} routes in addon: {addon_name}"
                    )

            except Exception as e:
                self._logger.warning(
                    f"Failed to extract routes from addon {addon_name}: {e}"
                )

        return routes

    def _extract_routes_from_loaded_addon(
        self, addon_name: str
    ) -> Dict[str, Dict[str, Any]]:
        """
        Extract routes from a single loaded addon module
        Only processes already imported modules
        """
        routes = {}

        # Get the main addon module (should already be imported)
        main_module_name = f"erp.addons.{addon_name}"
        if main_module_name not in sys.modules:
            self._logger.debug(
                f"Addon module {main_module_name} not imported, skipping"
            )
            return routes

        # Scan all loaded modules that belong to this addon
        addon_prefix = f"erp.addons.{addon_name}"
        for module_name, module in sys.modules.items():
            if module and module_name.startswith(addon_prefix):
                module_routes = self._extract_routes_from_module(module, module_name)
                routes.update(module_routes)

        return routes

    def _extract_routes_from_module(
        self, module: Any, module_name: str
    ) -> Dict[str, Dict[str, Any]]:
        """
        Extract routes from a loaded module
        Handles both function-based routes and controller-based routes
        """
        routes = {}

        try:
            # Scan all objects in the module
            for name, obj in inspect.getmembers(module):
                if name.startswith("_"):
                    continue

                # Check for function-based routes
                if inspect.isfunction(obj) and hasattr(obj, "_route_metadata"):
                    route_info = self._create_route_info_from_function(obj, module_name)
                    if route_info:
                        routes[route_info["path"]] = route_info

                # Check for controller classes
                elif inspect.isclass(obj) and self._is_controller_class(obj):
                    controller_routes = self._extract_routes_from_controller(
                        obj, module_name
                    )
                    routes.update(controller_routes)

        except Exception as e:
            self._logger.warning(
                f"Error extracting routes from module {module_name}: {e}"
            )

        return routes

    def _create_route_info_from_function(
        self, func: Any, module_name: str
    ) -> Optional[Dict[str, Any]]:
        """Create route info from a decorated function"""
        try:
            metadata = func._route_metadata

            # Skip system routes (they're handled separately)
            if getattr(func, "_is_system_route", False):
                return None

            return {
                "handler": func,
                "path": metadata["path"],
                "methods": metadata.get("methods", ["GET"]),
                "type": metadata.get("type", "http"),
                "auth": metadata.get("auth", "user"),
                "source": module_name,
                "metadata": metadata,
            }
        except Exception as e:
            self._logger.debug(
                f"Failed to create route info for function {func.__name__}: {e}"
            )
            return None

    def _is_controller_class(self, cls: type) -> bool:
        """Check if a class is a controller class"""
        # Simple heuristic: class has methods with route metadata
        for method_name, method in inspect.getmembers(cls, inspect.isfunction):
            if hasattr(method, "_route_metadata"):
                return True
        return False

    def _extract_routes_from_controller(
        self, controller_class: type, module_name: str
    ) -> Dict[str, Dict[str, Any]]:
        """Extract routes from a controller class"""
        routes = {}

        try:
            for method_name, method in inspect.getmembers(
                controller_class, inspect.isfunction
            ):
                if hasattr(method, "_route_metadata"):
                    metadata = method._route_metadata

                    # Skip system routes
                    if getattr(method, "_is_system_route", False):
                        continue

                    # Create controller handler
                    handler = self._create_controller_handler(
                        controller_class, method, metadata
                    )

                    route_info = {
                        "handler": handler,
                        "path": metadata["path"],
                        "methods": metadata.get("methods", ["GET"]),
                        "type": metadata.get("type", "http"),
                        "auth": metadata.get("auth", "user"),
                        "source": f"{module_name}.{controller_class.__name__}",
                        "metadata": metadata,
                        "controller_class": controller_class,
                        "controller_method": method_name,
                    }

                    routes[route_info["path"]] = route_info

        except Exception as e:
            self._logger.warning(
                f"Error extracting routes from controller {controller_class.__name__}: {e}"
            )

        return routes

    def _create_controller_handler(
        self, controller_class: type, method: Any, metadata: Dict[str, Any]
    ) -> Any:
        """Create a handler for controller method"""

        async def controller_handler(*args, **kwargs):
            # Create controller instance
            controller_instance = controller_class()

            # Call the method
            if inspect.iscoroutinefunction(method):
                return await method(controller_instance, *args, **kwargs)
            else:
                return method(controller_instance, *args, **kwargs)

        # Copy metadata to handler
        controller_handler._route_metadata = metadata
        controller_handler.__name__ = f"{controller_class.__name__}.{method.__name__}"

        return controller_handler

    async def _register_routes_with_fastapi(
        self, routes: Dict[str, Dict[str, Any]]
    ) -> None:
        """Register discovered routes with FastAPI application"""
        try:
            from ...http.integration.fastapi import RouteIntegration
            from .registry_manager import MemoryRegistryManager

            app = getattr(MemoryRegistryManager, "_app", None)
            if not app:
                self._logger.debug(f"No FastAPI app available for {self.db_name}")
                return

            # Register routes with FastAPI
            RouteIntegration._register_routes_from_registry(
                app, routes, f"db_{self.db_name}"
            )
            self._logger.info(
                f"Registered {len(routes)} routes with FastAPI for {self.db_name}"
            )

        except Exception as e:
            self._logger.error(f"FastAPI registration failed for {self.db_name}: {e}")
            raise

    async def refresh_routes(self) -> None:
        """Refresh routes from loaded addons"""
        async with self._lock:
            self._discovered_routes.clear()
            self._registered_with_fastapi = False
            self._routes_registered = False
            self._routes_registered = False
            await self.discover_and_register_routes()

    def get_discovered_routes(self) -> Dict[str, Dict[str, Any]]:
        """Get currently discovered routes"""
        return self._discovered_routes.copy()

    async def clear_routes(self) -> None:
        """Clear all discovered routes"""
        async with self._lock:
            self._discovered_routes.clear()
            self._registered_with_fastapi = False
