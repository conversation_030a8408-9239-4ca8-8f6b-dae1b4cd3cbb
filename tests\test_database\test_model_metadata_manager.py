"""
Test suite for ModelMetadataManager functionality

This module tests:
- Model metadata storage and retrieval
- Field metadata management
- Metadata statistics and operations
"""

import pytest

from erp.database.memory import ModelMetadataManager


class TestModelMetadataManager:
    """Test ModelMetadataManager functionality"""

    def test_metadata_manager_initialization(self):
        """Test ModelMetadataManager initialization"""
        manager = ModelMetadataManager("test_db")

        assert manager.db_name == "test_db"
        assert manager.models == {}
        assert manager.model_fields == {}

    @pytest.mark.asyncio
    async def test_set_model_data(self):
        """Test setting model data"""
        manager = ModelMetadataManager("test_db")

        model_data = {
            "name": "Test Model",
            "info": "Test model description",
            "fields": {
                "field1": {
                    "name": "field1",
                    "field_description": "Field 1",
                    "ttype": "char",
                }
            },
        }

        await manager.set_model_data("test.model", model_data)

        # Check model metadata
        assert "test.model" in manager.models
        assert manager.models["test.model"]["name"] == "Test Model"
        assert manager.models["test.model"]["info"] == "Test model description"

        # Check field metadata
        assert "test.model" in manager.model_fields
        assert "field1" in manager.model_fields["test.model"]

    @pytest.mark.asyncio
    async def test_get_model_data(self):
        """Test getting model data"""
        manager = ModelMetadataManager("test_db")

        # Set up test data
        manager.models["test.model"] = {
            "name": "Test Model",
            "model": "test.model",
            "info": "Test info",
        }

        result = await manager.get_model_data("test.model")
        assert result is not None
        assert result["name"] == "Test Model"
        assert result["info"] == "Test info"

    @pytest.mark.asyncio
    async def test_get_model_data_nonexistent(self):
        """Test getting data for nonexistent model"""
        manager = ModelMetadataManager("test_db")

        result = await manager.get_model_data("nonexistent.model")
        assert result is None

    @pytest.mark.asyncio
    async def test_get_all_models_metadata(self):
        """Test getting all models metadata"""
        manager = ModelMetadataManager("test_db")

        # Set up test data
        manager.models = {"model1": {"name": "Model 1"}, "model2": {"name": "Model 2"}}

        result = await manager.get_all_models_metadata()
        assert len(result) == 2
        assert "model1" in result
        assert "model2" in result

    @pytest.mark.asyncio
    async def test_get_all_fields_metadata(self):
        """Test getting all fields metadata"""
        manager = ModelMetadataManager("test_db")

        # Set up test data
        manager.model_fields = {
            "model1": {"field1": {"name": "field1"}},
            "model2": {"field2": {"name": "field2"}},
        }

        result = await manager.get_all_fields_metadata()
        assert len(result) == 2
        assert "model1" in result
        assert "model2" in result
        assert "field1" in result["model1"]
        assert "field2" in result["model2"]

    def test_get_metadata_stats(self):
        """Test getting metadata statistics"""
        manager = ModelMetadataManager("test_db")

        # Set up test data
        manager.models = {"model1": {}, "model2": {}}
        manager.model_fields = {
            "model1": {"field1": {}, "field2": {}},
            "model2": {"field3": {}},
        }

        stats = manager.get_metadata_stats()

        assert "models_count" in stats
        assert "total_fields_count" in stats
        assert stats["models_count"] == 2
        assert stats["total_fields_count"] == 3

    def test_clear_metadata(self):
        """Test clearing metadata"""
        manager = ModelMetadataManager("test_db")

        # Set up test data
        manager.models = {"model1": {}}
        manager.model_fields = {"model1": {"field1": {}}}

        manager.clear_metadata()

        assert manager.models == {}
        assert manager.model_fields == {}

    @pytest.mark.asyncio
    async def test_model_metadata_with_field_list(self):
        """Test setting model data with field list instead of dict"""
        manager = ModelMetadataManager("test_db")

        model_data = {
            "name": "Test Model",
            "fields": ["field1", "field2", "field3"],  # List format
        }

        await manager.set_model_data("test.model", model_data)

        # Check that fields were converted to dict format
        assert "test.model" in manager.model_fields
        fields = manager.model_fields["test.model"]
        assert len(fields) == 3
        assert "field1" in fields
        assert "field2" in fields
        assert "field3" in fields

        # Check field structure
        assert fields["field1"]["name"] == "field1"
        assert fields["field1"]["ttype"] == "char"
