"""
Error detection and response creation module
Combines route type detection and error response functionality
"""

from .error_responses import ErrorResponseFactory, create_appropriate_error_response

# Import from the actual implementation modules
from .route_detection import RouteTypeDetector, detect_route_type_from_request

# Re-export all the required classes and functions
__all__ = [
    "RouteTypeDetector",
    "ErrorResponseFactory",
    "detect_route_type_from_request",
    "create_appropriate_error_response",
]
