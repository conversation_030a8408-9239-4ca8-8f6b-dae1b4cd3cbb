"""
Test suite for addon manager functionality

This module tests:
- AddonManager initialization and state management
- Dependency resolution and validation
- Installation order calculation
- Addon lifecycle management
- Error handling for dependency issues
"""

from unittest.mock import MagicMock

import pytest

from erp.addons import (
    AddonInfo,
    AddonManager,
    AddonState,
    CircularDependencyError,
    DependencyError,
    MissingDependencyError,
)


class TestAddonManager:
    """Test AddonManager functionality"""

    def test_addon_manager_initialization(self):
        """Test AddonManager initialization"""
        manager = AddonManager()

        assert hasattr(manager, "_addon_states")
        assert hasattr(manager, "_dependency_graph")
        assert hasattr(manager, "_reverse_dependency_graph")
        assert isinstance(manager._addon_states, dict)

    def test_addon_state_enum(self):
        """Test AddonState enum values"""
        assert AddonState.UNINSTALLED.value == "uninstalled"
        assert AddonState.INSTALLED.value == "installed"
        assert AddonState.TO_INSTALL.value == "to_install"
        assert AddonState.TO_UPGRADE.value == "to_upgrade"
        assert AddonState.TO_REMOVE.value == "to_remove"
        assert AddonState.BROKEN.value == "broken"

    def test_addon_info_dataclass(self):
        """Test AddonInfo dataclass"""
        manifest = MagicMock()
        manifest.depends = ["base"]

        addon_info = AddonInfo(
            name="test_addon", manifest=manifest, state=AddonState.UNINSTALLED
        )

        assert addon_info.name == "test_addon"
        assert addon_info.state == AddonState.UNINSTALLED
        assert addon_info.dependencies == ["base"]
        assert addon_info.dependents == []

    def test_dependency_error_exceptions(self):
        """Test dependency-related exceptions"""
        # Test base DependencyError
        with pytest.raises(DependencyError):
            raise DependencyError("Test dependency error")

        # Test CircularDependencyError
        with pytest.raises(CircularDependencyError):
            raise CircularDependencyError("Circular dependency detected")

        # Test MissingDependencyError
        with pytest.raises(MissingDependencyError):
            raise MissingDependencyError("Missing dependency")

    def test_check_dependencies_valid(self):
        """Test dependency checking with valid dependencies"""
        manager = AddonManager()

        # Setup mock addon states
        base_manifest = MagicMock()
        base_manifest.depends = []
        base_manifest.installable = True

        test_manifest = MagicMock()
        test_manifest.depends = ["base"]
        test_manifest.installable = True

        manager._addon_states = {
            "base": AddonInfo("base", base_manifest, AddonState.INSTALLED),
            "test_addon": AddonInfo(
                "test_addon", test_manifest, AddonState.UNINSTALLED
            ),
        }

        is_valid, errors = manager.check_dependencies("test_addon")

        assert is_valid is True
        assert len(errors) == 0

    def test_check_dependencies_missing(self):
        """Test dependency checking with missing dependencies"""
        manager = AddonManager()

        test_manifest = MagicMock()
        test_manifest.depends = ["missing_addon"]
        test_manifest.installable = True

        manager._addon_states = {
            "test_addon": AddonInfo("test_addon", test_manifest, AddonState.UNINSTALLED)
        }

        is_valid, errors = manager.check_dependencies("test_addon")

        assert is_valid is False
        assert len(errors) > 0
        assert any("missing_addon" in error for error in errors)

    def test_get_install_order_simple(self):
        """Test getting installation order for simple dependencies"""
        manager = AddonManager()

        # Setup addon states
        base_manifest = MagicMock()
        base_manifest.depends = []

        test_manifest = MagicMock()
        test_manifest.depends = ["base"]

        manager._addon_states = {
            "base": AddonInfo("base", base_manifest, AddonState.UNINSTALLED),
            "test_addon": AddonInfo(
                "test_addon", test_manifest, AddonState.UNINSTALLED
            ),
        }

        # Build dependency graph
        manager._build_dependency_graph()

        install_order = manager.get_install_order(["test_addon"])

        assert "base" in install_order
        assert "test_addon" in install_order
        assert install_order.index("base") < install_order.index("test_addon")

    def test_get_install_order_circular_dependency(self):
        """Test circular dependency detection"""
        manager = AddonManager()

        # Setup circular dependency: addon1 -> addon2 -> addon1
        addon1_manifest = MagicMock()
        addon1_manifest.depends = ["addon2"]

        addon2_manifest = MagicMock()
        addon2_manifest.depends = ["addon1"]

        manager._addon_states = {
            "addon1": AddonInfo("addon1", addon1_manifest, AddonState.UNINSTALLED),
            "addon2": AddonInfo("addon2", addon2_manifest, AddonState.UNINSTALLED),
        }

        # Build dependency graph
        manager._build_dependency_graph()

        with pytest.raises(CircularDependencyError):
            manager.get_install_order(["addon1"])

    @pytest.mark.asyncio
    async def test_install_addon_not_found(self):
        """Test installing non-existent addon"""
        manager = AddonManager()

        result = await manager.install_addon("nonexistent_addon")

        assert result is False

    @pytest.mark.asyncio
    async def test_install_addon_already_installed(self):
        """Test installing already installed addon"""
        manager = AddonManager()

        manifest = MagicMock()
        manifest.installable = True

        manager._addon_states = {
            "test_addon": AddonInfo("test_addon", manifest, AddonState.INSTALLED)
        }

        result = await manager.install_addon("test_addon")

        assert result is True  # Should return True for already installed

    @pytest.mark.asyncio
    async def test_install_addon_not_installable(self):
        """Test installing non-installable addon"""
        manager = AddonManager()

        manifest = MagicMock()
        manifest.installable = False

        manager._addon_states = {
            "test_addon": AddonInfo("test_addon", manifest, AddonState.UNINSTALLED)
        }

        result = await manager.install_addon("test_addon")

        assert result is False
