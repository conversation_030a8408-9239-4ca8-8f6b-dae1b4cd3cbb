"""
Base route adapter implementation
Provides common functionality for framework adapters
"""

from abc import abstractmethod
from typing import Any, Dict, List

from ...logging import get_logger
from ..interfaces import IRouteAdapter, RouteInfo

logger = get_logger(__name__)


class BaseRouteAdapter(IRouteAdapter):
    """Base implementation of route adapter"""

    def __init__(self, framework_name: str):
        self.framework_name = framework_name
        self.logger = get_logger(f"{__name__}.{framework_name}")

    @abstractmethod
    async def adapt_route(self, route_info: RouteInfo) -> Any:
        """Adapt route for specific framework"""
        pass

    def supports_framework(self, framework: str) -> bool:
        """Check if adapter supports the framework"""
        return framework.lower() == self.framework_name.lower()

    def _validate_route_info(self, route_info: RouteInfo) -> None:
        """Validate route info before adaptation"""
        if not route_info.path:
            raise ValueError("Route path cannot be empty")

        if not route_info.handler:
            raise ValueError("Route handler cannot be None")

        if not route_info.methods:
            raise ValueError("Route must have at least one method")

    def _normalize_path(self, path: str) -> str:
        """Normalize route path"""
        # Ensure path starts with /
        if not path.startswith("/"):
            path = "/" + path

        # Remove trailing slash unless it's the root path
        if len(path) > 1 and path.endswith("/"):
            path = path.rstrip("/")

        return path

    def _normalize_methods(self, methods: List[str]) -> List[str]:
        """Normalize HTTP methods"""
        return [method.upper() for method in methods]

    def _extract_tags(self, route_info: RouteInfo) -> List[str]:
        """Extract tags for route organization"""
        tags = []

        # Add scope-based tag
        tags.append(f"{route_info.scope.value}")

        # Add source-based tag if available
        if route_info.source:
            tags.append(f"source:{route_info.source}")

        # Add custom tags from metadata
        custom_tags = route_info.metadata.get("tags", [])
        if custom_tags:
            tags.extend(custom_tags)

        return tags

    def _extract_summary(self, route_info: RouteInfo) -> str:
        """Extract route summary"""
        # Try to get summary from metadata
        summary = route_info.metadata.get("summary")
        if summary:
            return summary

        # Try to get from handler docstring
        if route_info.handler and hasattr(route_info.handler, "__doc__"):
            doc = route_info.handler.__doc__
            if doc:
                # Use first line of docstring
                return doc.strip().split("\n")[0]

        # Generate default summary
        return f"{' '.join(route_info.methods)} {route_info.path}"

    def _extract_description(self, route_info: RouteInfo) -> str:
        """Extract route description"""
        # Try to get description from metadata
        description = route_info.metadata.get("description")
        if description:
            return description

        # Try to get from handler docstring
        if route_info.handler and hasattr(route_info.handler, "__doc__"):
            doc = route_info.handler.__doc__
            if doc:
                return doc.strip()

        return ""


class RouteAdapterRegistry:
    """Registry for route adapters"""

    def __init__(self):
        self._adapters: Dict[str, IRouteAdapter] = {}

    def register_adapter(self, framework: str, adapter: IRouteAdapter) -> None:
        """Register a route adapter"""
        self._adapters[framework.lower()] = adapter
        logger.debug(f"Registered route adapter for {framework}")

    def get_adapter(self, framework: str) -> IRouteAdapter:
        """Get route adapter for framework"""
        adapter = self._adapters.get(framework.lower())
        if not adapter:
            raise ValueError(f"No adapter registered for framework: {framework}")
        return adapter

    def get_supported_frameworks(self) -> List[str]:
        """Get list of supported frameworks"""
        return list(self._adapters.keys())

    def unregister_adapter(self, framework: str) -> bool:
        """Unregister route adapter"""
        framework_key = framework.lower()
        if framework_key in self._adapters:
            del self._adapters[framework_key]
            logger.debug(f"Unregistered route adapter for {framework}")
            return True
        return False


# Global adapter registry
_adapter_registry = RouteAdapterRegistry()


def get_adapter_registry() -> RouteAdapterRegistry:
    """Get the global adapter registry"""
    return _adapter_registry
