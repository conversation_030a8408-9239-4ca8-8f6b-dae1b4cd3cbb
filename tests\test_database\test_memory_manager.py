"""
Test suite for memory registry manager functionality

This module tests:
- MemoryRegistryManager singleton behavior
- Registry creation and lifecycle
- Base module validation
- Database name validation
- Registry cleanup and management
"""

from unittest.mock import patch

import pytest

from erp.database import MemoryRegistryManager


class TestMemoryRegistryManager:
    """Test MemoryRegistryManager functionality"""

    @pytest.mark.asyncio
    async def test_registry_manager_singleton_behavior(self):
        """Test that registry manager behaves as singleton"""
        # Clear existing registries
        MemoryRegistryManager._registries.clear()

        with patch.object(
            MemoryRegistryManager, "_is_base_module_installed", return_value=True
        ):
            registry1 = await MemoryRegistryManager.get_registry("test_db")
            registry2 = await MemoryRegistryManager.get_registry("test_db")

            assert registry1 is registry2
            assert len(MemoryRegistryManager._registries) == 1

    @pytest.mark.asyncio
    async def test_registry_manager_base_module_check(self):
        """Test that registry creation requires base module"""
        MemoryRegistryManager._registries.clear()

        with patch.object(
            MemoryRegistryManager, "_is_base_module_installed", return_value=False
        ):
            with pytest.raises(RuntimeError, match="Base module is not installed"):
                await MemoryRegistryManager.get_registry("test_db")

    @pytest.mark.asyncio
    async def test_registry_manager_invalid_db_name(self):
        """Test that invalid database names are rejected"""
        with pytest.raises(ValueError, match="Database name cannot be empty"):
            await MemoryRegistryManager.get_registry("")

        with pytest.raises(ValueError, match="Database name cannot be empty"):
            await MemoryRegistryManager.get_registry(None)
