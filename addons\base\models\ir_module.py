"""
Module management models for base addon
"""
import os

# Import base model - use sync version for compatibility
from erp.models import Model
from erp import fields

# Ensure this addon is available via standardized import
from erp.addons import ensure_addon_import
addon_path = os.path.join(os.path.dirname(__file__), '..')
ensure_addon_import('base', addon_path)


class IrModuleModule(Model):
    """Model for managing addon modules"""

    _name = 'ir.module.module'
    _description = 'Module'

    # Override name field to be the technical name
    name = fields.Char(string='Technical Name', required=True, unique=True, index=True, help='Technical name of the module')
    display_name = fields.Char(string='Display Name', required=True, index=True, help='Human readable name')
    summary = fields.Char(string='Summary', help='Short description of the module')
    description = fields.Text(string='Description', help='Detailed description of the module')
    author = fields.Char(string='Author', index=True, help='Module author')
    version = fields.Char(string='Version', help='Module version')
    category = fields.Char(string='Category', index=True, help='Module category')
    website = fields.Char(string='Website', help='Module website')

    state = fields.Selection([
        ('uninstalled', 'Not Installed'),
        ('installed', 'Installed'),
        ('to_upgrade', 'To be upgraded'),
        ('to_remove', 'To be removed'),
        ('to_install', 'To be installed'),
    ], string='Status', default='uninstalled', required=True, index=True)

    installable = fields.Boolean(string='Installable', default=True, required=True,
                         help='Whether this module is installable or not')
    auto_install = fields.Boolean(string='Automatic Installation', default=False, required=True,
                          help='An auto-installable module is automatically installed when all its dependencies are satisfied')
    application = fields.Boolean(string='Application', default=False, required=True, index=True,
                         help='Whether this module is an application or not')
    sort_order = fields.Integer(string='Sort Order', default=100, required=True,
                      help='Sort order for ordering modules')

    actionAt = fields.Datetime(string='Last Action', readonly=True,
                       help='Timestamp of the last action performed on this module')

    def button_install(self):
        """Install the module"""
        from datetime import datetime
        self.write({'state': 'to_install', 'actionAt': datetime.now()})

    def button_uninstall(self):
        """Uninstall the module"""
        from datetime import datetime
        self.write({'state': 'to_remove', 'actionAt': datetime.now()})

    def button_upgrade(self):
        """Upgrade the module"""
        from datetime import datetime
        self.write({'state': 'to_upgrade', 'actionAt': datetime.now()})

    def button_immediate_install(self):
        """Install the module immediately"""
        from datetime import datetime
        self.write({'state': 'installed', 'actionAt': datetime.now()})

    def button_immediate_uninstall(self):
        """Uninstall the module immediately"""
        from datetime import datetime
        self.write({'state': 'uninstalled', 'actionAt': datetime.now()})

    @classmethod
    def get_installed_modules(cls):
        """Get list of installed modules"""
        return cls.search([('state', '=', 'installed')])

    @classmethod
    def get_installable_modules(cls):
        """Get list of installable modules"""
        return cls.search([('installable', '=', True)])

    def install_dependencies(self):
        """Install module dependencies (placeholder)"""
        # In a real implementation, this would parse dependencies
        # and install them recursively
        pass

# The model is automatically registered via the metaclass
