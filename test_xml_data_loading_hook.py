#!/usr/bin/env python3
"""
Test script to validate the XML data loading hook system.

This script tests:
1. XML data loading hook registration
2. Hook execution order (IR population before XML data loading)
3. Error handling and rollback support
4. Directory organization validation
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from erp.addons.hooks import get_hook_registry, HookType
from erp.addons.hooks.hook_context import HookContext
from erp.logging import get_logger

logger = get_logger(__name__)


async def test_hook_registration():
    """Test that both IR population and XML data loading hooks are registered."""
    logger.info("Testing hook registration...")
    
    hook_registry = get_hook_registry()
    post_install_hooks = hook_registry.get_hooks(HookType.POST_INSTALL)
    
    # Find our hooks
    ir_hook = None
    xml_hook = None
    
    for hook in post_install_hooks:
        if hasattr(hook.func, '__name__'):
            if hook.func.__name__ == 'core_ir_population_hook':
                ir_hook = hook
            elif hook.func.__name__ == 'core_xml_data_loading_hook':
                xml_hook = hook
    
    # Validate hooks exist
    assert ir_hook is not None, "IR population hook not found"
    assert xml_hook is not None, "XML data loading hook not found"
    
    # Validate priorities (IR should run before XML)
    assert ir_hook.priority < xml_hook.priority, \
        f"IR hook priority ({ir_hook.priority}) should be lower than XML hook priority ({xml_hook.priority})"
    
    logger.info("✅ Hook registration test passed")
    return True


async def test_hook_execution_order():
    """Test that hooks execute in the correct order."""
    logger.info("Testing hook execution order...")
    
    hook_registry = get_hook_registry()
    post_install_hooks = hook_registry.get_hooks(HookType.POST_INSTALL)
    
    # Sort hooks by priority (same as registry does)
    sorted_hooks = sorted(post_install_hooks, key=lambda h: h.priority)
    
    # Find positions of our hooks
    ir_position = None
    xml_position = None
    
    for i, hook in enumerate(sorted_hooks):
        if hasattr(hook.func, '__name__'):
            if hook.func.__name__ == 'core_ir_population_hook':
                ir_position = i
            elif hook.func.__name__ == 'core_xml_data_loading_hook':
                xml_position = i
    
    # Validate execution order
    assert ir_position is not None, "IR population hook not found in sorted list"
    assert xml_position is not None, "XML data loading hook not found in sorted list"
    assert ir_position < xml_position, \
        f"IR hook should execute before XML hook (positions: IR={ir_position}, XML={xml_position})"
    
    logger.info("✅ Hook execution order test passed")
    return True


def test_directory_organization():
    """Test that no unwanted directories were created in the main erp/ folder."""
    logger.info("Testing directory organization...")
    
    erp_dir = project_root / "erp"
    expected_dirs = {
        "__pycache__", "addons", "cli", "cluster", "config", "context", 
        "data", "database", "environment", "fields", "http", "logging", 
        "models", "routes", "server_config", "templates", "tests", "utils"
    }
    
    actual_dirs = {item.name for item in erp_dir.iterdir() if item.is_dir()}
    
    # Check for unexpected directories
    unexpected_dirs = actual_dirs - expected_dirs
    assert not unexpected_dirs, f"Unexpected directories found in erp/: {unexpected_dirs}"
    
    # Check that core hooks are in the right place
    core_hooks_file = erp_dir / "addons" / "core" / "core_hooks.py"
    assert core_hooks_file.exists(), "core_hooks.py not found in expected location"
    
    logger.info("✅ Directory organization test passed")
    return True


def test_base_addon_data_files():
    """Test that base addon has the correct data files."""
    logger.info("Testing base addon data files...")
    
    base_addon_dir = project_root / "addons" / "base"
    
    # Check manifest
    manifest_file = base_addon_dir / "__manifest__.py"
    assert manifest_file.exists(), "Base addon manifest not found"
    
    # Check ir_module_data.xml exists
    ir_module_data_file = base_addon_dir / "data" / "ir_module_data.xml"
    assert ir_module_data_file.exists(), "ir_module_data.xml not found"
    
    # Check manifest includes the new data file
    with open(manifest_file, 'r') as f:
        manifest_content = f.read()
    
    assert 'ir_module_data.xml' in manifest_content, \
        "ir_module_data.xml not referenced in manifest"
    
    logger.info("✅ Base addon data files test passed")
    return True


async def main():
    """Run all tests."""
    logger.info("Starting XML data loading hook system validation...")
    
    try:
        # Import the core hooks to register them
        from erp.addons.core import core_hooks
        
        # Run tests
        await test_hook_registration()
        await test_hook_execution_order()
        test_directory_organization()
        test_base_addon_data_files()
        
        logger.info("🎉 All tests passed! XML data loading hook system is working correctly.")
        return True
        
    except Exception as e:
        logger.error("❌ Test failed: %s", e)
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
