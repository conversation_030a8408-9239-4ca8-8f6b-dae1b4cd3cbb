"""
ERP Logging System - Modern Modular Architecture
Comprehensive logging module with focused, single-responsibility components

The logging system is organized into logical subdirectories:
- core/: Core system components and facade interface
- config/: Configuration system
- utils/: Utility functions and decorators
- adapters/: Adapter classes for different backends
- middleware/: Middleware system for custom processing
- monitoring/: Performance monitoring and metrics
- coordination/: Operation coordination and rate limiting
"""

# Adapters (for custom backends)
from .adapters import (
    ConsoleAdapter,
    DatabaseAdapter,
    FileAdapter,
    LoggingAdapter,
    RemoteAdapter,
)

# Configuration system
from .config import (
    ConfigLoader,
    ConfigValidator,
    FilterConfig,
    FormatterConfig,
    HandlerConfig,
    LoggerConfig,
    LoggingConfig,
    MonitoringConfig,
)
from .coordination import (
    LoggerSuppressor,
    LoggingCoordinator,
    LoggingRateLimiter,
    OperationTracker,
    get_logging_coordinator,
)

# Factory system (for custom component creation)
# Core system components (for advanced usage)
# Main facade interface (recommended for most use cases)
from .core import (
    ComponentFactoryRegistry,
    ComponentRegistry,
    FilterFactory,
    FormatterFactory,
    HandlerFactory,
    LogContext,
    LoggerFactory,
    LoggerManager,
    LoggingFacade,
    LoggingSystem,
    get_factory_registry,
    get_logger,
    get_logging_facade,
    get_logging_system,
    get_system_status,
    initialize_logging,
    log_api_calls,
    log_database_operations,
    log_method_calls,
    log_performance,
    log_security_events,
    log_structured,
    operation_context,
    quiet_operation,
    record_request,
    shutdown_logging,
    start_performance_monitoring,
    stop_performance_monitoring,
)

# Middleware system (for custom processing pipelines)
from .middleware import (
    ContextMiddleware,
    FilterMiddleware,
    LoggingMiddleware,
    LoggingPipeline,
    PerformanceMiddleware,
    SecurityMiddleware,
    get_global_pipeline,
    process_log_record,
)

# Monitoring and coordination (for advanced monitoring)
from .monitoring import (
    AlertManager,
    ApplicationMetrics,
    MetricsCollector,
    MetricsStorage,
    PerformanceMonitor,
    SystemMetrics,
)

# Utility functions and decorators
from .utils import (
    LogContext,
    log_api_calls,
    log_database_operations,
    log_method_calls,
    log_performance,
    log_security_events,
    log_structured,
)

__all__ = [
    # Main facade interface (recommended)
    "LoggingFacade",
    "get_logging_facade",
    "initialize_logging",
    "get_logger",
    "start_performance_monitoring",
    "stop_performance_monitoring",
    "record_request",
    "operation_context",
    "quiet_operation",
    "get_system_status",
    "shutdown_logging",
    # Utility functions and decorators
    "log_performance",
    "log_structured",
    "LogContext",
    "log_method_calls",
    "log_database_operations",
    "log_api_calls",
    "log_security_events",
    # Configuration system
    "LoggingConfig",
    "FormatterConfig",
    "HandlerConfig",
    "FilterConfig",
    "LoggerConfig",
    "MonitoringConfig",
    "ConfigLoader",
    "ConfigValidator",
    # Core system (advanced usage)
    "LoggingSystem",
    "ComponentRegistry",
    "LoggerManager",
    "get_logging_system",
    # Factory system (custom components)
    "ComponentFactoryRegistry",
    "FormatterFactory",
    "FilterFactory",
    "HandlerFactory",
    "LoggerFactory",
    "get_factory_registry",
    # Middleware system (custom processing)
    "LoggingPipeline",
    "LoggingMiddleware",
    "ContextMiddleware",
    "PerformanceMiddleware",
    "SecurityMiddleware",
    "FilterMiddleware",
    "get_global_pipeline",
    "process_log_record",
    # Monitoring and coordination (advanced monitoring)
    "PerformanceMonitor",
    "SystemMetrics",
    "ApplicationMetrics",
    "MetricsCollector",
    "AlertManager",
    "MetricsStorage",
    "LoggingCoordinator",
    "OperationTracker",
    "LoggerSuppressor",
    "LoggingRateLimiter",
    "get_logging_coordinator",
    # Adapters (custom backends)
    "LoggingAdapter",
    "ConsoleAdapter",
    "FileAdapter",
    "DatabaseAdapter",
    "RemoteAdapter",
]
