# ERP-BIN Command Reference

The `erp-bin` script is the primary command-line interface for the ERP system, providing comprehensive database and server management capabilities through a modular CLI architecture.

## Overview

ERP-BIN supports four main operations:
- **init**: Initialize databases with ERP system
- **start**: Start HTTP server with optional database context
- **purge**: Remove all databases (development/testing)
- **lint**: Run code linting and formatting checks

## Architecture

The ERP-BIN CLI is built on a modular architecture:
- **Modular Commands**: Each command group is implemented as a separate module
- **Backward Compatibility**: Supports legacy usage patterns automatically
- **Extensible Design**: Easy to add new commands and functionality

## Command Syntax

```bash
python erp-bin <command> [options]
python erp-bin [legacy-options]  # Backward compatibility
```

## Backward Compatibility

The new modular CLI maintains full backward compatibility with existing usage patterns:

```bash
# Legacy patterns (still supported)
python erp-bin mydb --force         # Auto-detected as: init mydb --force
python erp-bin --db mydb --reload   # Auto-detected as: start --db mydb --reload
python erp-bin                      # Auto-detected as: start
```

## Commands

### init - Database Initialization

Initialize a database with the ERP system, including base modules and data.

```bash
python erp-bin init <db_name> [options]
```

**Arguments:**
- `db_name`: Name of the database to initialize (required)

**Options:**
- `--force`: Force initialization even if database already exists
- `--demo`: Install demo data if available
- `--exit`: Exit after initialization (don't start server)
- `--no-http`: Skip HTTP server startup
- `--quiet`: Suppress banner output
- `-i, --install-addon ADDON_NAME`: Install specified addon and its dependencies during initialization

**Examples:**
```bash
# Basic initialization
python erp-bin init mycompany

# Initialize with demo data
python erp-bin init testdb --demo
python erp-bin init testdb -d            # Abbreviated form

# Initialize and exit (for scripting)
python erp-bin init proddb --exit --quiet
python erp-bin init proddb -e -q         # Abbreviated form

# Force reinitialize existing database
python erp-bin init mydb --force
python erp-bin init mydb -f              # Abbreviated form

# Initialize with specific addon and its dependencies
python erp-bin init mydb -i sale

# Initialize with addon and demo data
python erp-bin init testdb -i crm --demo

# Initialize with addon and exit (for scripting)
python erp-bin init proddb -i accounting --exit --quiet
```

**Behavior:**
1. Creates database if it doesn't exist
2. Installs base addon and dependencies
3. If `-i` option is provided, installs specified addon and all its dependencies
4. Sets up initial data and configurations
5. By default, suggests starting HTTP server with `erp-bin start`
6. Sets the database as current in configuration

**Note:** The modular architecture separates database initialization from server startup for better modularity.

### start - Server Management

Start the HTTP server with optional database context.

```bash
python erp-bin start [options]
```

**Options:**
- `--db <name>`: Database name to start with (optional)
- `--host <host>`: Host interface to bind to (overrides config)
- `--port <port>`: Port to bind to (overrides config)
- `--reload`: Enable auto-reload for development
- `--quiet`: Suppress banner output

**Examples:**
```bash
# Start without database (multi-db mode)
python erp-bin start

# Start with specific database
python erp-bin start --db mycompany

# Development server with auto-reload
python erp-bin start --db testdb --reload

# Production server on all interfaces
python erp-bin start --db proddb --host 0.0.0.0 --port 80

# Custom configuration
python erp-bin start --host ************* --port 8080 --reload
```

**Behavior:**
- Without `--db`: Starts in multi-database mode
- With `--db`: Validates database exists and is initialized
- Auto-reload watches Python files for changes
- Server accessible at configured host:port

### purge - Database Cleanup

Remove all user databases (excludes system databases).

```bash
python erp-bin purge [options]
```

**Options:**
- `--force`: Skip confirmation prompts (dangerous!)
- `--quiet`: Suppress banner output

**Examples:**
```bash
# Interactive purge with confirmations
python erp-bin purge

# Force purge (for scripts)
python erp-bin purge --force --quiet
```

**Safety Features:**
- Requires double confirmation by default
- Protects system databases (postgres, template0, template1)
- Lists databases before deletion
- Terminates active connections before deletion

### lint - Code Quality and Formatting

Run code linting and formatting checks on the codebase using multiple tools.

```bash
python erp-bin lint [options]
```

**Options:**
- `--fix`: Automatically fix issues where possible
- `--tool {flake8,isort,black,mypy,all}`: Specific tool to run (default: all)
- `--paths [PATHS ...]`: Paths to lint (default: erp/ tests/ examples/ scripts/)
- `--check-only`: Only check, do not fix (opposite of --fix)
- `--quiet`: Suppress banner output

**Examples:**
```bash
# Run all linting tools (check only)
python erp-bin lint

# Run specific tool
python erp-bin lint --tool flake8

# Fix issues automatically
python erp-bin lint --fix

# Lint specific paths
python erp-bin lint --paths erp/cli/ tests/

# Run black formatting only with fixes
python erp-bin lint --tool black --fix

# Check specific files without fixing
python erp-bin lint --tool flake8 --paths erp/cli/lint.py --check-only
```

**Supported Tools:**
- **isort**: Import sorting and organization
- **black**: Code formatting and style
- **flake8**: Style guide enforcement and error detection
- **mypy**: Static type checking
- **all**: Run all tools in sequence

**Tool Configuration:**
All tools are configured via `pyproject.toml` and `.flake8` files:
- Line length: 88 characters (consistent across tools)
- Import style: Black-compatible
- Type checking: Gradual typing with mypy

## Global Options

**Available for all commands:**
- `--version`: Show version information
- `--help`: Show help message

## Configuration Integration

ERP-BIN respects configuration from `erp.conf`:

**Database Settings:**
- `db_host`, `db_port`, `db_user`, `db_password`
- `list_db`: Enable multi-database mode
- `db_filter`: Filter accessible databases

**Server Settings:**
- `http_interface`, `http_port`
- `addons_path`
- `log_level`, `log_file`

**Override with Command Line:**
```bash
# Command line options override config file
python erp-bin start --host 0.0.0.0 --port 8080
```

## Environment Variables

Override configuration with environment variables:

```bash
# Development mode
export ERP_DEV_MODE=true

# Database connection
export ERP_DB_HOST=remote-server.com
export ERP_DB_PORT=5433
export ERP_DB_USER=myuser
export ERP_DB_PASSWORD=mypass

# Server settings
export ERP_HTTP_PORT=8080
export ERP_HTTP_INTERFACE=0.0.0.0
```

## Exit Codes

- `0`: Success
- `1`: General error
- `130`: Interrupted by user (Ctrl+C)

## Logging

ERP-BIN provides detailed logging with:
- Colored console output (when supported)
- Progress indicators and status messages
- Error details with suggestions
- Performance timing information

**Log Levels:**
- Database operations: INFO level
- Server startup: INFO level
- Errors: ERROR level with details
- Debug information: DEBUG level

## Integration Examples

**Docker Integration:**
```dockerfile
# In Dockerfile
CMD ["python", "erp-bin", "start", "--host", "0.0.0.0"]
```

**Systemd Service:**
```ini
[Unit]
Description=ERP System
After=postgresql.service

[Service]
Type=exec
User=erp
WorkingDirectory=/opt/erp
ExecStart=/opt/erp/venv/bin/python erp-bin start --db production
Restart=always

[Install]
WantedBy=multi-user.target
```

**CI/CD Pipeline:**
```bash
# Test database setup
python erp-bin init test_db --demo --exit --quiet

# Run tests
python -m pytest

# Cleanup
python erp-bin purge --force --quiet
```

## Security Considerations

- Database credentials are read from configuration
- Multi-database mode requires proper `db_filter` configuration
- Production deployments should use `--quiet` to avoid information disclosure
- Consider using environment variables for sensitive settings
- Regular database backups before using `purge` command

## Modular Architecture

The ERP-BIN CLI is built using a modular architecture for better maintainability and extensibility:

### Command Groups

- **DatabaseCommandGroup** (`erp.cli.database`): Handles `init` and `purge` commands
- **ServerCommandGroup** (`erp.cli.server`): Handles `start` command
- **Utilities** (`erp.cli.utils`): Common utilities and system functions

### Key Components

- **ERPCLIManager** (`erp.cli.manager`): Main CLI coordinator
- **CompatibilityArgumentParser** (`erp.cli.compatibility`): Backward compatibility layer
- **BaseCommand** (`erp.cli.base`): Base classes for commands

### Benefits

- **Separation of Concerns**: Each command group handles specific functionality
- **Extensibility**: Easy to add new commands and features
- **Maintainability**: Modular code is easier to test and maintain
- **Backward Compatibility**: Legacy usage patterns continue to work

## Configuration Options

### Abbreviated Arguments

All ERP-BIN commands support abbreviated argument forms for faster typing:

**Global Options:**
- `--quiet` / `-q` - Suppress banner output
- `--verbose` / `-v` - Verbose output with detailed error information
- `--version` / `-V` - Show version information

**Init Command:**
- `--force` / `-f` - Force initialization
- `--demo` / `-d` - Install demo data
- `--exit` / `-e` - Exit after initialization
- `--no-create` / `-n` - Don't create database if missing

**Start Command:**
- `--db` / `-d` - Database name
- `--host` / `-H` - Host interface
- `--port` / `-p` - Port number
- `--reload` / `-r` - Auto-reload for development

**Lint Command:**
- `--fix` / `-f` - Automatically fix issues
- `--tool` / `-t` - Specific tool to run
- `--paths` / `-P` - Paths to lint
- `--check-only` / `-c` - Only check, don't fix

**Purge Command:**
- `--force` / `-f` - Skip confirmation prompts

### Error Handling Configuration

The system supports configurable stacktrace display through `erp.conf`:

```ini
[options]
# Control whether stacktraces are shown in error responses
# Set to false to hide stacktraces in production environments
stacktrace_enabled = true
```

**Behavior:**
- When `stacktrace_enabled = true`: Full stacktraces are shown in CLI and HTTP errors
- When `stacktrace_enabled = false`: Stacktraces are hidden by default
- The `--verbose` / `-v` flag always overrides this setting and shows stacktraces
- Applies to both CLI commands and HTTP error responses (JSON RPC and HTML)

## Performance Notes

- Database initialization can take several minutes for large systems
- Auto-reload (`--reload`) has performance overhead in production
- Connection pooling is configured via `db_pool_*` settings
- Large databases may require increased timeout values
- Modular architecture reduces memory footprint by loading only needed components
