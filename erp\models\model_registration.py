"""
Model registration and management functionality
"""

from typing import Any, Dict, List, Optional, Type

from ..logging import get_logger
from .base_model import BaseModel


class ModelRegistration:
    """Handles registration and management of discovered models"""

    def __init__(self, addon_name: str):
        """
        Initialize model registration for a specific addon

        Args:
            addon_name: Name of the addon
        """
        self.addon_name = addon_name
        self.logger = get_logger(f"{__name__}.{addon_name}")
        self._models: Dict[str, Type[BaseModel]] = {}

    def register_models(self, models: Dict[str, Type[BaseModel]]) -> None:
        """
        Register discovered models

        Args:
            models: Dictionary mapping model names to model classes
        """
        self._models.update(models)
        self.logger.debug(
            f"Registered {len(models)} models for addon: {self.addon_name}"
        )

    def get_all_models(self) -> Dict[str, Type[BaseModel]]:
        """
        Get all registered models

        Returns:
            Dictionary mapping model names to model classes
        """
        return self._models.copy()

    def get_models_by_type(
        self, model_type: str = "standard"
    ) -> Dict[str, Type[BaseModel]]:
        """
        Get models filtered by type

        Args:
            model_type: Type of models to return ('standard', 'abstract', 'transient', 'all')

        Returns:
            Dictionary mapping model names to model classes of the specified type
        """
        if model_type == "all":
            return self._models.copy()

        filtered_models = {}
        for model_name, model_class in self._models.items():
            if model_type == "abstract" and getattr(model_class, "_abstract", False):
                filtered_models[model_name] = model_class
            elif model_type == "transient" and getattr(
                model_class, "_transient", False
            ):
                filtered_models[model_name] = model_class
            elif (
                model_type == "standard"
                and not getattr(model_class, "_abstract", False)
                and not getattr(model_class, "_transient", False)
            ):
                filtered_models[model_name] = model_class

        return filtered_models

    def get_model(self, model_name: str) -> Optional[Type[BaseModel]]:
        """
        Get a specific model by name

        Args:
            model_name: Technical name of the model

        Returns:
            Model class or None if not found
        """
        return self._models.get(model_name)

    def get_model_names(self) -> List[str]:
        """
        Get list of all model names

        Returns:
            List of model names
        """
        return list(self._models.keys())

    def get_model_fields(self, model_name: str) -> Dict[str, Any]:
        """
        Get all fields for a specific model

        Args:
            model_name: Technical name of the model

        Returns:
            Dictionary mapping field names to field objects
        """
        model_class = self.get_model(model_name)
        if not model_class:
            return {}

        return getattr(model_class, "_fields", {})

    def get_all_models_with_fields(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all models with their field information

        Returns:
            Dictionary mapping model names to their field dictionaries
        """
        result = {}
        for model_name, model_class in self._models.items():
            result[model_name] = {
                "class": model_class,
                "fields": getattr(model_class, "_fields", {}),
                "table": getattr(model_class, "_table", None)
                or model_name.replace(".", "_"),
                "description": getattr(model_class, "_description", None) or model_name,
            }

        return result

    def has_models(self) -> bool:
        """
        Check if any models are registered

        Returns:
            True if models are registered, False otherwise
        """
        return len(self._models) > 0

    def clear(self) -> None:
        """Clear all registered models"""
        self._models.clear()
        self.logger.debug(f"Cleared model registration for addon: {self.addon_name}")

    def model_exists(self, model_name: str) -> bool:
        """
        Check if a model exists in the registry

        Args:
            model_name: Technical name of the model

        Returns:
            True if model exists, False otherwise
        """
        return model_name in self._models

    def get_model_count(self) -> int:
        """
        Get the number of registered models

        Returns:
            Number of registered models
        """
        return len(self._models)
