"""
Many2Many relational field implementation
"""

from .base import FieldValidationError, RelationalField


class Many2Many(RelationalField):
    """Many-to-many relationship field with intersection table"""

    def __init__(
        self, comodel_name, relation=None, column1=None, column2=None, **kwargs
    ):
        """
        Initialize Many2Many field

        Args:
            comodel_name: Name of the related model
            relation: Name of the intersection table (auto-generated if not provided)
            column1: Name of the column referencing this model (auto-generated if not provided)
            column2: Name of the column referencing the comodel (auto-generated if not provided)
        """
        super().__init__(comodel_name, **kwargs)
        self.relation_table = relation
        self.column1 = column1
        self.column2 = column2
        # Many2Many fields are not stored in main table
        self.store = False

    def get_relation_table_name(self, model_name):
        """Generate intersection table name"""
        if self.relation_table:
            return self.relation_table

        # Auto-generate table name: model1_model2_rel
        model1 = model_name.replace(".", "_")
        model2 = self.comodel_name.replace(".", "_")

        # Ensure consistent ordering for bidirectional relationships
        if model1 < model2:
            return f"{model1}_{model2}_rel"
        else:
            return f"{model2}_{model1}_rel"

    def get_column_names(self, model_name):
        """Get column names for intersection table"""
        if self.column1 and self.column2:
            return self.column1, self.column2

        # Auto-generate column names
        model1_col = model_name.replace(".", "_") + "_id"
        model2_col = self.comodel_name.replace(".", "_") + "_id"

        return model1_col, model2_col

    def get_sql_type(self):
        """Many2Many fields don't have SQL representation in main table"""
        return None

    def get_intersection_table_schema(self, model_name):
        """Get schema for intersection table"""
        table_name = self.get_relation_table_name(model_name)
        col1, col2 = self.get_column_names(model_name)

        return {
            "table_name": table_name,
            "columns": {col1: "UUID NOT NULL", col2: "UUID NOT NULL"},
            "constraints": [
                f"PRIMARY KEY ({col1}, {col2})",
                f'FOREIGN KEY ({col1}) REFERENCES {model_name.replace(".", "_")} (id) ON DELETE CASCADE',
                f'FOREIGN KEY ({col2}) REFERENCES {self.comodel_name.replace(".", "_")} (id) ON DELETE CASCADE',
            ],
            "indexes": [
                f"CREATE INDEX IF NOT EXISTS idx_{table_name}_{col1} ON {table_name} ({col1})",
                f"CREATE INDEX IF NOT EXISTS idx_{table_name}_{col2} ON {table_name} ({col2})",
            ],
        }

    def _validate_value(self, value):
        """Validate Many2Many field value"""
        if value is None:
            return None

        # Value can be:
        # 1. A list of IDs
        # 2. A list of model instances
        # 3. A RecordSet
        # 4. Command tuples for operations

        if isinstance(value, list):
            # Handle command tuples or list of records/IDs
            validated_commands = []
            for item in value:
                if isinstance(item, tuple) and len(item) >= 2:
                    # Command tuple: (command, id, values)
                    command = item[0]
                    if command in (0, 1, 2, 3, 4, 5, 6):  # Valid command codes
                        validated_commands.append(item)
                    else:
                        raise FieldValidationError(f"Invalid command code: {command}")
                elif isinstance(item, str):
                    # ID string
                    validated_commands.append((4, item, 0))  # Link command
                elif isinstance(item, int):
                    # ID integer
                    validated_commands.append((4, str(item), 0))  # Link command
                elif hasattr(item, "id"):
                    # Model instance
                    validated_commands.append((4, item.id, 0))  # Link command
                else:
                    raise FieldValidationError(
                        f"Invalid item type in Many2Many list: {type(item)}"
                    )
            return validated_commands

        elif hasattr(value, "_records"):
            # RecordSet - convert to link commands
            return [(4, record.id, 0) for record in value._records]

        else:
            raise FieldValidationError(
                f"Invalid value type for Many2Many field: {type(value)}"
            )

    def convert_to_cache(self, value):
        """Convert value for caching"""
        if value is None:
            return []
        # Store as list of IDs
        if isinstance(value, list):
            ids = []
            for item in value:
                if isinstance(item, tuple) and len(item) >= 2:
                    command, record_id = item[0], item[1]
                    if command in (4, 1):  # Link or update
                        ids.append(record_id)
                    # Skip other commands for cache
                else:
                    ids.append(str(item))
            return ids
        return []

    def convert_to_record(self, value):
        """Convert cached value back to record value"""
        if not value:
            return []
        # TODO: Implement proper RecordSet creation when model registry is available
        return value
