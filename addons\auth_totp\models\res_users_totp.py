"""
TOTP Extension for res.users model

This module extends the res.users model with Time-based One-Time Password (TOTP)
functionality for two-factor authentication.
"""

import base64
import io
import secrets
from typing import Optional

try:
    import pyotp
    import qrcode
    from qrcode.image.pil import PilImage
except ImportError:
    pyotp = None
    qrcode = None
    PilImage = None

from erp.models import Model
from erp import fields
from erp.api import depends


class ResUsersTotp(Model):
    """Extend res.users with TOTP functionality"""

    _name = 'res.users'
    _inherit = 'res.users'

    # Two-factor authentication fields
    totp_secret = fields.Char(
        string='TOTP Secret', 
        size=32, 
        readonly=True,
        help='Secret key for two-factor authentication'
    )

    totp_enabled = fields.Boolean(
        string='Two-Factor Authentication', 
        default=False,
        help='Enable two-factor authentication for this user'
    )

    totp_backup_codes = fields.Text(
        string='Backup Codes',
        readonly=True,
        help='One-time backup codes for account recovery'
    )

    def generate_totp_secret(self):
        """Generate a new TOTP secret for the user"""
        if not pyotp:
            raise ImportError("pyotp library is required for TOTP functionality")
        
        secret = pyotp.random_base32()
        self.totp_secret = secret
        return secret

    def get_totp_qr_code(self, issuer_name: str = "ERP System") -> Optional[str]:
        """
        Generate QR code for TOTP setup
        
        Args:
            issuer_name: Name of the issuing organization
            
        Returns:
            Base64 encoded QR code image or None if libraries not available
        """
        if not pyotp or not qrcode or not self.totp_secret:
            return None

        # Create TOTP URI
        totp_uri = pyotp.totp.TOTP(self.totp_secret).provisioning_uri(
            name=self.email or self.login,
            issuer_name=issuer_name
        )

        # Generate QR code
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)

        # Create image
        img = qr.make_image(fill_color="black", back_color="white", image_factory=PilImage)
        
        # Convert to base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        
        return img_str

    def verify_totp_code(self, code: str) -> bool:
        """
        Verify TOTP code
        
        Args:
            code: 6-digit TOTP code
            
        Returns:
            True if code is valid, False otherwise
        """
        if not pyotp or not self.totp_secret or not self.totp_enabled:
            return False

        totp = pyotp.TOTP(self.totp_secret)
        return totp.verify(code, valid_window=1)  # Allow 1 window tolerance

    def generate_backup_codes(self, count: int = 8) -> list:
        """
        Generate backup codes for account recovery
        
        Args:
            count: Number of backup codes to generate
            
        Returns:
            List of backup codes
        """
        codes = []
        for _ in range(count):
            # Generate 8-character alphanumeric code
            code = secrets.token_hex(4).upper()
            codes.append(code)
        
        # Store codes (in real implementation, these should be hashed)
        self.totp_backup_codes = '\n'.join(codes)
        return codes

    def verify_backup_code(self, code: str) -> bool:
        """
        Verify and consume a backup code
        
        Args:
            code: Backup code to verify
            
        Returns:
            True if code is valid and consumed, False otherwise
        """
        if not self.totp_backup_codes:
            return False

        codes = self.totp_backup_codes.split('\n')
        code_upper = code.upper().strip()
        
        if code_upper in codes:
            # Remove used code
            codes.remove(code_upper)
            self.totp_backup_codes = '\n'.join(codes)
            return True
        
        return False

    async def enable_totp(self, issuer_name: str = "ERP System"):
        """
        Enable TOTP for the user
        
        Args:
            issuer_name: Name of the issuing organization
            
        Returns:
            Dictionary with setup information
        """
        if not pyotp:
            raise ImportError("pyotp library is required for TOTP functionality")

        # Generate secret if not exists
        if not self.totp_secret:
            self.generate_totp_secret()

        # Generate backup codes
        backup_codes = self.generate_backup_codes()

        # Get QR code
        qr_code = self.get_totp_qr_code(issuer_name)

        await self.write({
            'totp_enabled': True,
            'totp_secret': self.totp_secret,
            'totp_backup_codes': self.totp_backup_codes
        })

        return {
            'secret': self.totp_secret,
            'qr_code': qr_code,
            'backup_codes': backup_codes
        }

    async def disable_totp(self):
        """Disable TOTP for the user"""
        await self.write({
            'totp_enabled': False,
            'totp_secret': False,
            'totp_backup_codes': False
        })

    async def authenticate_with_totp(self, login: str, password: str, totp_code: str = None):
        """
        Authenticate user with password and optional TOTP code
        
        Args:
            login: User login
            password: User password
            totp_code: TOTP code (required if TOTP is enabled)
            
        Returns:
            User record if authentication successful, False otherwise
        """
        # First authenticate with password
        user = await self.authenticate(login, password)
        if not user:
            return False

        # If TOTP is enabled, verify TOTP code
        if user.totp_enabled:
            if not totp_code:
                return False
            
            # Try TOTP code first, then backup code
            if not (user.verify_totp_code(totp_code) or user.verify_backup_code(totp_code)):
                return False

        return user
