"""
Core HTTP functionality
Contains essential HTTP components like JSON-RPC, route handling, and exceptions
"""

from .error_detection import (
    ErrorResponseFactory,
    RouteTypeDetector,
    create_appropriate_error_response,
    detect_route_type_from_request,
)
from .exceptions import (
    AuthenticationError,
    AuthorizationError,
    HTTPError,
    RouteError,
    ValidationError,
)
from .html_errors import HTMLErrorResponse
from .jsonrpc import JsonRpcError, JsonRpcHandler, JsonRpcRequest, JsonRpcResponse

__all__ = [
    "JsonRpcHandler",
    "JsonRpcError",
    "JsonRpcRequest",
    "JsonRpcResponse",
    "HTTPError",
    "RouteError",
    "AuthenticationError",
    "AuthorizationError",
    "ValidationError",
    "HTMLErrorResponse",
    "RouteTypeDetector",
    "ErrorResponseFactory",
    "detect_route_type_from_request",
    "create_appropriate_error_response",
]
