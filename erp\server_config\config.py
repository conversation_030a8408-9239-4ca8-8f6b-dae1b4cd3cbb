"""
Server configuration management
"""

import os

from fastapi import FastAP<PERSON>

from ..logging import get_logger
from ..templates.manager import get_template_manager
from .lifecycle import LifecycleManager
from .middleware import MiddlewareSetup


class ServerConfig:
    """Server configuration and setup"""

    def __init__(self):
        self.template_manager = get_template_manager()
        self.middleware_setup = MiddlewareSetup()
        self.lifecycle_manager = LifecycleManager()
        self.logger = None
        self._initialize_logging()

    def _initialize_logging(self):
        """Setup logging for server config (assumes logging is already initialized)"""
        try:
            # Configure uvicorn logging to use our formatters
            from ..logging import get_logging_system

            logging_system = get_logging_system()
            logging_manager = logging_system.get_manager()

            # Check if the manager has configure_uvicorn_logging method
            if hasattr(logging_manager, "configure_uvicorn_logging"):
                logging_manager.configure_uvicorn_logging()

            self.logger = get_logger(__name__)
            self.logger.debug("🎨 Server logging configuration applied")
        except Exception as e:
            # Fallback to basic logging if initialization fails
            import logging

            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger(__name__)
            self.logger.error(f"❌ Failed to initialize advanced logging: {e}")

    def create_app(self) -> FastAPI:
        """Create FastAPI application with lifespan management"""
        app = FastAPI(
            title="ERP System",
            description="Odoo-like ERP system with async support",
            version="1.0.0",
            lifespan=self.lifecycle_manager.create_lifespan(),
        )

        # Add custom exception handler to preserve detailed error information
        self.lifecycle_manager.setup_exception_handlers(app)

        return app

    def setup_middleware(self, app: FastAPI):
        """Setup FastAPI middleware"""
        self.middleware_setup.setup_middleware(app)

    async def setup_template_directories(self):
        """Setup template directories from default locations"""
        # Add default template directory
        if os.path.exists("templates"):
            self.template_manager.add_template_directory("templates")

        print(f"Template directories: {self.template_manager.template_dirs}")

    async def setup_http_routes(self, app: FastAPI):
        """Setup HTTP routes"""
        from ..http import setup_http_routes

        # Register ONLY global/system routes (from @route decorators)
        # NO database routes, NO addon routes during server startup
        setup_http_routes(app)
        self.logger.debug("System HTTP routes registered")

        # Database-specific routes will be registered lazily when databases are accessed
        self.logger.debug("Database routes will be registered lazily when needed")

    def get_addon_info(self):
        """Get information about loaded addons"""
        return {}
