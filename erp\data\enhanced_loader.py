"""
Enhanced SQL-based XML Data Loader with Transactional Context

This module provides an enhanced data loader that uses raw SQL for all operations
and ensures proper transactional context for data loading operations.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List

from ..database.connection.manager import DatabaseManager
from ..logging import get_logger
from .exceptions import DataLoadingError, RecordCreationError
from .parser import XMLDataParser
from .sql_helpers import ModelSQLHelpers, SQLHelpers, XMLIDSQLHelpers
from .validation.sql_validators import SQLFieldValidator
from .xmlid_manager import XMLIDManager


class EnhancedSQLDataLoader:
    """
    Enhanced SQL-based data loader with comprehensive validation and transactional context.
    
    This loader ensures all operations use raw SQL and maintain proper transactional
    isolation for IR population, XML ID validation, and data loading.
    """

    def __init__(self, db_manager: DatabaseManager, config: Dict[str, Any] = None):
        """
        Initialize enhanced SQL data loader.

        Args:
            db_manager: Database manager instance
            config: Configuration options
        """
        self.db_manager = db_manager
        self.config = config or {}
        self.logger = get_logger(__name__)

        # Initialize SQL helpers
        self.sql = SQLHelpers(db_manager)
        self.model_sql = ModelSQLHelpers(self.sql)
        self.xmlid_sql = XMLIDSQLHelpers(self.sql)
        
        # Initialize enhanced validator
        self.field_validator = SQLFieldValidator(db_manager)
        
        # Initialize XML ID manager
        self.xmlid_manager = XMLIDManager(
            db_manager, 
            strict_validation=self.config.get("strict_xmlid_validation", True)
        )
        
        # Initialize parser
        self.parser = XMLDataParser()
        
        # Statistics
        self.stats = {
            "total_records": 0,
            "successful_records": 0,
            "failed_records": 0,
            "validation_errors": 0,
            "processing_errors": 0
        }



    async def load_data_file(
        self, file_path: str, addon_name: str = None
    ) -> Dict[str, Any]:
        """
        Load XML data file with enhanced validation within existing transaction context.

        This method now works within the existing transaction context instead of
        creating separate transactions. Transaction management is handled by the
        calling context.

        Args:
            file_path: Path to XML data file
            addon_name: Name of the addon (for XML ID context)

        Returns:
            Dictionary with loading results
        """
        start_time = datetime.now(timezone.utc)
        self.logger.info(f"Loading XML data file: {file_path}")
        
        try:
            # Parse XML file
            parse_result = self.parser.parse_file(file_path, addon_name)

            records = parse_result.get("records", [])
            if not records:
                return self._create_empty_result(file_path, parse_result)

            # Load records within existing transaction context
            result = await self._load_records_in_transaction(records, addon_name)

            # Create final result
            end_time = datetime.now(timezone.utc)
            duration = (end_time - start_time).total_seconds()
            return {
                "source": file_path,
                "success": result["success"],
                "duration": duration,
                "statistics": {
                    "total_records": len(records),
                    "successful_records": result["successful_records"],
                    "failed_records": result["failed_records"],
                    "validation_errors": result["validation_errors"],
                    "success_rate": (result["successful_records"] / len(records)) * 100 if records else 100.0,
                },
                "errors": result["errors"],
                "warnings": result["warnings"],
                "metadata": {
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "addon_name": addon_name,
                    "parse_stats": parse_result.get("stats", {}),
                },
            }
        except Exception as e:
            self.logger.error(f"Error loading data file {file_path}: {e}")
            return self._create_error_result(file_path, str(e), start_time)

    async def _load_records_in_transaction(
        self, records: List[Dict[str, Any]], addon_name: str
    ) -> Dict[str, Any]:
        """
        Load records within existing transaction context with enhanced validation.

        This method now works within the existing transaction context instead of
        creating separate transactions.

        Args:
            records: List of record definitions
            addon_name: Addon name for context

        Returns:
            Dictionary with loading results
        """
        result = {
            "success": True,
            "successful_records": 0,
            "failed_records": 0,
            "validation_errors": 0,
            "errors": [],
            "warnings": []
        }

        for i, record_def in enumerate(records):
            try:
                # Validate record structure
                validation_result = await self._validate_record_structure(record_def)
                if not validation_result["valid"]:
                    error_msg = f"Record {i}: {validation_result['error']}"
                    self.logger.error(error_msg)
                    raise DataLoadingError(error_msg)

                # Validate model and fields
                model_validation = await self._validate_model_and_fields(record_def)
                if not model_validation["valid"]:
                    error_msg = f"Record {i}: {model_validation['error']}"
                    self.logger.error(error_msg)
                    raise DataLoadingError(error_msg)

                # Process and validate field values
                field_processing = await self._process_and_validate_fields(record_def)
                if not field_processing["valid"]:
                    error_msg = f"Record {i}: {field_processing['error']}"
                    self.logger.error(error_msg)
                    raise DataLoadingError(error_msg)

                # Load the record
                load_success = await self._load_single_record(
                    record_def, field_processing["processed_values"], addon_name
                )

                if load_success:
                    result["successful_records"] += 1
                    self.logger.debug(f"Successfully loaded record {i}")
                else:
                    error_msg = f"Record {i}: Failed to load record"
                    self.logger.error(error_msg)
                    raise DataLoadingError(error_msg)

            except Exception as e:
                error_msg = f"Record {i}: {str(e)}"
                self.logger.error(f"Error processing record {i}: {e}")
                raise DataLoadingError(error_msg)

        return result

    async def _validate_record_structure(self, record_def: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate basic record structure.

        Args:
            record_def: Record definition

        Returns:
            Dictionary with validation result
        """
        if not isinstance(record_def, dict):
            return {"valid": False, "error": "Record must be a dictionary"}

        if "model" not in record_def:
            return {"valid": False, "error": "Record missing 'model' field"}

        if "values" not in record_def:
            return {"valid": False, "error": "Record missing 'values' field"}

        if not isinstance(record_def["values"], dict):
            return {"valid": False, "error": "Record 'values' must be a dictionary"}

        return {"valid": True, "error": None}

    async def _validate_model_and_fields(self, record_def: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate model exists and fields are valid using raw SQL.

        Args:
            record_def: Record definition

        Returns:
            Dictionary with validation result
        """
        model_name = record_def["model"]

        # Validate model exists
        if not await self.sql.validate_model_exists(model_name):
            return {"valid": False, "error": f"Model '{model_name}' does not exist"}

        # Validate model table exists
        if not await self.model_sql.validate_model_table_exists(model_name):
            return {"valid": False, "error": f"Table for model '{model_name}' does not exist"}

        # Get model fields for validation
        model_fields = await self.model_sql.get_model_fields(model_name)
        field_map = {field["name"]: field for field in model_fields}

        # Validate each field exists
        for field_name in record_def["values"].keys():
            if field_name not in field_map:
                return {
                    "valid": False, 
                    "error": f"Field '{field_name}' does not exist in model '{model_name}'"
                }

        return {"valid": True, "error": None, "field_map": field_map}

    def _create_empty_result(self, source: str, parse_result: Dict[str, Any]) -> Dict[str, Any]:
        """Create result for empty data files."""
        return {
            "source": source,
            "success": True,
            "duration": 0,
            "statistics": {
                "total_records": 0,
                "successful_records": 0,
                "failed_records": 0,
                "validation_errors": 0,
                "success_rate": 100.0,
            },
            "errors": [],
            "warnings": ["No records found to process"],
            "metadata": parse_result.get("context", {}),
        }

    def _create_error_result(self, source: str, error_msg: str, start_time: datetime) -> Dict[str, Any]:
        """Create result for error cases."""
        end_time = datetime.now(timezone.utc)
        duration = (end_time - start_time).total_seconds()

        return {
            "source": source,
            "success": False,
            "duration": duration,
            "statistics": {
                "total_records": 0,
                "successful_records": 0,
                "failed_records": 0,
                "validation_errors": 0,
                "success_rate": 0.0,
            },
            "errors": [error_msg],
            "warnings": [],
            "metadata": {
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "error": True,
            },
        }

    async def _process_and_validate_fields(self, record_def: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process and validate field values using enhanced SQL validation.

        Args:
            record_def: Record definition

        Returns:
            Dictionary with validation result and processed values
        """
        model_name = record_def["model"]
        values = record_def["values"]
        processed_values = {}
        errors = []

        self.logger.info(f"Processing fields for model {model_name}: {list(values.keys())}")

        # Get model fields for type information
        model_fields = await self.model_sql.get_model_fields(model_name)
        field_map = {field["name"]: field for field in model_fields}

        # Debug logging
        self.logger.debug(f"Found {len(model_fields)} fields for model {model_name}")
        for field in model_fields:
            if field["name"] in ["installable", "auto_install", "application"]:
                self.logger.debug(f"Boolean field {field['name']}: type={field.get('ttype', 'unknown')}")

        for field_name, field_value in values.items():
            try:
                field_info = field_map.get(field_name, {})
                field_type = field_info.get("ttype", "char")

                # Debug logging for boolean fields
                if field_name in ["installable", "auto_install", "application"]:
                    self.logger.info(f"Processing {field_name}: value='{field_value}' ({type(field_value)}) type={field_type}")

                # Handle special field value formats (ref, eval, etc.)
                if isinstance(field_value, dict):
                    processed_value = await self._process_special_field_value(
                        field_value, field_type, field_info
                    )
                else:
                    # Always try to infer boolean fields first, regardless of field info
                    if field_name in ["installable", "auto_install", "application"] and isinstance(field_value, str):
                        self.logger.info(f"Force converting boolean field {field_name}: {field_value}")
                        processed_value = self._convert_value_by_inference(field_name, field_value)
                    elif not field_info and isinstance(field_value, str):
                        self.logger.debug(f"Using inference for {field_name}: {field_value}")
                        processed_value = self._convert_value_by_inference(field_name, field_value)
                    else:
                        # Direct value validation
                        validation_result = await self.field_validator.validate_field_by_type(
                            field_type, field_value, field_info
                        )

                        if validation_result["valid"]:
                            processed_value = validation_result["value"]
                        else:
                            errors.append(f"Field '{field_name}': {validation_result['error']}")
                            continue

                # Debug logging for boolean fields
                if field_name in ["installable", "auto_install", "application"]:
                    self.logger.debug(f"Processed {field_name}: '{field_value}' -> {processed_value} ({type(processed_value)})")

                processed_values[field_name] = processed_value

            except Exception as e:
                errors.append(f"Field '{field_name}': Error processing value - {e}")

        if errors:
            return {"valid": False, "error": "; ".join(errors), "processed_values": {}}

        return {"valid": True, "error": None, "processed_values": processed_values}

    def _convert_value_by_inference(self, field_name: str, field_value: str) -> Any:
        """
        Convert field value by inferring type from field name and value.
        This is a fallback when field type information is not available.
        """
        # Common boolean field names
        boolean_fields = {
            'installable', 'auto_install', 'application', 'active', 'required',
            'readonly', 'transient', 'abstract', 'enabled', 'visible'
        }

        if field_name in boolean_fields:
            # Convert string boolean values
            if isinstance(field_value, str):
                value_lower = field_value.lower().strip()
                if value_lower in ("true", "1", "yes", "on", "t"):
                    return True
                elif value_lower in ("false", "0", "no", "off", "f", ""):
                    return False

        # Try to convert numeric values
        if field_value.isdigit():
            return int(field_value)

        try:
            if '.' in field_value:
                return float(field_value)
        except ValueError:
            pass

        # Return as string if no conversion applies
        return field_value

    async def _process_special_field_value(
        self, field_def: Dict[str, Any], field_type: str, field_info: Dict[str, Any]
    ) -> Any:
        """
        Process special field value formats (ref, eval, etc.).

        Args:
            field_def: Field definition dictionary
            field_type: Field type
            field_info: Field information

        Returns:
            Processed field value
        """
        value_type = field_def.get("type")
        value = field_def.get("value")

        if value_type == "ref":
            # XML ID reference
            if field_type == "many2one":
                validation_result = await self.field_validator.validate_many2one_field(
                    value, field_info.get("relation", "")
                )
                if validation_result["valid"]:
                    return validation_result["value"]
                else:
                    raise ValueError(validation_result["error"])
            else:
                # Resolve XML ID to record ID
                xmlid_result = await self.xmlid_sql.validate_xmlid_reference(value)
                if xmlid_result["valid"]:
                    return xmlid_result["res_id"]
                else:
                    raise ValueError(xmlid_result["error"])

        elif value_type == "eval":
            # Python expression evaluation (simplified)
            try:
                # For security, only allow basic expressions
                if isinstance(value, str) and value.strip():
                    # Simple evaluation for common cases
                    if value.lower() in ("true", "false"):
                        return value.lower() == "true"
                    elif value.isdigit():
                        return int(value)
                    elif value.replace(".", "").isdigit():
                        return float(value)
                    else:
                        # Return as string for complex expressions
                        return value
                return value
            except Exception as e:
                raise ValueError(f"Error evaluating expression '{value}': {e}")

        else:
            # Direct value
            validation_result = await self.field_validator.validate_field_by_type(
                field_type, value, field_info
            )
            if validation_result["valid"]:
                return validation_result["value"]
            else:
                raise ValueError(validation_result["error"])

    async def _load_single_record(
        self, record_def: Dict[str, Any], processed_values: Dict[str, Any], addon_name: str
    ) -> bool:
        """
        Load a single record into the database using raw SQL.

        Args:
            record_def: Record definition
            processed_values: Processed and validated field values
            addon_name: Addon name for XML ID context

        Returns:
            True if successful, False otherwise
        """
        try:
            model_name = record_def["model"]
            xml_id = record_def.get("xml_id")
            noupdate = record_def.get("noupdate", False)

            # Check if record already exists (by XML ID)
            existing_record_id = None
            if xml_id:
                xmlid_data = await self.xmlid_sql.xmlid_lookup(xml_id)
                if xmlid_data:
                    existing_record_id = xmlid_data["res_id"]

            if existing_record_id:
                if not noupdate:
                    # Update existing record
                    success = await self.model_sql.update_record(
                        model_name, existing_record_id, processed_values
                    )
                    if success:
                        self.logger.debug(f"Updated record {xml_id} in {model_name}")
                    return success
                else:
                    self.logger.debug(f"Skipped updating record {xml_id} (noupdate=True)")
                    return True
            else:
                # Create new record
                new_record_id = await self.model_sql.create_record(
                    model_name, processed_values
                )
                if not new_record_id:
                    raise RecordCreationError(f"Failed to create record in {model_name}")

                # Store XML ID mapping if provided
                if xml_id:
                    success = await self.xmlid_sql.create_or_update_xmlid(
                        addon_name or "base", xml_id, model_name, new_record_id, noupdate
                    )
                    if not success:
                        self.logger.warning(f"Failed to store XML ID mapping for {xml_id}")

                self.logger.debug(
                    f"Created record {xml_id or 'no-id'} in {model_name} with ID {new_record_id}"
                )
                return True

        except Exception as e:
            self.logger.error(f"Error loading record: {e}")
            return False
