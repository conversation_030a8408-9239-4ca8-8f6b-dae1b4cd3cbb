"""
Test suite for res.partner model
"""

import pytest
from unittest.mock import AsyncMock, MagicMock


class TestResPartner:
    """Test res.partner model functionality"""
    
    @pytest.fixture
    def partner_model(self):
        """Create a partner model instance for testing"""
        from addons.base.models.res_partner import ResPartner
        return ResPartner

    @pytest.fixture
    def country_model(self):
        """Create a country model instance for testing"""
        from addons.base.models.res_country import ResCountry
        return ResCountry
    
    @pytest.fixture
    def sample_partner_data(self):
        """Sample partner data for testing"""
        return {
            'name': 'Test Partner',
            'email': '<EMAIL>',
            'phone': '******-123-4567',
            'mobile': '******-987-6543',
            'street': '123 Main Street',
            'street2': 'Suite 100',
            'city': 'New York',
            'zip': '10001',
            'is_company': False,
            'active': True
        }
    
    @pytest.fixture
    def sample_country_data(self):
        """Sample country data for testing"""
        return {
            'name': 'United States',
            'code': 'US',
            'phone_code': 1,
            'address_format': '%(name)s\n%(street)s\n%(city)s, %(state_code)s %(zip)s\n%(country_name)s'
        }
    
    def test_partner_model_attributes(self, partner_model):
        """Test partner model has correct attributes"""
        assert partner_model._name == 'res.partner'
        assert partner_model._description == 'Contact'
        
        # Check essential fields exist
        fields = partner_model._fields
        assert 'name' in fields
        assert 'email' in fields
        assert 'phone' in fields
        assert 'mobile' in fields
        assert 'street' in fields
        assert 'city' in fields
        assert 'country_id' in fields
        assert 'is_company' in fields
        assert 'formatted_address' in fields
    
    def test_partner_initialization(self, partner_model, sample_partner_data):
        """Test partner initialization with data"""
        partner = partner_model(**sample_partner_data)
        
        assert partner.name == 'Test Partner'
        assert partner.email == '<EMAIL>'
        assert partner.phone == '******-123-4567'
        assert partner.mobile == '******-987-6543'
        assert partner.street == '123 Main Street'
        assert partner.city == 'New York'
        assert partner.is_company is False
        assert partner.active is True
    
    def test_default_values(self, partner_model):
        """Test default field values"""
        partner = partner_model(name='Test Partner')
        
        assert partner.active is True
        assert partner.is_company is False
        assert partner.customer_rank == 0
        assert partner.supplier_rank == 0
    
    def test_display_name_computation_person(self, partner_model):
        """Test display name computation for person without parent"""
        partner = partner_model(name='John Doe', is_company=False)
        partner._compute_display_name()
        
        assert partner.display_name == 'John Doe'
    
    def test_display_name_computation_company(self, partner_model):
        """Test display name computation for company"""
        partner = partner_model(name='ACME Corp', is_company=True)
        partner._compute_display_name()
        
        assert partner.display_name == 'ACME Corp'
    
    def test_display_name_computation_contact_with_parent(self, partner_model):
        """Test display name computation for contact with parent company"""
        # Mock parent company
        parent = MagicMock()
        parent.name = 'ACME Corp'
        
        partner = partner_model(name='John Doe', is_company=False, parent_id=parent)
        partner._compute_display_name()
        
        assert partner.display_name == 'John Doe (ACME Corp)'
    
    def test_simple_address_formatting(self, partner_model, sample_partner_data):
        """Test simple address formatting fallback"""
        partner = partner_model(**sample_partner_data)
        
        # Mock state
        state = MagicMock()
        state.code = 'NY'
        partner.state_id = state
        
        # Mock country
        country = MagicMock()
        country.name = 'United States'
        partner.country_id = country
        
        formatted = partner._format_address_simple()
        
        expected_lines = [
            'Test Partner',
            '123 Main Street',
            'Suite 100',
            'New York NY 10001',
            'United States'
        ]
        
        assert formatted == '\n'.join(expected_lines)
    
    def test_address_fields_list(self, partner_model):
        """Test address fields list"""
        partner = partner_model(name='Test')
        fields = partner._get_address_fields()
        
        expected_fields = ['street', 'street2', 'city', 'state_id', 'zip', 'country_id']
        assert fields == expected_fields
    
    async def test_name_get(self, partner_model):
        """Test name_get method"""
        partner = partner_model(name='Test Partner')
        partner.id = 'partner_id'
        partner.display_name = 'Test Partner Display'
        
        result = await partner.name_get()
        assert result == [('partner_id', 'Test Partner Display')]
    
    async def test_copy_address_from(self, partner_model):
        """Test copying address from another partner"""
        # Source partner with address
        source = partner_model(
            street='456 Oak Street',
            city='Boston',
            zip='02101'
        )
        
        # Mock state and country
        state = MagicMock()
        state.id = 'state_id'
        source.state_id = state
        
        country = MagicMock()
        country.id = 'country_id'
        source.country_id = country
        
        # Target partner
        target = partner_model(name='Target Partner')
        target.write = AsyncMock()
        
        await target.copy_address_from(source)
        
        # Verify write was called with address data
        target.write.assert_called_once()
        call_args = target.write.call_args[0][0]
        
        assert call_args['street'] == '456 Oak Street'
        assert call_args['city'] == 'Boston'
        assert call_args['zip'] == '02101'
        assert call_args['state_id'] == 'state_id'
        assert call_args['country_id'] == 'country_id'


class TestResCountryAddressFormatting:
    """Test address formatting functionality in res.country"""
    
    @pytest.fixture
    def country_model(self):
        """Create a country model instance for testing"""
        from addons.base.models.res_country import ResCountry
        return ResCountry
    
    @pytest.fixture
    def mock_partner(self):
        """Create a mock partner for testing"""
        partner = MagicMock()
        partner.name = 'John Doe'
        partner.street = '123 Main St'
        partner.street2 = 'Apt 4B'
        partner.city = 'New York'
        partner.zip = '10001'
        
        # Mock state
        state = MagicMock()
        state.name = 'New York'
        state.code = 'NY'
        partner.state_id = state
        
        return partner
    
    def test_format_address_with_template(self, country_model, mock_partner):
        """Test address formatting using country template"""
        country = country_model(
            name='United States',
            code='US',
            address_format='%(name)s\n%(street)s\n%(city)s, %(state_code)s %(zip)s\n%(country_name)s'
        )
        
        formatted = country.format_address(mock_partner)
        
        expected = 'John Doe\n123 Main St\nNew York, NY 10001\nUnited States'
        assert formatted == expected
    
    def test_format_address_without_template(self, country_model, mock_partner):
        """Test address formatting fallback when no template"""
        country = country_model(name='United States', code='US')
        
        formatted = country.format_address(mock_partner)
        
        # Should use fallback formatting
        assert 'John Doe' in formatted
        assert '123 Main St' in formatted
        assert 'New York' in formatted
        assert 'United States' in formatted
    
    def test_prepare_address_data(self, country_model, mock_partner):
        """Test address data preparation"""
        country = country_model(name='United States', code='US')
        
        data = country._prepare_address_data(mock_partner)
        
        assert data['name'] == 'John Doe'
        assert data['street'] == '123 Main St'
        assert data['street2'] == 'Apt 4B'
        assert data['city'] == 'New York'
        assert data['zip'] == '10001'
        assert data['state_name'] == 'New York'
        assert data['state_code'] == 'NY'
        assert data['country_name'] == 'United States'
        assert data['country_code'] == 'US'
    
    def test_format_address_with_missing_fields(self, country_model):
        """Test address formatting with missing partner fields"""
        # Partner with minimal data
        partner = MagicMock()
        partner.name = 'John Doe'
        partner.street = None
        partner.street2 = None
        partner.city = 'New York'
        partner.zip = None
        partner.state_id = None
        
        country = country_model(
            name='United States',
            code='US',
            address_format='%(name)s\n%(street)s\n%(city)s\n%(country_name)s'
        )
        
        formatted = country.format_address(partner)
        
        # Should handle missing fields gracefully
        lines = formatted.split('\n')
        assert 'John Doe' in lines
        assert 'New York' in lines
        assert 'United States' in lines
    
    def test_format_address_fallback(self, country_model, mock_partner):
        """Test fallback address formatting"""
        country = country_model(name='United States', code='US')
        
        formatted = country._format_address_fallback(mock_partner)
        
        expected_parts = [
            'John Doe',
            '123 Main St',
            'Apt 4B',
            'New York, New York',
            'United States'
        ]
        
        for part in expected_parts:
            assert part in formatted
