#!/usr/bin/env python3
"""
Unified Cross-Platform Python Virtual Environment Setup Script

This single script automatically handles all virtual environment setup tasks
across Windows, macOS, and Linux without requiring multiple separate files.

Features:
- Auto-detects operating system and executes appropriate commands
- Creates new virtual environment or validates/repairs existing one
- Automatically upgrades pip and installs dependencies
- Generates platform-specific activation instructions
- Comprehensive error handling and user feedback
- Cross-platform compatibility with single command execution

Usage:
    python setup_env.py [options]

Options:
    --venv-name NAME     Specify virtual environment directory name (default: venv)
    --no-deps           Skip dependency installation
    --force-recreate    Delete existing venv and create a new one
    --quiet             Minimal output mode
    --help              Show this help message

Author: ERP-PY Project
License: MIT
"""

import argparse
import os
import platform
import shutil
import subprocess
import sys
from pathlib import Path
from typing import List, Optional, Tuple


class Colors:
    """ANSI color codes for cross-platform colored output."""

    RED = "\033[0;31m"
    GREEN = "\033[0;32m"
    YELLOW = "\033[1;33m"
    BLUE = "\033[0;34m"
    PURPLE = "\033[0;35m"
    CYAN = "\033[0;36m"
    WHITE = "\033[1;37m"
    BOLD = "\033[1m"
    NC = "\033[0m"  # No Color

    @classmethod
    def enable_ansi_support(cls):
        """Enable ANSI color support on Windows."""
        if platform.system() == "Windows":
            try:
                # Enable ANSI support on Windows 10+
                import ctypes

                kernel32 = ctypes.windll.kernel32
                kernel32.SetConsoleMode(kernel32.GetStdHandle(-11), 7)
            except:
                # If ANSI support fails, colors will still work on modern terminals
                pass


class UnifiedVenvManager:
    """Unified virtual environment manager for all platforms."""

    def __init__(
        self,
        venv_name: str = "venv",
        project_root: Optional[str] = None,
        quiet: bool = False,
    ):
        self.venv_name = venv_name
        self.project_root = Path(project_root or os.getcwd()).resolve()
        self.venv_path = self.project_root / venv_name
        self.quiet = quiet

        # Platform detection
        self.system = platform.system().lower()
        self.is_windows = self.system == "windows"
        self.is_unix = self.system in ["linux", "darwin"]

        # Setup colors
        Colors.enable_ansi_support()

        # Platform-specific paths
        if self.is_windows:
            self.scripts_dir = self.venv_path / "Scripts"
            self.python_exe = self.scripts_dir / "python.exe"
            self.pip_exe = self.scripts_dir / "pip.exe"
            self.activate_script = self.scripts_dir / "activate.bat"
            self.activate_ps1 = self.scripts_dir / "Activate.ps1"
        else:
            self.bin_dir = self.venv_path / "bin"
            self.python_exe = self.bin_dir / "python"
            self.pip_exe = self.bin_dir / "pip"
            self.activate_script = self.bin_dir / "activate"

    def print_status(self, color: str, symbol: str, message: str, force: bool = False):
        """Print colored status message."""
        if not self.quiet or force:
            print(f"{color}{symbol}{Colors.NC} {message}")

    def print_success(self, message: str, force: bool = False):
        self.print_status(Colors.GREEN, "✓", message, force)

    def print_error(self, message: str, force: bool = True):
        self.print_status(Colors.RED, "✗", message, force)

    def print_warning(self, message: str, force: bool = False):
        self.print_status(Colors.YELLOW, "⚠", message, force)

    def print_info(self, message: str, force: bool = False):
        self.print_status(Colors.BLUE, "ℹ", message, force)

    def print_header(self, title: str, force: bool = False):
        """Print a formatted header."""
        if not self.quiet or force:
            print(f"\n{Colors.BOLD}{Colors.CYAN}{'=' * 60}{Colors.NC}")
            print(f"{Colors.BOLD}{Colors.WHITE}{title}{Colors.NC}")
            print(f"{Colors.BOLD}{Colors.CYAN}{'=' * 60}{Colors.NC}")

    def check_python_available(self) -> bool:
        """Check if Python is available and get version info."""
        try:
            result = subprocess.run(
                [sys.executable, "--version"],
                capture_output=True,
                text=True,
                check=True,
            )
            version = result.stdout.strip()
            self.print_success(f"Found Python: {version}")

            # Check Python version (require 3.12+)
            version_info = sys.version_info
            if version_info < (3, 12):
                self.print_error(
                    f"Python 3.12+ required, found {version_info.major}.{version_info.minor}"
                )
                return False

            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.print_error("Python not found or not accessible")
            self.print_info("Please ensure Python 3.8+ is installed and in your PATH")
            if self.is_windows:
                self.print_info("Download from: https://python.org")
            elif self.system == "darwin":
                self.print_info("Install with: brew install python3")
            else:
                self.print_info(
                    "Install with: sudo apt-get install python3 python3-venv"
                )
            return False

    def venv_exists_and_valid(self) -> bool:
        """Check if virtual environment exists and is valid."""
        if not self.venv_path.exists():
            return False

        # Check for required files
        if self.is_windows:
            required_files = [self.python_exe, self.activate_script]
        else:
            required_files = [self.python_exe, self.activate_script]

        for file_path in required_files:
            if not file_path.exists():
                self.print_warning(
                    f"Virtual environment incomplete: missing {file_path.name}"
                )
                return False

        # Test if Python executable works
        try:
            result = subprocess.run(
                [str(self.python_exe), "--version"],
                capture_output=True,
                text=True,
                check=True,
                timeout=10,
            )
            self.print_success("Existing virtual environment is valid")
            return True
        except (
            subprocess.CalledProcessError,
            subprocess.TimeoutExpired,
            FileNotFoundError,
        ):
            self.print_warning(
                "Virtual environment exists but Python executable is corrupted"
            )
            return False

    def remove_venv(self) -> bool:
        """Safely remove virtual environment directory."""
        if not self.venv_path.exists():
            return True

        self.print_info(f"Removing existing virtual environment at {self.venv_path}")
        try:
            if self.is_windows:
                # On Windows, handle read-only files
                def handle_remove_readonly(func, path, exc):
                    if os.path.exists(path):
                        os.chmod(path, 0o777)
                        func(path)

                shutil.rmtree(self.venv_path, onerror=handle_remove_readonly)
            else:
                shutil.rmtree(self.venv_path)

            self.print_success("Virtual environment removed successfully")
            return True
        except Exception as e:
            self.print_error(f"Failed to remove virtual environment: {e}")
            self.print_info(
                "You may need to remove it manually or run with administrator privileges"
            )
            return False

    def create_venv(self) -> bool:
        """Create a new virtual environment."""
        self.print_info(f"Creating virtual environment at: {self.venv_path}")

        try:
            # Create new virtual environment
            subprocess.run(
                [sys.executable, "-m", "venv", str(self.venv_path)],
                check=True,
                cwd=self.project_root,
            )

            self.print_success("Virtual environment created successfully")
            return True

        except subprocess.CalledProcessError as e:
            self.print_error(f"Error creating virtual environment: {e}")
            self.print_info("This might be due to:")
            self.print_info("  - Insufficient permissions")
            self.print_info("  - Python venv module not available")
            self.print_info("  - Disk space issues")
            if not self.is_windows:
                self.print_info("  Try: sudo apt-get install python3-venv")
            return False
        except Exception as e:
            self.print_error(f"Unexpected error: {e}")
            return False

    def upgrade_pip(self) -> bool:
        """Upgrade pip in the virtual environment."""
        self.print_info("Upgrading pip...")
        try:
            subprocess.run(
                [str(self.python_exe), "-m", "pip", "install", "--upgrade", "pip"],
                check=True,
                capture_output=True,
            )
            self.print_success("Pip upgraded successfully")
            return True
        except subprocess.CalledProcessError:
            self.print_warning("Could not upgrade pip (continuing anyway)")
            return False

    def install_dependencies(self) -> bool:
        """Install dependencies from requirements.txt if it exists."""
        requirements_file = self.project_root / "requirements.txt"

        if not requirements_file.exists():
            self.print_info(
                "No requirements.txt found, skipping dependency installation"
            )
            return True

        self.print_info(f"Installing dependencies from {requirements_file}")
        try:
            subprocess.run(
                [str(self.pip_exe), "install", "-r", str(requirements_file)],
                check=True,
                cwd=self.project_root,
            )
            self.print_success("Dependencies installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            self.print_error(f"Error installing dependencies: {e}")
            self.print_info("You may need to install them manually later")
            return False

    def get_activation_commands(self) -> Tuple[str, List[str]]:
        """Get platform-specific activation commands."""
        if self.is_windows:
            # Check if we're likely in PowerShell
            if os.environ.get("PSModulePath"):
                primary_cmd = f'& "{self.activate_ps1}"'
                fallback_cmds = [
                    f'"{self.activate_script}"',
                    f'call "{self.activate_script}"',
                ]
            else:
                primary_cmd = f'"{self.activate_script}"'
                fallback_cmds = [
                    f'call "{self.activate_script}"',
                    f'& "{self.activate_ps1}"',
                ]
        else:
            primary_cmd = f'source "{self.activate_script}"'
            fallback_cmds = [f'. "{self.activate_script}"']

        return primary_cmd, fallback_cmds

    def create_activation_helper_scripts(self):
        """Create platform-specific activation helper scripts."""
        if self.is_windows:
            # Create Windows batch file
            batch_content = f"""@echo off
REM Quick activation script for {self.venv_name} virtual environment

if not exist "{self.activate_script}" (
    echo Virtual environment not found at {self.venv_path}
    echo Run "python setup_env.py" first to create the virtual environment
    pause
    exit /b 1
)

echo Activating virtual environment...
call "{self.activate_script}"
"""
            batch_file = self.project_root / "activate_env.bat"
            try:
                with open(batch_file, "w") as f:
                    f.write(batch_content)
                self.print_success(f"Created activation helper: {batch_file.name}")
            except Exception as e:
                self.print_warning(f"Could not create activation helper: {e}")

        else:
            # Create Unix shell script
            shell_content = f"""#!/bin/bash
# Quick activation script for {self.venv_name} virtual environment

VENV_ACTIVATE="{self.activate_script}"

if [[ ! -f "$VENV_ACTIVATE" ]]; then
    echo "Virtual environment not found at {self.venv_path}"
    echo "Run 'python setup_env.py' first to create the virtual environment"
    return 1 2>/dev/null || exit 1
fi

echo "Activating virtual environment..."
source "$VENV_ACTIVATE"
echo "Virtual environment activated!"
echo "Python executable: $(which python)"
echo "To deactivate, run: deactivate"
"""
            shell_file = self.project_root / "activate_env.sh"
            try:
                with open(shell_file, "w") as f:
                    f.write(shell_content)
                # Make executable on Unix systems
                os.chmod(shell_file, 0o755)
                self.print_success(f"Created activation helper: {shell_file.name}")
            except Exception as e:
                self.print_warning(f"Could not create activation helper: {e}")

    def get_package_count(self) -> int:
        """Get the number of installed packages."""
        try:
            result = subprocess.run(
                [str(self.pip_exe), "list"], capture_output=True, text=True, check=True
            )
            # Count non-header lines
            lines = [
                line.strip()
                for line in result.stdout.split("\n")
                if line.strip() and not line.startswith("-")
            ]
            return max(0, len(lines) - 2)  # Subtract header lines
        except:
            return 0

    def print_success_summary(self):
        """Print final success summary with activation instructions."""
        self.print_header("🎉 Virtual Environment Setup Complete!", force=True)

        primary_cmd, fallback_cmds = self.get_activation_commands()

        print(f"\n{Colors.BOLD}Virtual Environment Details:{Colors.NC}")
        print(f"  Location: {self.venv_path}")
        print(f"  Python: {self.python_exe}")

        package_count = self.get_package_count()
        if package_count > 0:
            print(f"  Installed packages: {package_count}")

        print(f"\n{Colors.BOLD}To activate the virtual environment:{Colors.NC}")
        print(f"  {Colors.GREEN}{primary_cmd}{Colors.NC}")

        if fallback_cmds:
            print(f"\n{Colors.BOLD}Alternative activation commands:{Colors.NC}")
            for cmd in fallback_cmds:
                print(f"  {Colors.CYAN}{cmd}{Colors.NC}")

        print(f"\n{Colors.BOLD}Quick activation helper:{Colors.NC}")
        if self.is_windows:
            print(f"  {Colors.YELLOW}activate_env.bat{Colors.NC}")
        else:
            print(f"  {Colors.YELLOW}source activate_env.sh{Colors.NC}")

        print(f"\n{Colors.BOLD}To deactivate:{Colors.NC}")
        print(f"  {Colors.CYAN}deactivate{Colors.NC}")

        print(f"\n{Colors.CYAN}{'=' * 60}{Colors.NC}\n")

    def run_setup(
        self, force_recreate: bool = False, install_deps: bool = True
    ) -> bool:
        """Run the complete virtual environment setup process."""

        # Print header
        self.print_header("Python Virtual Environment Setup")
        print(f"Project root: {self.project_root}")
        print(f"Virtual environment: {self.venv_path}")
        print(f"Platform: {platform.system()} {platform.release()}")
        print()

        # Check Python availability
        if not self.check_python_available():
            return False

        # Handle existing virtual environment
        venv_exists = self.venv_exists_and_valid()

        if venv_exists and not force_recreate:
            self.print_success("Using existing virtual environment")

            # Upgrade pip and install/update dependencies
            self.upgrade_pip()
            if install_deps:
                self.install_dependencies()

            # Create activation helpers
            self.create_activation_helper_scripts()

            # Show success summary
            self.print_success_summary()
            return True

        elif venv_exists and force_recreate:
            self.print_info(
                "Force recreate requested, removing existing virtual environment"
            )
            if not self.remove_venv():
                return False

        elif self.venv_path.exists() and not venv_exists:
            self.print_warning("Incomplete virtual environment detected, removing...")
            if not self.remove_venv():
                return False

        # Create new virtual environment
        if not self.create_venv():
            return False

        # Upgrade pip
        if not self.upgrade_pip():
            self.print_warning("Continuing without pip upgrade...")

        # Install dependencies
        if install_deps:
            if not self.install_dependencies():
                self.print_warning("Continuing without all dependencies...")

        # Create activation helpers
        self.create_activation_helper_scripts()

        # Show success summary
        self.print_success_summary()
        return True


def activate_environment_interactive():
    """Interactive activation that works in the current shell."""
    venv_manager = UnifiedVenvManager(quiet=True)

    if not venv_manager.venv_exists_and_valid():
        print(f"{Colors.RED}✗{Colors.NC} Virtual environment not found or invalid")
        print(
            f"{Colors.YELLOW}⚠{Colors.NC} Run 'python setup_env.py' first to create the virtual environment"
        )
        return False

    # Get activation commands
    primary_cmd, fallback_cmds = venv_manager.get_activation_commands()

    print(
        f"{Colors.GREEN}✓{Colors.NC} Virtual environment found at: {venv_manager.venv_path}"
    )
    print(f"\n{Colors.BOLD}To activate the virtual environment, run:{Colors.NC}")
    print(f"  {Colors.GREEN}{primary_cmd}{Colors.NC}")

    if fallback_cmds:
        print(f"\n{Colors.BOLD}Alternative commands:{Colors.NC}")
        for cmd in fallback_cmds:
            print(f"  {Colors.CYAN}{cmd}{Colors.NC}")

    print(f"\n{Colors.BOLD}Quick activation helper:{Colors.NC}")
    if venv_manager.is_windows:
        print(f"  {Colors.YELLOW}.\\activate_env.bat{Colors.NC}")
    else:
        print(f"  {Colors.YELLOW}source activate_env.sh{Colors.NC}")

    return True


def run_command_in_venv(command_args: List[str]):
    """Run a command inside the virtual environment."""
    venv_manager = UnifiedVenvManager(quiet=True)

    if not venv_manager.venv_exists_and_valid():
        print(f"{Colors.RED}✗{Colors.NC} Virtual environment not found or invalid")
        print(
            f"{Colors.YELLOW}⚠{Colors.NC} Run 'python setup_env.py' first to create the virtual environment"
        )
        return False

    # Prepare the command
    if command_args[0] == "python":
        cmd = [str(venv_manager.python_exe)] + command_args[1:]
    elif command_args[0] == "pip":
        cmd = [str(venv_manager.pip_exe)] + command_args[1:]
    else:
        # For other commands, try to run them in the venv context
        if venv_manager.is_windows:
            # On Windows, we need to activate first, then run the command
            activate_and_run = (
                f'call "{venv_manager.activate_script}" && {" ".join(command_args)}'
            )
            cmd = ["cmd", "/c", activate_and_run]
        else:
            # On Unix, source the activation script and run the command
            activate_and_run = (
                f'source "{venv_manager.activate_script}" && {" ".join(command_args)}'
            )
            cmd = ["bash", "-c", activate_and_run]

    print(
        f"{Colors.BLUE}ℹ{Colors.NC} Running in virtual environment: {' '.join(command_args)}"
    )

    try:
        result = subprocess.run(cmd, cwd=venv_manager.project_root)
        return result.returncode == 0
    except Exception as e:
        print(f"{Colors.RED}✗{Colors.NC} Error running command: {e}")
        return False


def main():
    """Main function to handle command line arguments and run the setup."""
    parser = argparse.ArgumentParser(
        description="Unified cross-platform Python virtual environment manager",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python setup_env.py                    # Setup/verify virtual environment
  python setup_env.py --activate         # Show activation instructions
  python setup_env.py --run python --version  # Run python in venv
  python setup_env.py --run pip list     # Run pip in venv
  python setup_env.py --force-recreate   # Recreate virtual environment
        """,
    )

    parser.add_argument(
        "--venv-name",
        default="venv",
        help="Virtual environment directory name (default: venv)",
    )
    parser.add_argument(
        "--no-deps", action="store_true", help="Skip dependency installation"
    )
    parser.add_argument(
        "--force-recreate",
        action="store_true",
        help="Delete existing venv and create a new one",
    )
    parser.add_argument("--quiet", action="store_true", help="Minimal output mode")
    parser.add_argument(
        "--activate",
        action="store_true",
        help="Show activation instructions for existing venv",
    )
    parser.add_argument(
        "--run",
        nargs=argparse.REMAINDER,
        help="Run a command in the virtual environment",
    )

    args = parser.parse_args()

    # Handle special modes
    if args.activate:
        success = activate_environment_interactive()
        sys.exit(0 if success else 1)

    if args.run:
        success = run_command_in_venv(args.run)
        sys.exit(0 if success else 1)

    # Initialize the unified virtual environment manager
    venv_manager = UnifiedVenvManager(venv_name=args.venv_name, quiet=args.quiet)

    # Run the setup process
    success = venv_manager.run_setup(
        force_recreate=args.force_recreate, install_deps=not args.no_deps
    )

    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
