"""
Specialized field types for ERP models
"""

import re

from .base import Field, FieldValidationError
from .basic import Text


class Binary(Field):
    """Binary field for file storage"""

    def __init__(self, attachment=True, **kwargs):
        super().__init__(**kwargs)
        self.attachment = attachment

    def get_sql_type(self):
        if self.attachment:
            return "VARCHAR(255)"  # Store attachment reference
        return "BYTEA"  # Store binary data directly

    def _validate_value(self, value):
        """Validate binary field value"""
        if value is None:
            return None

        if isinstance(value, (bytes, bytearray)):
            return value

        if isinstance(value, str):
            # Assume base64 encoded
            import base64

            try:
                return base64.b64decode(value)
            except Exception:
                raise FieldValidationError("Invalid base64 encoded binary data")

        raise FieldValidationError(f"Cannot convert {type(value).__name__} to binary")


class Json(Field):
    """JSON field for structured data"""

    def get_sql_type(self):
        return "JSONB"

    def _validate_value(self, value):
        """Validate JSON field value"""
        if value is None:
            return None

        # Ensure value is JSON serializable
        import json

        try:
            json.dumps(value)
            return value
        except (TypeError, ValueError) as e:
            raise FieldValidationError(f"Value is not JSON serializable: {e}")


class Html(Text):
    """HTML field - extends Text with HTML-specific features"""

    def __init__(self, sanitize=True, **kwargs):
        super().__init__(**kwargs)
        self.sanitize = sanitize

    def _validate_value(self, value):
        """Validate HTML field value"""
        value = super()._validate_value(value)

        if self.sanitize and value:
            # Basic HTML sanitization (in a real implementation, use a proper library)
            # Remove script tags and their content
            value = re.sub(
                r"<script[^>]*>.*?</script>", "", value, flags=re.DOTALL | re.IGNORECASE
            )
            # Remove dangerous attributes
            value = re.sub(
                r"\s(on\w+|javascript:)[^>]*", "", value, flags=re.IGNORECASE
            )

        return value
