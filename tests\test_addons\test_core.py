"""
Test suite for addon core functionality

This module tests:
- Core IR population hooks
- AddonIRManager functionality
- Core hook integration
- IR metadata management
- Schema synchronization
"""

from unittest.mock import MagicMock, patch

import pytest

from erp.addons.core import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, addon_ir_manager, core_ir_population_hook
from erp.addons.hooks import <PERSON><PERSON><PERSON>x<PERSON>, HookType


class TestCoreIRPopulationHook:
    """Test core IR population hook functionality"""

    @pytest.mark.asyncio
    async def test_core_ir_population_hook_success(self):
        """Test successful IR population hook execution"""
        # Create mock environment and context
        mock_env = MagicMock()
        mock_env.cr = MagicMock()

        context = HookContext(
            hook_type=HookType.POST_INSTALL,
            addon_name="test_addon",
            env=mock_env,
            metadata={},
        )

        # Mock the schema sync and IR population
        with (
            patch(
                "erp.addons.core.core_hooks.SchemaComparator.sync_model_tables"
            ) as mock_schema,
            patch(
                "erp.addons.core.core_hooks.ir_population_manager.populate_ir_metadata_with_rollback"
            ) as mock_ir,
        ):

            mock_schema.return_value = {
                "sync_successful": True,
                "summary": {"tables_created": 2},
            }

            mock_ir.return_value = {
                "status": "success",
                "models_processed": 3,
                "fields_processed": 15,
            }

            result = await core_ir_population_hook(context)

            assert result is True
            mock_schema.assert_called_once_with("test_addon")
            mock_ir.assert_called_once()

    @pytest.mark.asyncio
    async def test_core_ir_population_hook_no_env(self):
        """Test IR population hook with no environment"""
        context = HookContext(
            hook_type=HookType.POST_INSTALL,
            addon_name="test_addon",
            env=None,
            metadata={},
        )

        result = await core_ir_population_hook(context)
        assert result is False

    @pytest.mark.asyncio
    async def test_core_ir_population_hook_schema_failure(self):
        """Test IR population hook with schema sync failure"""
        mock_env = MagicMock()
        mock_env.cr = MagicMock()

        context = HookContext(
            hook_type=HookType.POST_INSTALL,
            addon_name="test_addon",
            env=mock_env,
            metadata={},
        )

        with patch(
            "erp.addons.core.core_hooks.SchemaComparator.sync_model_tables"
        ) as mock_schema:
            mock_schema.return_value = {
                "sync_successful": False,
                "message": "Schema sync failed",
            }

            result = await core_ir_population_hook(context)

            assert result is False
            mock_schema.assert_called_once_with("test_addon")

    @pytest.mark.asyncio
    async def test_core_ir_population_hook_ir_failure(self):
        """Test IR population hook with IR population failure"""
        mock_env = MagicMock()
        mock_env.cr = MagicMock()

        context = HookContext(
            hook_type=HookType.POST_INSTALL,
            addon_name="test_addon",
            env=mock_env,
            metadata={},
        )

        with (
            patch(
                "erp.addons.core.core_hooks.SchemaGenerator.sync_model_tables"
            ) as mock_schema,
            patch(
                "erp.addons.core.core_hooks.ir_population_manager.populate_ir_metadata_with_rollback"
            ) as mock_ir,
        ):

            mock_schema.return_value = {
                "sync_successful": True,
                "summary": {"tables_created": 2},
            }

            mock_ir.return_value = {
                "status": "error",
                "message": "IR population failed",
                "rollback_performed": True,
            }

            result = await core_ir_population_hook(context)

            assert result is False
            mock_schema.assert_called_once_with("test_addon")
            mock_ir.assert_called_once()

    @pytest.mark.asyncio
    async def test_core_ir_population_hook_exception(self):
        """Test IR population hook with exception"""
        mock_env = MagicMock()
        mock_env.cr = MagicMock()

        context = HookContext(
            hook_type=HookType.POST_INSTALL,
            addon_name="test_addon",
            env=mock_env,
            metadata={},
        )

        with patch(
            "erp.addons.core.core_hooks.SchemaGenerator.sync_model_tables"
        ) as mock_schema:
            mock_schema.side_effect = Exception("Test exception")

            result = await core_ir_population_hook(context)

            assert result is False
            mock_schema.assert_called_once_with("test_addon")


class TestAddonIRManager:
    """Test AddonIRManager functionality"""

    def test_addon_ir_manager_creation(self):
        """Test AddonIRManager creation"""
        manager = AddonIRManager()
        assert manager is not None
        assert hasattr(manager, "logger")

    @pytest.mark.asyncio
    async def test_ensure_ir_population_for_addon_success(self):
        """Test successful IR population for addon"""
        manager = AddonIRManager()
        mock_env = MagicMock()

        with patch(
            "erp.addons.core.core_hooks.core_ir_population_hook", return_value=True
        ) as mock_hook:
            result = await manager.ensure_ir_population_for_addon(
                mock_env, "test_addon"
            )

            assert result["success"] is True
            assert result["addon_name"] == "test_addon"
            assert result["operation"] == "ir_population"
            mock_hook.assert_called_once()

    @pytest.mark.asyncio
    async def test_ensure_ir_population_for_addon_failure(self):
        """Test failed IR population for addon"""
        manager = AddonIRManager()
        mock_env = MagicMock()

        with patch(
            "erp.addons.core.core_hooks.core_ir_population_hook", return_value=False
        ) as mock_hook:
            result = await manager.ensure_ir_population_for_addon(
                mock_env, "test_addon"
            )

            assert result["success"] is False
            assert result["addon_name"] == "test_addon"
            assert result["operation"] == "ir_population"
            mock_hook.assert_called_once()

    @pytest.mark.asyncio
    async def test_validate_ir_population_for_addon_success(self):
        """Test successful IR population validation"""
        manager = AddonIRManager()
        mock_env = MagicMock()
        mock_env.cr = MagicMock()

        expected_validation = {
            "models_validated": 3,
            "fields_validated": 15,
            "all_valid": True,
        }

        with patch(
            "erp.addons.core.core_hooks.ir_population_manager.validate_ir_metadata_for_addon"
        ) as mock_validate:
            mock_validate.return_value = expected_validation

            result = await manager.validate_ir_population_for_addon(
                mock_env, "test_addon"
            )

            assert result["success"] is True
            assert result["addon_name"] == "test_addon"
            assert result["validation_results"] == expected_validation
            mock_validate.assert_called_once_with(mock_env.cr, "test_addon")

    @pytest.mark.asyncio
    async def test_validate_ir_population_for_addon_exception(self):
        """Test IR population validation with exception"""
        manager = AddonIRManager()
        mock_env = MagicMock()
        mock_env.cr = MagicMock()

        with patch(
            "erp.addons.core.core_hooks.ir_population_manager.validate_ir_metadata_for_addon"
        ) as mock_validate:
            mock_validate.side_effect = Exception("Validation error")

            result = await manager.validate_ir_population_for_addon(
                mock_env, "test_addon"
            )

            assert result["success"] is False
            assert result["addon_name"] == "test_addon"
            assert "error" in result
            assert result["error"] == "Validation error"
            mock_validate.assert_called_once_with(mock_env.cr, "test_addon")


class TestGlobalAddonIRManager:
    """Test global addon IR manager instance"""

    def test_global_addon_ir_manager_exists(self):
        """Test that global addon_ir_manager instance exists"""
        assert addon_ir_manager is not None
        assert isinstance(addon_ir_manager, AddonIRManager)

    @pytest.mark.asyncio
    async def test_global_addon_ir_manager_functionality(self):
        """Test global addon IR manager functionality"""
        mock_env = MagicMock()

        with patch(
            "erp.addons.core.core_hooks.core_ir_population_hook", return_value=True
        ):
            result = await addon_ir_manager.ensure_ir_population_for_addon(
                mock_env, "test_addon"
            )

            assert result["success"] is True
            assert result["addon_name"] == "test_addon"


class TestCoreModuleIntegration:
    """Test core module integration"""

    def test_core_module_imports(self):
        """Test that core module components can be imported"""
        from erp.addons.core import __all__

        expected_components = [
            "core_ir_population_hook",
            "AddonIRManager",
            "addon_ir_manager",
        ]

        for component in expected_components:
            assert component in __all__

    def test_core_module_components_accessible(self):
        """Test that core module components are accessible"""
        from erp.addons.core import (
            AddonIRManager,
            addon_ir_manager,
            core_ir_population_hook,
        )

        assert callable(core_ir_population_hook)
        assert AddonIRManager is not None
        assert addon_ir_manager is not None
        assert isinstance(addon_ir_manager, AddonIRManager)

    @pytest.mark.asyncio
    async def test_core_hook_integration_with_hook_system(self):
        """Test core hook integration with the hook system"""
        from erp.addons.hooks import HookType, get_hook_registry

        # The core hook should be registered in the hook registry
        registry = get_hook_registry()

        # Check if our core hook is registered (it might be among others)
        hooks = registry.get_hooks(HookType.POST_INSTALL, None)  # None for global hooks

        # Find our core hook
        core_hook_found = False
        for hook in hooks:
            if hook.func == core_ir_population_hook:
                core_hook_found = True
                assert (
                    hook.priority == 100
                )  # Low priority to run after addon-specific hooks
                break

        # Note: This test might need adjustment based on actual hook registration implementation
        # The core hook might be registered differently or not at all during testing
