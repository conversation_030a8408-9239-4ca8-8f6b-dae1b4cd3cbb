import logging
import time
import uuid
from typing import Any, Callable

from fastapi import Request

from ...logging.utils import log_structured

logger = logging.getLogger(__name__)


class LoggingMiddleware:
    """Handles request logging with structured data and performance metrics."""

    @staticmethod
    async def process_request(request: Request, call_next: Callable) -> Any:
        """Processes the request, logs start/end, and handles errors."""
        start_time = time.perf_counter()
        request_id = str(uuid.uuid4())[:8]

        log_structured(
            logger,
            logging.DEBUG,
            f"Request started: {request.method} {request.url}",
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            user_agent=request.headers.get("user-agent"),
            client_ip=request.client.host if request.client else "unknown",
            operation="request_start",
        )

        try:
            response = await call_next(request)
            process_time = time.perf_counter() - start_time
            log_level = logging.WARNING if process_time > 2.0 else logging.DEBUG

            log_structured(
                logger,
                log_level,
                f"Request completed: {request.method} {request.url}",
                request_id=request_id,
                method=request.method,
                url=str(request.url),
                status_code=response.status_code,
                duration=process_time,
                operation="request_complete",
            )
            return response
        except Exception as e:
            process_time = time.perf_counter() - start_time
            log_structured(
                logger,
                logging.ERROR,
                f"Request failed: {request.method} {request.url}",
                request_id=request_id,
                method=request.method,
                url=str(request.url),
                error_type=type(e).__name__,
                error_message=str(e),
                duration=process_time,
                operation="request_error",
            )
            raise
