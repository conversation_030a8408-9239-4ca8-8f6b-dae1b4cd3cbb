"""
Test suite for JSON RPC functionality

This module tests:
- JSON RPC request/response handling
- JSON RPC error handling
- JSON RPC handler functionality
- Request validation and processing
"""

import json
from unittest.mock import MagicMock

import pytest
from fastapi.responses import JSONResponse

from erp.http import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JsonRpc<PERSON><PERSON><PERSON>, JsonRpcRequest, JsonRpcResponse


class TestJsonRpcRequest:
    """Test JSON RPC request handling"""

    def test_jsonrpc_request_creation(self):
        """Test JSON RPC request creation"""
        request = JsonRpcRequest(
            method="test_method", params={"param1": "value1"}, id="123"
        )

        assert request.method == "test_method"
        assert request.params == {"param1": "value1"}
        assert request.id == "123"
        assert request.jsonrpc == "2.0"

    def test_jsonrpc_request_from_dict(self):
        """Test creating JSON RPC request from dictionary"""
        data = {
            "jsonrpc": "2.0",
            "method": "test_method",
            "params": ["param1", "param2"],
            "id": 1,
        }

        request = JsonRpcRequest.from_dict(data)

        assert request.method == "test_method"
        assert request.params == ["param1", "param2"]
        assert request.id == 1

    def test_jsonrpc_request_notification(self):
        """Test JSON RPC notification (no id)"""
        request = JsonRpcRequest(method="notify_method", params={})

        assert request.is_notification() is True
        assert request.id is None

    def test_jsonrpc_request_validation(self):
        """Test JSON RPC request validation"""
        # Valid request
        valid_request = JsonRpcRequest(method="test", id=1)
        assert valid_request.validate() is None

        # Invalid request (no method)
        invalid_request = JsonRpcRequest(method="", id=1)
        error = invalid_request.validate()
        assert error is not None
        assert "method" in error.lower()


class TestJsonRpcResponse:
    """Test JSON RPC response handling"""

    def test_jsonrpc_response_success(self):
        """Test successful JSON RPC response"""
        response = JsonRpcResponse(result={"success": True}, id=1)

        assert response.result == {"success": True}
        assert response.id == 1
        assert response.error is None
        assert response.jsonrpc == "2.0"

    def test_jsonrpc_response_error(self):
        """Test error JSON RPC response"""
        error = JsonRpcError.method_not_found("unknown_method", 1)
        response = JsonRpcResponse(error=error, id=1)

        assert response.result is None
        assert response.error == error
        assert response.id == 1

    def test_jsonrpc_response_to_response(self):
        """Test converting JSON RPC response to FastAPI response"""
        response = JsonRpcResponse(result={"data": "test"}, id=1)
        fastapi_response = response.to_response()

        assert isinstance(fastapi_response, JSONResponse)


class TestJsonRpcError:
    """Test JSON RPC error handling"""

    def test_jsonrpc_error_parse_error(self):
        """Test parse error creation"""
        error = JsonRpcError.parse_error("Invalid JSON")

        assert error.code == -32700
        assert error.message == "Parse error"
        assert error.data == "Invalid JSON"

    def test_jsonrpc_error_invalid_request(self):
        """Test invalid request error"""
        error = JsonRpcError.invalid_request("Missing method", request_id=1)

        assert error.code == -32600
        assert error.message == "Invalid Request"
        assert error.data == "Missing method"

    def test_jsonrpc_error_method_not_found(self):
        """Test method not found error"""
        error = JsonRpcError.method_not_found("unknown_method", 1)

        assert error.code == -32601
        assert error.message == "Method not found"
        assert "unknown_method" in error.data

    def test_jsonrpc_error_invalid_params(self):
        """Test invalid params error"""
        error = JsonRpcError.invalid_params(request_id=1)

        assert error.code == -32602
        assert error.message == "Invalid params"

    def test_jsonrpc_error_internal_error(self):
        """Test internal error"""
        error = JsonRpcError.internal_error("Database connection failed", 1)

        assert error.code == -32603
        assert error.message == "Internal error"
        assert error.data == "Database connection failed"


class TestJsonRpcHandler:
    """Test JSON RPC handler functionality"""

    def test_jsonrpc_handler_initialization(self):
        """Test JSON RPC handler initialization"""
        handler = JsonRpcHandler()

        assert hasattr(handler, "_methods")
        assert isinstance(handler._methods, dict)

    def test_jsonrpc_handler_register_method(self):
        """Test registering JSON RPC method"""
        handler = JsonRpcHandler()

        def test_method(param1, param2):
            return param1 + param2

        handler.register_method("add", test_method)

        assert "add" in handler._methods
        assert handler._methods["add"] == test_method

    def test_jsonrpc_handler_unregister_method(self):
        """Test unregistering JSON RPC method"""
        handler = JsonRpcHandler()

        def test_method():
            return "test"

        handler.register_method("test", test_method)
        assert "test" in handler._methods

        handler.unregister_method("test")
        assert "test" not in handler._methods

    @pytest.mark.asyncio
    async def test_jsonrpc_handler_handle_request(self):
        """Test handling JSON RPC request"""
        handler = JsonRpcHandler()

        def add_method(a, b):
            return a + b

        handler.register_method("add", add_method)

        # Mock request
        mock_request = MagicMock()
        request_data = {"jsonrpc": "2.0", "method": "add", "params": [2, 3], "id": 1}
        mock_request.body.return_value = json.dumps(request_data).encode()

        response = await handler.handle_request(mock_request)

        assert isinstance(response, JSONResponse)
