"""
Route registries package
Provides separate registries for different route scopes
"""

from .database import (
    DatabaseRouteManager,
    DatabaseRouteRegistry,
    get_database_route_manager,
)
from .system import (
    SystemRouteRegistry,
    get_system_route_registry,
    reset_system_route_registry,
)

__all__ = [
    "SystemRouteRegistry",
    "get_system_route_registry",
    "reset_system_route_registry",
    "DatabaseRouteRegistry",
    "DatabaseRouteManager",
    "get_database_route_manager",
]
