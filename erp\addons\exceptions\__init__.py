"""
Addon exceptions

This package contains all exception classes used throughout the addon system.
"""

from .addon_exceptions import (
    AddonError,
    AddonHookError,
    AddonInstallationError,
    AddonLifecycleError,
    AddonManifestError,
    AddonNotFoundError,
    AddonRegistryError,
    AddonStateError,
    AddonUninstallationError,
    AddonUpgradeError,
    CircularDependencyError,
    DependencyError,
    MissingDependencyError,
)

__all__ = [
    "AddonError",
    "DependencyError",
    "CircularDependencyError",
    "MissingDependencyError",
    "AddonInstallationError",
    "AddonUninstallationError",
    "AddonUpgradeError",
    "AddonManifestError",
    "AddonNotFoundError",
    "AddonStateError",
    "AddonRegistryError",
    "AddonHookError",
    "AddonLifecycleError",
]
