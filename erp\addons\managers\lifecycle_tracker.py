"""
Addon Lifecycle Completion Tracker

This module provides tracking for addon installation sessions to detect when
the entire dependency chain has completed, enabling single registry refresh
at the end instead of after each individual addon.
"""

import asyncio
import time
import uuid
from dataclasses import dataclass
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set

from ...logging import get_logger

logger = get_logger(__name__)


class SessionState(Enum):
    """States for installation session lifecycle"""

    ACTIVE = "active"
    COMPLETING = "completing"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class InstallationSession:
    """Information about an installation session"""

    session_id: str
    db_name: str
    explicit_addons: Set[str]
    all_addons: Set[str]  # includes dependencies
    state: SessionState
    created_at: float
    completed_addons: Set[str]
    failed_addons: Set[str]
    savepoint_names: Set[str]
    committed_savepoints: Set[str]
    completion_callbacks: List[Callable]


class AddonLifecycleTracker:
    """
    Tracks addon installation sessions to detect lifecycle completion.

    This tracker:
    1. Creates sessions for top-level installation requests
    2. Tracks which addons are part of each session
    3. Monitors savepoint commits and addon completion
    4. Detects when the entire dependency chain is complete
    5. Triggers registry refresh only once at the end
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self._sessions: Dict[str, InstallationSession] = {}
        self._addon_to_session: Dict[str, str] = {}  # addon_name -> session_id
        self._db_to_active_session: Dict[str, str] = {}  # db_name -> session_id
        self._lock = asyncio.Lock()

    async def start_installation_session(
        self, db_name: str, explicit_addons: List[str], all_addons: List[str]
    ) -> str:
        """
        Start a new installation session for a dependency chain.

        Args:
            db_name: Database name
            explicit_addons: Addons explicitly requested by user
            all_addons: All addons including dependencies

        Returns:
            Session ID
        """
        async with self._lock:
            session_id = f"install_session_{uuid.uuid4().hex[:8]}_{int(time.time())}"

            # Check if there's already an active session for this database
            if db_name in self._db_to_active_session:
                existing_session_id = self._db_to_active_session[db_name]
                self.logger.warning(
                    f"Database {db_name} already has active session {existing_session_id}"
                )
                # For now, we'll allow concurrent sessions but log a warning

            session = InstallationSession(
                session_id=session_id,
                db_name=db_name,
                explicit_addons=set(explicit_addons),
                all_addons=set(all_addons),
                state=SessionState.ACTIVE,
                created_at=time.time(),
                completed_addons=set(),
                failed_addons=set(),
                savepoint_names=set(),
                committed_savepoints=set(),
                completion_callbacks=[],
            )

            self._sessions[session_id] = session
            self._db_to_active_session[db_name] = session_id

            # Map addons to this session
            for addon_name in all_addons:
                self._addon_to_session[addon_name] = session_id

            self.logger.info(
                f"Started installation session {session_id} for database {db_name}"
            )
            self.logger.debug(f"Session includes explicit addons: {explicit_addons}")
            self.logger.debug(f"Session includes all addons: {all_addons}")

            return session_id

    async def register_savepoint(
        self, session_id: str, savepoint_name: str, addon_name: str
    ):
        """Register a savepoint as part of the session"""
        async with self._lock:
            if session_id not in self._sessions:
                self.logger.warning(
                    f"Session {session_id} not found for savepoint {savepoint_name}"
                )
                return

            session = self._sessions[session_id]
            session.savepoint_names.add(savepoint_name)

            self.logger.debug(
                f"Registered savepoint {savepoint_name} for addon {addon_name} in session {session_id}"
            )

    async def mark_savepoint_committed(
        self, session_id: str, savepoint_name: str, addon_name: str
    ):
        """Mark a savepoint as committed"""
        async with self._lock:
            if session_id not in self._sessions:
                self.logger.warning(
                    f"Session {session_id} not found for committed savepoint {savepoint_name}"
                )
                return

            session = self._sessions[session_id]
            session.committed_savepoints.add(savepoint_name)

            self.logger.debug(
                f"Marked savepoint {savepoint_name} as committed for addon {addon_name} in session {session_id}"
            )

            # Check if this completes the session
            await self._check_session_completion(session_id)

    async def mark_addon_completed(self, addon_name: str, success: bool):
        """Mark an addon as completed (successfully or failed)"""
        async with self._lock:
            if addon_name not in self._addon_to_session:
                self.logger.debug(f"Addon {addon_name} not tracked in any session")
                return

            session_id = self._addon_to_session[addon_name]
            session = self._sessions[session_id]

            if success:
                session.completed_addons.add(addon_name)
                self.logger.debug(
                    f"Marked addon {addon_name} as completed in session {session_id}"
                )
            else:
                session.failed_addons.add(addon_name)
                self.logger.debug(
                    f"Marked addon {addon_name} as failed in session {session_id}"
                )

            # Check if this completes the session
            await self._check_session_completion(session_id)

    async def _check_session_completion(self, session_id: str):
        """Check if a session is complete and trigger callbacks if so"""
        session = self._sessions[session_id]

        if session.state != SessionState.ACTIVE:
            return  # Already processed

        # Check if all addons are either completed or failed
        total_processed = len(session.completed_addons) + len(session.failed_addons)

        if total_processed >= len(session.all_addons):
            # All addons processed - determine final state
            if session.failed_addons:
                session.state = SessionState.FAILED
                self.logger.info(
                    f"Session {session_id} failed - some addons failed: {session.failed_addons}"
                )
            else:
                session.state = SessionState.COMPLETED
                self.logger.info(
                    f"Session {session_id} completed successfully - all addons installed"
                )

            # Trigger completion callbacks
            await self._trigger_completion_callbacks(session_id)

    async def _trigger_completion_callbacks(self, session_id: str):
        """Trigger all completion callbacks for a session"""
        session = self._sessions[session_id]

        self.logger.debug(
            f"Triggering {len(session.completion_callbacks)} completion callbacks for session {session_id}"
        )

        for callback in session.completion_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(session)
                else:
                    callback(session)
            except Exception as e:
                self.logger.error(
                    f"Error in completion callback for session {session_id}: {e}"
                )

    async def add_completion_callback(self, session_id: str, callback: Callable):
        """Add a callback to be triggered when the session completes"""
        async with self._lock:
            if session_id not in self._sessions:
                self.logger.warning(
                    f"Session {session_id} not found for callback registration"
                )
                return

            session = self._sessions[session_id]
            session.completion_callbacks.append(callback)

            # If session is already complete, trigger callback immediately
            if session.state in (SessionState.COMPLETED, SessionState.FAILED):
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(session)
                    else:
                        callback(session)
                except Exception as e:
                    self.logger.error(
                        f"Error in immediate completion callback for session {session_id}: {e}"
                    )

    async def get_session_for_addon(self, addon_name: str) -> Optional[str]:
        """Get the session ID for an addon"""
        return self._addon_to_session.get(addon_name)

    async def get_session_for_database(self, db_name: str) -> Optional[str]:
        """Get the active session ID for a database"""
        return self._db_to_active_session.get(db_name)

    async def cleanup_session(self, session_id: str):
        """Clean up a completed session"""
        async with self._lock:
            if session_id not in self._sessions:
                return

            session = self._sessions[session_id]

            # Remove addon mappings
            for addon_name in session.all_addons:
                if addon_name in self._addon_to_session:
                    del self._addon_to_session[addon_name]

            # Remove database mapping
            if session.db_name in self._db_to_active_session:
                del self._db_to_active_session[session.db_name]

            # Remove session
            del self._sessions[session_id]

            self.logger.debug(f"Cleaned up session {session_id}")

    async def get_session_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get status information for a session"""
        if session_id not in self._sessions:
            return None

        session = self._sessions[session_id]
        return {
            "session_id": session.session_id,
            "db_name": session.db_name,
            "state": session.state.value,
            "explicit_addons": list(session.explicit_addons),
            "all_addons": list(session.all_addons),
            "completed_addons": list(session.completed_addons),
            "failed_addons": list(session.failed_addons),
            "progress": f"{len(session.completed_addons) + len(session.failed_addons)}/{len(session.all_addons)}",
            "created_at": session.created_at,
        }


# Singleton instance
_lifecycle_tracker = None


def get_lifecycle_tracker() -> AddonLifecycleTracker:
    """Get the global lifecycle tracker instance"""
    global _lifecycle_tracker
    if _lifecycle_tracker is None:
        _lifecycle_tracker = AddonLifecycleTracker()
    return _lifecycle_tracker
