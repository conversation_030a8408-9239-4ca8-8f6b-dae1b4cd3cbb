"""
Database management commands
"""

import argparse
import asyncio
from typing import List

from ..config import config
from ..database.registry import DatabaseRegistry
from ..database.registry.initialization import DatabaseInitializer
from ..database.registry.lifecycle import DatabaseLifecycle
from ..logging import get_logger
from .base import BaseCommand, CommandGroup


class InitCommand(BaseCommand):
    """Initialize database command with positional database name"""

    def add_arguments(self, parser: argparse.ArgumentParser):
        parser.add_argument("db_name", help="Database name to initialize")
        parser.add_argument(
            "--force", "-f",
            action="store_true",
            help="Force initialization even if database already exists",
        )
        parser.add_argument(
            "--demo", "-d", action="store_true", help="Install demo data if available"
        )
        parser.add_argument(
            "--no-create", "-n",
            action="store_true",
            help="Do not create database if it does not exist",
        )
        parser.add_argument(
            "--exit", "-e",
            action="store_true",
            help="Exit after initialization instead of starting server",
        )
        parser.add_argument(
            "--no-http",
            action="store_true",
            help="Skip HTTP server startup (only applicable if not using --exit)",
        )
        parser.add_argument(
            "-i", "--install-addon",
            type=str,
            help="Install specified addon and its dependencies during initialization",
        )

    def handle(self, args: argparse.Namespace) -> int:
        """Handle database initialization command"""

        async def _init_database():
            db_name = args.db_name

            self.print_info(f"🚀 Initializing database: {db_name}")

            # Check if database is already initialized
            if await self._is_database_initialized(db_name):
                if not args.force:
                    self.print_success(
                        f"Database '{db_name}' is already initialized with ERP system"
                    )
                    self.print_info(
                        "   Skipping initialization (use --force to reinitialize)"
                    )
                    return 0
                else:
                    self.print_warning(
                        f"Database '{db_name}' is already initialized, but --force flag specified"
                    )
                    self.print_warning("   Proceeding with reinitialization...")

            # Check if database exists first
            existing_dbs = await DatabaseRegistry.list_databases()
            db_exists = db_name in existing_dbs

            # Create database if it doesn't exist
            if not db_exists:
                if args.no_create:
                    self.print_error(
                        f"Database '{db_name}' does not exist and --no-create flag was specified."
                    )
                    return 1
                else:
                    self.print_info(f"📊 Creating database: {db_name}")
                    await DatabaseLifecycle.create_database(
                        db_name, install_addon=args.install_addon
                    )
                    self.print_success(f"Database '{db_name}' created successfully")
                    # Database creation already includes initialization, so we're done
            else:
                # Database exists but may not be initialized, so initialize it
                self.print_info(
                    "🔧 Installing base modules and setting up initial data..."
                )
                await DatabaseInitializer.initialize_database(
                    db_name, install_addon=args.install_addon
                )

            # If we reach here, initialization was successful
            self.print_success(
                f"Database '{db_name}' initialized successfully!"
            )

            # Print database info
            try:
                from ..utils import get_database_info, print_database_info

                # Get database manager first, then pass it to get_database_info
                db_manager = await DatabaseRegistry.get_database(db_name)
                db_info = await get_database_info(db_manager)
                print_database_info(db_info)
            except Exception as e:
                logger = get_logger(__name__)
                logger.debug(f"Could not get database info: {e}")

            # Set the initialized database as the current database in config
            config.set("options", "db_name", db_name)

            # If --exit flag is used, exit after initialization
            if args.exit:
                self.print_info(
                    "🚪 Exiting after initialization (--exit flag specified)"
                )
                return 0

            # Otherwise, handle post-initialization behavior
            return self._handle_post_initialization(args, db_name)

        # Run the async function - let exceptions bubble up to main()
        return asyncio.run(_init_database())

    async def _is_database_initialized(self, db_name: str) -> bool:
        """Check if a database is already initialized with ERP system"""
        try:
            from ..database.memory.registry_manager import MemoryRegistryManager

            return await MemoryRegistryManager._is_base_module_installed(db_name)
        except Exception as e:
            logger = get_logger(__name__)
            logger.warning(f"Error checking database initialization status: {e}")
            return False

    def _handle_post_initialization(
        self, args: argparse.Namespace, db_name: str
    ) -> int:
        """Handle behavior after successful database initialization"""
        try:
            if args.no_http:
                self.print_info(
                    "🚫 HTTP server startup skipped (--no-http flag specified)"
                )
                self.print_info(
                    f"📊 Database '{db_name}' is ready and selected as current database"
                )
                self.print_info(
                    "💡 You can start the HTTP server later with: erp-bin start"
                )

                # Keep the process running without HTTP server
                self.print_info(
                    "🔄 Keeping process running with initialized database..."
                )
                self.print_info("   Press Ctrl+C to exit")

                try:
                    import signal
                    import time

                    def signal_handler(_signum, _frame):
                        self.print_info("\n🛑 Received shutdown signal, exiting...")
                        exit(0)

                    signal.signal(signal.SIGINT, signal_handler)
                    signal.signal(signal.SIGTERM, signal_handler)

                    # Keep running until interrupted
                    while True:
                        time.sleep(1)

                except KeyboardInterrupt:
                    self.print_info("\n🛑 Process interrupted by user")
                    return 0
            else:
                # Default behavior: suggest starting the server
                self.print_success(f"Database '{db_name}' initialized successfully!")
                self.print_info("💡 To start the HTTP server, run: erp-bin start")
                return 0

        except Exception as e:
            self.print_error(f"Failed to complete initialization: {e}")
            if getattr(args, "verbose", False):
                import traceback

                traceback.print_exc()
            return 1


class PurgeCommand(BaseCommand):
    """Purge all databases command"""

    def add_arguments(self, parser: argparse.ArgumentParser):
        parser.add_argument(
            "--force", "-f",
            action="store_true",
            help="Skip confirmation prompts (dangerous!)",
        )

    def handle(self, args: argparse.Namespace) -> int:
        """Handle database purge command"""

        async def _purge_databases():
            try:
                return await self._purge_all_databases(args.force)
            except Exception as e:
                self.print_error(f"Failed to purge databases: {e}")
                if getattr(args, "verbose", False):
                    import traceback

                    traceback.print_exc()
                return 1

        # Run the async function
        result = asyncio.run(_purge_databases())
        return 0 if result else 1

    async def _purge_all_databases(self, force: bool = False) -> bool:
        """Purge all databases with confirmation prompts"""
        logger = get_logger(__name__)

        try:
            # Get list of user databases
            self.print_info("🔍 Scanning for databases...")
            user_databases = await self._get_user_databases()

            if not user_databases:
                self.print_success("No user databases found to purge")
                return True

            self.print_info(f"📋 Found {len(user_databases)} database(s) to purge:")
            for db in user_databases:
                self.print_info(f"   • {db}")

            # First confirmation
            if not force:
                print(
                    f"\n⚠️  WARNING: This will permanently delete ALL {len(user_databases)} database(s) and their data!"
                )
                print("📋 Databases to be deleted:")
                for db in user_databases:
                    print(f"   • {db}")

                confirm1 = (
                    input("\nDo you want to continue? (yes/no): ").strip().lower()
                )
                if confirm1 not in ["yes", "y"]:
                    self.print_info("🚫 Purge operation cancelled by user")
                    return False

                # Second confirmation with exact text
                print(
                    f"\n🔥 FINAL WARNING: You are about to delete {len(user_databases)} database(s) permanently!"
                )
                confirm2 = input('Type "PURGE ALL" to confirm: ').strip()
                if confirm2 != "PURGE ALL":
                    self.print_info(
                        "🚫 Purge operation cancelled - confirmation text did not match"
                    )
                    return False

            # Perform the purge
            self.print_info("🗑️  Starting database purge operation...")
            success_count = 0
            failed_databases = []

            for db_name in user_databases:
                self.print_info(f"🗑️  Removing database: {db_name}")
                try:
                    success = await DatabaseLifecycle.drop_database(db_name)
                    if success:
                        success_count += 1
                        self.print_success(f"Successfully removed database: {db_name}")
                    else:
                        failed_databases.append(db_name)
                        self.print_error(f"Failed to remove database: {db_name}")
                except Exception as e:
                    failed_databases.append(db_name)
                    self.print_error(f"Error removing database {db_name}: {e}")

            # Report results
            self.print_info(f"\n📊 Purge operation completed:")
            self.print_info(f"   ✅ Successfully removed: {success_count} database(s)")
            if failed_databases:
                self.print_error(
                    f"   ❌ Failed to remove: {len(failed_databases)} database(s)"
                )
                for db in failed_databases:
                    self.print_error(f"      • {db}")
                return False
            else:
                self.print_success("🎉 All databases purged successfully!")
                return True

        except Exception as e:
            logger.error(f"Failed to purge databases: {e}")
            return False

    async def _get_user_databases(self) -> List[str]:
        """Get list of user databases (excluding system databases)"""
        try:
            # Get all databases
            all_databases = await DatabaseRegistry.list_databases()

            # Filter out system databases
            system_dbs = {"postgres", "template0", "template1"}
            user_databases = [db for db in all_databases if db not in system_dbs]

            return user_databases
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"Failed to get user databases: {e}")
            return []


class DatabaseCommandGroup(CommandGroup):
    """Database command group"""

    def __init__(self):
        super().__init__()
        self.register_command(InitCommand())
        self.register_command(PurgeCommand())

    def add_commands(self, subparsers, parent_parser=None):
        """Add database commands to subparsers"""
        # Initialize database (new command with positional db_name)
        init_parser = subparsers.add_parser(
            "init",
            help="Initialize database with name",
            parents=[parent_parser] if parent_parser else [],
        )
        self.commands["init"].add_arguments(init_parser)

        # Purge databases command
        purge_parser = subparsers.add_parser(
            "purge",
            help="Remove all databases",
            parents=[parent_parser] if parent_parser else [],
        )
        self.commands["purge"].add_arguments(purge_parser)
