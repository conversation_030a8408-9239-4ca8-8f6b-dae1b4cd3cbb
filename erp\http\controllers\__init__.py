"""
Controller components package
Provides modular controller functionality through mixins and specialized components
"""

from .base import BaseController
from .controller import (
    APIController,
    Controller,
    DatabaseController,
    MinimalController,
    TemplateController,
)
from .mixins import AuthMixin, DatabaseMixin, RequestMixin, ResponseMixin, TemplateMixin
from .registry import ControllerRegistry, get_controller_registry

__all__ = [
    "BaseController",
    "RequestMixin",
    "ResponseMixin",
    "TemplateMixin",
    "DatabaseMixin",
    "AuthMixin",
    "Controller",
    "MinimalController",
    "APIController",
    "TemplateController",
    "DatabaseController",
    "ControllerRegistry",
    "get_controller_registry",
]
