"""
Common helper functions for addon operations

This module provides shared helper functions that are used across
multiple addon components to eliminate code duplication.
"""

import asyncio
import time
from contextlib import asynccontextmanager
from typing import Any, Awaitable, Callable, Dict, List

from ...logging import get_logger

logger = get_logger(__name__)


class TimingContext:
    """Context manager for timing operations"""

    def __init__(self, operation_name: str, addon_name: str = None):
        self.operation_name = operation_name
        self.addon_name = addon_name
        self.start_time = None
        self.duration = None
        self.logger = get_logger(__name__)

    def __enter__(self):
        self.start_time = time.perf_counter()
        if self.addon_name:
            self.logger.debug(f"Starting {self.operation_name} for {self.addon_name}")
        else:
            self.logger.debug(f"Starting {self.operation_name}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.duration = time.perf_counter() - self.start_time
        if exc_type is None:
            if self.addon_name:
                self.logger.debug(
                    f"Completed {self.operation_name} for {self.addon_name} in {self.duration:.3f}s"
                )
            else:
                self.logger.debug(
                    f"Completed {self.operation_name} in {self.duration:.3f}s"
                )
        else:
            if self.addon_name:
                self.logger.error(
                    f"Failed {self.operation_name} for {self.addon_name} after {self.duration:.3f}s"
                )
            else:
                self.logger.error(
                    f"Failed {self.operation_name} after {self.duration:.3f}s"
                )

    def get_duration(self) -> float:
        """Get the duration of the operation"""
        if self.duration is not None:
            return self.duration
        elif self.start_time is not None:
            return time.perf_counter() - self.start_time
        else:
            return 0.0


async def retry_async_operation(
    operation: Callable[[], Awaitable[Any]],
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    operation_name: str = "operation",
) -> Any:
    """
    Retry an async operation with exponential backoff

    Args:
        operation: Async function to retry
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries in seconds
        backoff_factor: Factor to multiply delay by after each retry
        operation_name: Name of the operation for logging

    Returns:
        Result of the operation

    Raises:
        Exception: The last exception if all retries fail
    """
    last_exception = None
    current_delay = delay

    for attempt in range(max_retries + 1):
        try:
            result = await operation()
            if attempt > 0:
                logger.info(f"✅ {operation_name} succeeded on attempt {attempt + 1}")
            return result
        except Exception as e:
            last_exception = e
            if attempt < max_retries:
                logger.warning(
                    f"⚠️ {operation_name} failed on attempt {attempt + 1}, retrying in {current_delay:.1f}s: {e}"
                )
                await asyncio.sleep(current_delay)
                current_delay *= backoff_factor
            else:
                logger.error(
                    f"❌ {operation_name} failed after {max_retries + 1} attempts: {e}"
                )

    raise last_exception


def format_duration(seconds: float) -> str:
    """
    Format duration in a human-readable way

    Args:
        seconds: Duration in seconds

    Returns:
        Formatted duration string
    """
    if seconds < 1:
        return f"{seconds * 1000:.0f}ms"
    elif seconds < 60:
        return f"{seconds:.3f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        remaining_seconds = seconds % 60
        return f"{minutes}m {remaining_seconds:.1f}s"
    else:
        hours = int(seconds // 3600)
        remaining_minutes = int((seconds % 3600) // 60)
        return f"{hours}h {remaining_minutes}m"


def safe_get_nested_value(
    data: Dict[str, Any], keys: List[str], default: Any = None
) -> Any:
    """
    Safely get a nested value from a dictionary

    Args:
        data: Dictionary to search in
        keys: List of keys to traverse
        default: Default value if key path doesn't exist

    Returns:
        Value at the key path or default
    """
    current = data
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return default
    return current


def merge_dictionaries(*dicts: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge multiple dictionaries, with later ones taking precedence

    Args:
        *dicts: Dictionaries to merge

    Returns:
        Merged dictionary
    """
    result = {}
    for d in dicts:
        if d:
            result.update(d)
    return result


def validate_addon_name(addon_name: str) -> bool:
    """
    Validate addon name format

    Args:
        addon_name: Name to validate

    Returns:
        True if valid, False otherwise
    """
    if not addon_name:
        return False

    # Addon names should be valid Python identifiers
    if not addon_name.isidentifier():
        return False

    # Should not start with underscore (reserved)
    if addon_name.startswith("_"):
        return False

    return True


def normalize_addon_name(name: str) -> str:
    """
    Normalize addon name to a valid format

    Args:
        name: Name to normalize

    Returns:
        Normalized name
    """
    # Replace spaces and hyphens with underscores
    normalized = name.replace(" ", "_").replace("-", "_")

    # Remove invalid characters
    normalized = "".join(c for c in normalized if c.isalnum() or c == "_")

    # Ensure it doesn't start with a number
    if normalized and normalized[0].isdigit():
        normalized = "addon_" + normalized

    # Ensure it's not empty
    if not normalized:
        normalized = "unnamed_addon"

    return normalized.lower()


def create_success_response(data: Any = None, message: str = None) -> Dict[str, Any]:
    """
    Create a standardized success response

    Args:
        data: Response data
        message: Success message

    Returns:
        Standardized success response dictionary
    """
    response = {"success": True, "timestamp": time.time()}

    if data is not None:
        response["data"] = data

    if message:
        response["message"] = message

    return response


@asynccontextmanager
async def transaction_context(db_manager, operation_name: str = "operation"):
    """
    Context manager for database transactions with proper error handling.

    This function is now deprecated in favor of working within existing transaction
    contexts. It's maintained for backward compatibility but should not be used
    for new code in addon installation flows.

    Args:
        db_manager: Database manager instance
        operation_name: Name of the operation for logging
    """
    logger.warning(
        "transaction_context is deprecated. Use existing transaction contexts "
        "for addon installation operations. Operation: %s", operation_name
    )

    try:
        await db_manager.begin()
        logger.debug("Started transaction for %s", operation_name)
        yield db_manager
        await db_manager.commit()
        logger.debug("Committed transaction for %s", operation_name)
    except Exception as e:
        await db_manager.rollback()
        logger.error("Rolled back transaction for %s: %s", operation_name, e)
        raise


def chunk_list(items: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    Split a list into chunks of specified size

    Args:
        items: List to chunk
        chunk_size: Size of each chunk

    Returns:
        List of chunks
    """
    return [items[i : i + chunk_size] for i in range(0, len(items), chunk_size)]


def flatten_list(nested_list: List[List[Any]]) -> List[Any]:
    """
    Flatten a nested list

    Args:
        nested_list: List of lists to flatten

    Returns:
        Flattened list
    """
    return [item for sublist in nested_list for item in sublist]
