"""
Route decorators for HTTP routes
"""

import functools
import inspect
from typing import Callable, List, Optional, Union

from ..logging import get_logger
from .auth import AuthType
from .metadata import HttpMethod, RouteType, create_route_metadata
from .registries import DatabaseRouteManager, get_system_route_registry

logger = get_logger(__name__)


def route(
    path: str,
    *,
    type: RouteType = RouteType.HTTP,
    auth: AuthType = AuthType.USER,
    methods: Optional[List[Union[str, HttpMethod]]] = None,
    cors: Optional[str] = None,
    csrf: bool = True,
    save_session: bool = True,
    database: Optional[str] = None,
    **kwargs,
) -> Callable:
    """
    Odoo-style HTTP route decorator

    Args:
        path: URL path pattern
        type: Route type (http, json)
        auth: Authentication type (none, public, user, admin)
        methods: HTTP methods (default: ['GET'] for http, ['POST'] for json)
        cors: CORS origin pattern
        csrf: Enable CSRF protection
        save_session: Save session after request
        database: Specific database to register route for (optional)
        **kwargs: Additional route parameters

    Returns:
        Decorated function

    Example:
        @route('/api/users', type='json', auth='user', methods=['POST'])
        async def get_users(request):
            return {'users': []}

        @route('/web/login', type='http', auth='none', methods=['GET', 'POST'])
        async def login_page(request):
            return HTMLResponse('<html>Login</html>')
    """

    def decorator(func: Callable) -> Callable:
        # Create route metadata
        route_metadata = create_route_metadata(
            path=path,
            type=type,
            auth=auth,
            methods=methods,
            cors=cors,
            csrf=csrf,
            save_session=save_session,
            database=database,
            original_func=func,
            **kwargs,
        )

        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            """Route wrapper - simplified for development"""

            # Call the original function directly
            if inspect.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)

            return result

        # Store route metadata on the function for lazy discovery
        wrapper._route_metadata = route_metadata.to_dict()

        # Store the handler in the module for discovery by AppRegistry
        # This allows lazy loading to find and register routes when needed
        module = inspect.getmodule(func)
        if module:
            if not hasattr(module, "_route_handlers"):
                module._route_handlers = []
            module._route_handlers.append(wrapper)
            logger.debug(f"Stored route handler for lazy loading: {path}")

        return wrapper

    return decorator


def systemRoute(
    path: str,
    *,
    methods: Optional[List[Union[str, HttpMethod]]] = None,
    type: Union[RouteType, str] = RouteType.HTTP,
    auth: Union[AuthType, str] = AuthType.USER,
    cors: Optional[str] = None,
    csrf: bool = True,
    save_session: bool = True,
    **kwargs,
) -> Callable:
    """
    System route decorator for database-independent routes

    This decorator registers routes with the SystemRouteRegistry for routes that
    don't depend on any specific database (e.g., health checks, system info, etc.)

    Args:
        path: URL path pattern
        methods: HTTP methods (default: ['GET'] for http, ['POST'] for json)
        type: Route type (http, json)
        auth: Authentication type (none, public, user, admin)
        cors: CORS origin pattern
        csrf: Enable CSRF protection
        save_session: Save session after request
        **kwargs: Additional route parameters

    Returns:
        Decorated function

    Example:
        @systemRoute('/health', methods=['GET'])
        async def health_check():
            return {'status': 'healthy'}

        @systemRoute('/api/system/info', type='json', auth='none')
        async def system_info():
            return {'version': '1.0.0'}
    """

    def decorator(func: Callable) -> Callable:
        # Convert string values to enum values if needed
        route_type = RouteType(type) if isinstance(type, str) else type
        auth_type = AuthType(auth) if isinstance(auth, str) else auth

        # Create route metadata
        route_metadata = create_route_metadata(
            path=path,
            type=route_type,
            auth=auth_type,
            methods=methods,
            cors=cors,
            csrf=csrf,
            save_session=save_session,
            original_func=func,
            **kwargs,
        )

        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            return await func(*args, **kwargs)

        # Register with system route registry immediately
        system_registry = get_system_route_registry()

        # Create RouteInfo object for registration
        from .interfaces import RouteInfo, RouteScope

        route_info = RouteInfo(
            path=path,
            handler=wrapper,
            methods=route_metadata.methods,
            route_type=route_metadata.type,
            auth=route_metadata.auth,
            scope=RouteScope.SYSTEM,
            metadata=route_metadata.to_dict(),
            source=None,  # System routes don't have a specific source
        )

        # Register the route synchronously for immediate availability
        # System routes need to be available immediately during module import
        system_registry._register_route_sync(route_info)

        # Store complete metadata on the function (including path for reference)
        wrapper._route_metadata = route_metadata.to_dict()
        wrapper._is_system_route = True

        logger.debug(f"Registered system route: {route_metadata.methods} {path}")

        return wrapper

    return decorator


def _register_route_in_database_registry(
    path: str, handler: Callable, **kwargs
) -> bool:
    """
    Optimized route registration for database-specific registry
    Returns True if successfully scheduled, False otherwise
    """
    return DatabaseRouteManager.register_route_in_database_registry(
        path, handler, **kwargs
    )
