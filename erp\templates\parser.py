"""
XML Template Parser - Core parsing functionality
"""

import xml.etree.ElementTree as ET
from typing import Any, Dict, List

from .exceptions import TemplateSyntaxError


class TemplateParser:
    """Parses XML templates and extracts template definitions"""

    def __init__(self):
        self.templates: Dict[str, ET.Element] = {}

    def parse_template_file(self, content: str) -> Dict[str, ET.Element]:
        """Parse template file content and extract template definitions"""
        try:
            # Parse XML content
            root = ET.fromstring(content)

            # Find all template definitions
            templates = {}
            for template_elem in root.findall(".//t[@t-name]"):
                template_name = template_elem.get("t-name")
                if template_name:
                    templates[template_name] = template_elem

            return templates

        except ET.ParseError as e:
            raise TemplateSyntaxError(f"Invalid XML syntax: {e}")

    def extract_directives(self, element: ET.Element) -> Dict[str, str]:
        """Extract template directives from element attributes"""
        directives = {}

        for attr_name, attr_value in element.attrib.items():
            if attr_name.startswith("t-"):
                directives[attr_name] = attr_value

        return directives

    def is_template_element(self, element: ET.Element) -> bool:
        """Check if element is a template control element (t tag)"""
        return element.tag == "t"

    def has_directives(self, element: ET.Element) -> bool:
        """Check if element has any template directives"""
        return any(attr.startswith("t-") for attr in element.attrib.keys())


class ExpressionEvaluator:
    """Evaluates template expressions safely"""

    def __init__(self):
        # Safe built-in functions for template expressions
        self.safe_builtins = {
            "str": str,
            "int": int,
            "float": float,
            "bool": bool,
            "len": len,
            "range": range,
            "enumerate": enumerate,
            "zip": zip,
            "min": min,
            "max": max,
            "sum": sum,
            "abs": abs,
            "round": round,
        }

    def evaluate(self, expression: str, context: Dict[str, Any]) -> Any:
        """Safely evaluate a template expression"""
        try:
            # Security validation
            self._validate_expression_security(expression, context)

            # Check if expression contains filters (pipe syntax)
            if "|" in expression and not self._is_logical_or(expression):
                return self._evaluate_with_filters(expression, context)

            # Create safe evaluation environment
            safe_dict = {"__builtins__": self.safe_builtins, **context}

            # Evaluate expression
            return eval(expression, safe_dict)

        except Exception as e:
            raise TemplateSyntaxError(
                f"Error evaluating expression '{expression}': {e}"
            )

    def _validate_expression_security(
        self, expression: str, context: Dict[str, Any]
    ) -> None:
        """Validate expression for security"""
        try:
            from .security import get_security_manager

            security_manager = get_security_manager()
            if security_manager and security_manager.enabled:
                security_manager.validate_expression(expression, context)
        except ImportError:
            # Security module not available, skip validation
            pass

    def _is_logical_or(self, expression: str) -> bool:
        """Check if pipe is part of logical OR operation"""
        # Simple heuristic: if we have 'or' keyword near the pipe, it's likely logical OR
        return " or " in expression.lower()

    def _evaluate_with_filters(self, expression: str, context: Dict[str, Any]) -> Any:
        """Evaluate expression with filter pipeline"""
        from .filters import FilterRegistry

        # Split by pipe, but be careful about pipes in strings
        parts = self._split_filter_expression(expression)

        # First part is the base expression
        base_expr = parts[0].strip()
        value = self.evaluate_simple(base_expr, context)

        # Apply filters in sequence
        for filter_part in parts[1:]:
            filter_part = filter_part.strip()
            if ":" in filter_part:
                filter_name, filter_args = filter_part.split(":", 1)
                filter_name = filter_name.strip()
                # Parse filter arguments
                args = self._parse_filter_args(filter_args.strip(), context)
                value = FilterRegistry.apply_filter(filter_name, value, *args)
            else:
                filter_name = filter_part
                value = FilterRegistry.apply_filter(filter_name, value)

        return value

    def evaluate_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """Evaluate a boolean condition"""
        result = self.evaluate(condition, context)
        return bool(result)

    def evaluate_simple(self, expression: str, context: Dict[str, Any]) -> Any:
        """Evaluate expression without filter processing"""
        try:
            # Create safe evaluation environment
            safe_dict = {"__builtins__": self.safe_builtins, **context}

            # Evaluate expression
            return eval(expression, safe_dict)

        except Exception as e:
            raise TemplateSyntaxError(
                f"Error evaluating expression '{expression}': {e}"
            )

    def _split_filter_expression(self, expression: str) -> List[str]:
        """Split expression by pipes, respecting string literals"""
        parts = []
        current_part = ""
        in_string = False
        string_char = None
        i = 0

        while i < len(expression):
            char = expression[i]

            if char in ('"', "'") and (i == 0 or expression[i - 1] != "\\"):
                if not in_string:
                    in_string = True
                    string_char = char
                elif char == string_char:
                    in_string = False
                    string_char = None
            elif char == "|" and not in_string:
                parts.append(current_part)
                current_part = ""
                i += 1
                continue

            current_part += char
            i += 1

        if current_part:
            parts.append(current_part)

        return parts

    def _parse_filter_args(self, args_str: str, context: Dict[str, Any]) -> List[Any]:
        """Parse filter arguments"""
        if not args_str:
            return []

        # More sophisticated argument parsing that respects quotes
        args = []
        current_arg = ""
        in_string = False
        string_char = None
        i = 0

        while i < len(args_str):
            char = args_str[i]

            if char in ('"', "'") and (i == 0 or args_str[i - 1] != "\\"):
                if not in_string:
                    in_string = True
                    string_char = char
                elif char == string_char:
                    in_string = False
                    string_char = None
                current_arg += char
            elif char == "," and not in_string:
                # End of argument
                arg = current_arg.strip()
                if arg:
                    args.append(self.evaluate_simple(arg, context))
                current_arg = ""
            else:
                current_arg += char

            i += 1

        # Add the last argument
        if current_arg.strip():
            args.append(self.evaluate_simple(current_arg.strip(), context))

        return args

    def parse_foreach_expression(self, expression: str) -> tuple[str, str]:
        """Parse t-foreach expression to extract iterable and variable name"""
        # Handle expressions like "doc.order_line" with t-as="line"
        # For now, just return the expression as iterable
        return expression.strip(), "item"


class LoopContext:
    """Context for template loops (t-foreach)"""

    def __init__(self, items: List[Any], variable_name: str):
        self.items = items
        self.variable_name = variable_name
        self.index = 0
        self.length = len(items)

    @property
    def current_item(self) -> Any:
        """Get current loop item"""
        if 0 <= self.index < self.length:
            return self.items[self.index]
        return None

    @property
    def loop_vars(self) -> Dict[str, Any]:
        """Get loop variables (index, first, last, etc.)"""
        loop_obj = type(
            "LoopObject",
            (),
            {
                "index": self.index + 1,  # 1-based index
                "index0": self.index,  # 0-based index
                "first": self.index == 0,
                "last": self.index == self.length - 1,
                "odd": (self.index + 1) % 2 == 1,  # 1-based odd
                "even": (self.index + 1) % 2 == 0,  # 1-based even
                "length": self.length,
                "size": self.length,  # Alias for length
                "revindex": self.length - self.index,
                "revindex0": self.length - self.index - 1,
                "cycle": lambda *args: args[self.index % len(args)] if args else None,
            },
        )()

        return {
            "loop": loop_obj,
            f"{self.variable_name}_index": self.index + 1,
            f"{self.variable_name}_index0": self.index,
            f"{self.variable_name}_first": self.index == 0,
            f"{self.variable_name}_last": self.index == self.length - 1,
            f"{self.variable_name}_odd": (self.index + 1) % 2 == 1,
            f"{self.variable_name}_even": (self.index + 1) % 2 == 0,
            f"{self.variable_name}_size": self.length,
        }

    def next(self) -> bool:
        """Move to next item, return True if successful"""
        self.index += 1
        return self.index < self.length
