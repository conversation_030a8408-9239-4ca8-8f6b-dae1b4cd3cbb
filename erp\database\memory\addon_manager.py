"""
Addon management for database memory registry
"""

import importlib
import os
import sys
from pathlib import Path
from typing import List

from ...logging import get_logger


class AddonManager:
    """Manages addon loading and import operations"""

    def __init__(self, db_name: str):
        self.db_name = db_name
        self.addon_load_order: List[str] = []
        self._addons_loaded = False
        self._addons_imported = False
        self._cached_addon_modules = None
        self._logger = get_logger(f"{__name__}.{db_name}")

    async def set_addon_load_order(self, addon_order: List[str]) -> None:
        """Set the addon loading order"""
        self.addon_load_order = addon_order.copy()
        self._addons_loaded = False  # Reset to allow reloading
        self._logger.info(f"Addon load order set: {self.addon_load_order}")

    async def get_addon_load_order(self) -> List[str]:
        """Get the addon load order"""
        return self.addon_load_order.copy()

    async def load_addons_in_order(self) -> None:
        """Load addons in installation order"""
        if self._addons_loaded:
            return

        if not self.addon_load_order:
            self._logger.warning(
                "No addon load order specified, skipping addon loading"
            )
            return

        try:
            self._logger.info(
                f"Loading addons in installation order: {self.addon_load_order}"
            )
            loaded_count = 0
            failed_count = 0

            for addon_name in self.addon_load_order:
                try:
                    await self._load_single_addon(addon_name)
                    loaded_count += 1
                except Exception as e:
                    self._logger.error(f"Failed to load addon {addon_name}: {e}")
                    failed_count += 1
                    continue

            self._addons_loaded = True
            self._logger.info(
                f"Addon loading completed: {loaded_count} loaded, {failed_count} failed"
            )

        except Exception as e:
            self._logger.error(f"Critical error during addon loading: {e}")
            raise

    async def _load_single_addon(self, addon_name: str) -> None:
        """Load a single addon with its models and routes"""
        try:
            module_name = f"erp.addons.{addon_name}"
            if module_name not in sys.modules:
                # Register addon path for import system
                from ...addons import _addon_import_manager

                addon_path = os.path.join("addons", addon_name)
                _addon_import_manager.register_addon_path(addon_name, addon_path)

                # Import main addon module
                importlib.import_module(module_name)

                # Import all Python modules in the addon directory recursively
                self._import_addon_modules_recursive(module_name, addon_path)

            self._logger.debug(f"Loaded addon: {addon_name}")

        except Exception as e:
            self._logger.debug(f"Failed to load addon {addon_name}: {e}")
            raise

    def _import_addon_modules_recursive(
        self, module_name: str, addon_path: str
    ) -> None:
        """
        Recursively import all Python modules in an addon directory

        Args:
            module_name: Base module name (e.g., 'erp.addons.my_addon')
            addon_path: Path to the addon directory
        """
        try:
            addon_dir = Path(addon_path)
            if not addon_dir.exists():
                self._logger.debug(f"Addon directory does not exist: {addon_path}")
                return

            # Recursively find and import all Python files
            visited_modules = set()
            self._import_directory_recursive(addon_dir, module_name, visited_modules)

        except Exception as e:
            self._logger.debug(f"Error importing modules from {addon_path}: {e}")

    def _import_directory_recursive(
        self, directory: Path, base_module_path: str, visited_modules: set
    ) -> None:
        """
        Recursively import Python modules from a directory

        Args:
            directory: Directory path to scan
            base_module_path: Base module path for imports
            visited_modules: Set of already visited module names to avoid duplicates
        """
        try:
            # Find all Python files in the directory and subdirectories
            for py_file in directory.rglob("*.py"):
                # Skip files that start with __ (like __init__.py, __pycache__, etc.)
                if py_file.name.startswith("__"):
                    continue

                # Skip hidden files and directories
                if any(part.startswith(".") for part in py_file.parts):
                    continue

                # Skip __pycache__ directories
                if "__pycache__" in py_file.parts:
                    continue

                # Calculate relative path from the addon directory
                relative_path = py_file.relative_to(directory)

                # Convert file path to module name
                module_parts = list(relative_path.parts[:-1]) + [relative_path.stem]
                module_name = f"{base_module_path}.{'.'.join(module_parts)}"

                # Skip if already visited
                if module_name in visited_modules:
                    continue

                visited_modules.add(module_name)

                # Try to import the module
                try:
                    importlib.import_module(module_name)
                    self._logger.debug(f"Imported module: {module_name}")
                except Exception as e:
                    self._logger.debug(f"Failed to import {module_name}: {e}")

        except Exception as e:
            self._logger.debug(f"Error scanning directory {directory}: {e}")

    async def import_addon_modules(self) -> None:
        """Import addon modules in installation order"""
        if self._addons_imported:
            return  # Already imported

        # If we have addon load order, use it; otherwise fall back to directory scanning
        if self.addon_load_order:
            for addon_name in self.addon_load_order:
                try:
                    await self._load_single_addon(addon_name)
                except Exception as e:
                    self._logger.debug(f"Failed to import addon {addon_name}: {e}")
        else:
            # Fallback to directory scanning if no load order available
            await self._import_addons_from_directory()

        self._addons_imported = True

    async def _import_addons_from_directory(self) -> None:
        """Fallback method to import addons from directory scanning"""
        from ...config import config

        # Get addon paths from config
        addon_paths = getattr(config, "addons_paths", [])
        if not addon_paths:
            addon_paths = [getattr(config, "addons_path", "addons")]

        for addons_path in addon_paths:
            if not os.path.exists(addons_path):
                continue

            for item in os.listdir(addons_path):
                if not self._is_valid_addon(addons_path, item):
                    continue

                try:
                    self._import_addon_and_controllers(item)
                except Exception as e:
                    self._logger.debug(f"Failed to import addon {item}: {e}")

    def _is_valid_addon(self, addons_path: str, item: str) -> bool:
        """Check if item is a valid addon directory"""
        addon_path = os.path.join(addons_path, item)

        return (
            os.path.isdir(addon_path)
            and not item.startswith(".")
            and item != "__pycache__"
            and os.path.exists(os.path.join(addon_path, "__manifest__.py"))
        )

    def _import_addon_and_controllers(self, addon_name: str) -> None:
        """Optimized addon and controller import"""
        # Check if already imported
        module_name = f"erp.addons.{addon_name}"
        if module_name in sys.modules:
            return

        try:
            # Register addon path for import system
            from ...addons import _addon_import_manager

            addon_path = os.path.join("addons", addon_name)
            _addon_import_manager.register_addon_path(addon_name, addon_path)

            # Import main addon module
            importlib.import_module(module_name)

            # Import controllers efficiently
            self._import_controllers(addon_name, addon_path)

        except Exception as e:
            self._logger.debug(f"Failed to import addon {addon_name}: {e}")

    def _import_controllers(self, addon_name: str, addon_path: str) -> None:
        """Import controllers for an addon"""
        controllers_path = os.path.join(addon_path, "controllers")
        if not os.path.exists(controllers_path):
            return

        for controller_file in os.listdir(controllers_path):
            if not (
                controller_file.endswith(".py") and not controller_file.startswith("__")
            ):
                continue

            controller_name = controller_file[:-3]
            controller_module = f"erp.addons.{addon_name}.controllers.{controller_name}"

            if controller_module not in sys.modules:
                try:
                    importlib.import_module(controller_module)
                except ImportError:
                    pass  # Skip controllers that can't be imported

    def get_addon_modules_cached(self):
        """Get addon modules with caching for better performance"""
        if self._cached_addon_modules is None:
            self._cached_addon_modules = []
            for module_name, module in sys.modules.items():
                if self._is_addon_module(module_name) and module:
                    self._cached_addon_modules.append(module)

        return self._cached_addon_modules

    def _is_addon_module(self, module_name: str) -> bool:
        """Check if module name is an addon module"""
        return (
            module_name.startswith("erp.addons.") or module_name.startswith("addons.")
        ) and module_name != "addons"

    def reset_addon_state(self) -> None:
        """Reset addon loading state for reloading"""
        self._addons_loaded = False
        self._addons_imported = False
        self._cached_addon_modules = None
        self.addon_load_order.clear()
        self._logger.debug("Addon state reset")
