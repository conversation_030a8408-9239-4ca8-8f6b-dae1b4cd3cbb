"""
Interface definitions for HTTP routing system
Provides clear contracts for different components to reduce coupling
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

from .metadata import AuthType, RouteType


class RouteScope(Enum):
    """Scope of route registration"""

    SYSTEM = "system"
    DATABASE = "database"
    ADDON = "addon"


@dataclass
class RouteInfo:
    """Immutable route information"""

    path: str
    handler: Callable
    methods: List[str]
    route_type: RouteType
    auth: AuthType
    scope: RouteScope
    metadata: Dict[str, Any]
    source: Optional[str] = None  # Source module/addon name


class IRouteRegistry(ABC):
    """Interface for route registries"""

    @abstractmethod
    async def register_route(self, route_info: RouteInfo) -> bool:
        """Register a route"""
        pass

    @abstractmethod
    async def unregister_route(
        self, path: str, methods: Optional[List[str]] = None
    ) -> bool:
        """Unregister a route"""
        pass

    @abstractmethod
    async def get_routes(self) -> Dict[str, List[RouteInfo]]:
        """Get all registered routes grouped by path"""
        pass

    @abstractmethod
    async def get_route(self, path: str, method: str) -> Optional[RouteInfo]:
        """Get specific route by path and method"""
        pass

    @abstractmethod
    async def clear(self) -> None:
        """Clear all routes"""
        pass


class IRouteHandler(ABC):
    """Interface for route handlers"""

    @abstractmethod
    async def handle_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Handle incoming request"""
        pass

    @abstractmethod
    def supports_route_type(self, route_type: RouteType) -> bool:
        """Check if handler supports the route type"""
        pass


class IRouteDiscovery(ABC):
    """Interface for route discovery services"""

    @abstractmethod
    async def discover_routes(self, source: str, scope: RouteScope) -> List[RouteInfo]:
        """Discover routes from a source"""
        pass

    @abstractmethod
    async def refresh_routes(self, source: str) -> List[RouteInfo]:
        """Refresh routes from a source"""
        pass


class IRouteValidator(ABC):
    """Interface for route validation"""

    @abstractmethod
    async def validate_route(self, route_info: RouteInfo) -> List[str]:
        """Validate route, return list of errors"""
        pass

    @abstractmethod
    async def validate_registry(self, registry: IRouteRegistry) -> Dict[str, List[str]]:
        """Validate entire registry, return errors by path"""
        pass


class IMiddleware(ABC):
    """Interface for middleware components"""

    @abstractmethod
    async def process_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Process incoming request"""
        pass

    @abstractmethod
    async def process_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Process outgoing response"""
        pass

    @abstractmethod
    def get_priority(self) -> int:
        """Get middleware priority (lower = higher priority)"""
        pass


class IRouteAdapter(ABC):
    """Interface for adapting routes to different frameworks"""

    @abstractmethod
    async def adapt_route(self, route_info: RouteInfo) -> Any:
        """Adapt route for specific framework"""
        pass

    @abstractmethod
    def supports_framework(self, framework: str) -> bool:
        """Check if adapter supports the framework"""
        pass


class IRouteLifecycle(ABC):
    """Interface for route lifecycle management"""

    @abstractmethod
    async def on_route_registered(self, route_info: RouteInfo) -> None:
        """Called when route is registered"""
        pass

    @abstractmethod
    async def on_route_unregistered(self, route_info: RouteInfo) -> None:
        """Called when route is unregistered"""
        pass

    @abstractmethod
    async def on_registry_cleared(self, scope: RouteScope) -> None:
        """Called when registry is cleared"""
        pass


class IHealthCheck(ABC):
    """Interface for health check services"""

    @abstractmethod
    async def check_route_health(self, route_info: RouteInfo) -> Dict[str, Any]:
        """Check health of a specific route"""
        pass

    @abstractmethod
    async def check_registry_health(self, registry: IRouteRegistry) -> Dict[str, Any]:
        """Check health of entire registry"""
        pass


# Exception classes for better error handling
class RouteRegistrationError(Exception):
    """Raised when route registration fails"""

    pass


class RouteValidationError(Exception):
    """Raised when route validation fails"""

    pass


class RouteNotFoundError(Exception):
    """Raised when route is not found"""

    pass


class MiddlewareError(Exception):
    """Raised when middleware processing fails"""

    pass
