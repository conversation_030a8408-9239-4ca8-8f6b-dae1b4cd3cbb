"""
Database registry for managing multiple database connections
"""

from typing import Dict, List, Optional

from ...logging import get_logger
from ..connection.manager import DatabaseManager


class DatabaseRegistry:
    """Registry for managing multiple database connections"""

    _databases: Dict[str, DatabaseManager] = {}
    _current_db: Optional[str] = None
    _logger = get_logger(__name__)

    @classmethod
    async def get_database(cls, db_name: str) -> DatabaseManager:
        """Get database manager for given database name"""
        if db_name not in cls._databases:
            cls._logger.debug(f"Creating new database manager for: {db_name}")
            cls._databases[db_name] = DatabaseManager(db_name)
            await cls._databases[db_name].create_pool()
            cls._logger.info(
                f"Database manager created and pool established for: {db_name}"
            )
        else:
            cls._logger.debug(f"Returning existing database manager for: {db_name}")
        return cls._databases[db_name]

    @classmethod
    def set_current_database(cls, db_name: str):
        """Set current active database"""
        cls._logger.debug(f"Setting current database to: {db_name}")
        cls._current_db = db_name

    @classmethod
    async def get_current_database(cls) -> Optional[DatabaseManager]:
        """Get current active database manager"""
        if cls._current_db:
            return await cls.get_database(cls._current_db)
        return None

    @classmethod
    async def list_databases(cls) -> List[str]:
        """List available databases"""
        # Get default database manager to query system
        default_db = await cls.get_database("postgres")

        try:
            query = """
                SELECT datname FROM pg_database 
                WHERE datistemplate = false 
                AND datname != 'postgres'
                ORDER BY datname
            """
            rows = await default_db.fetch(query)
            return [row["datname"] for row in rows]
        except Exception as e:
            cls._logger.error(f"Error listing databases: {e}")
            return []

    @classmethod
    async def close_all(cls):
        """Close all database connections"""
        for db_name, db_manager in cls._databases.items():
            try:
                await db_manager.close_pool()
                cls._logger.debug(f"Closed connection to database: {db_name}")
            except Exception as e:
                cls._logger.error(f"Error closing database {db_name}: {e}")

        cls._databases.clear()
        cls._current_db = None

    @classmethod
    def get_database_names(cls) -> List[str]:
        """Get list of currently connected database names"""
        return list(cls._databases.keys())

    @classmethod
    def get_current_database_name(cls) -> Optional[str]:
        """Get current database name"""
        return cls._current_db
