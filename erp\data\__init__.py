"""
Enhanced Data loading infrastructure for ERP system

This package provides comprehensive XML data file parsing and loading capabilities
with modular processors, validation, transformation, and error handling.
Similar to Odoo's data loading mechanism but with enhanced features.
"""

from .exceptions import (
    DataLoadingError,
    ModelNotFoundError,
    RecordCreationError,
    XMLIDError,
    XMLParsingError,
)
from .loader import DataLoader
from .parser import FieldType, ParseMode, XMLDataParser

# Import processors
from .processors import (
    ActionProcessor,
    BaseDataProcessor,
    MenuProcessor,
    ProcessorError,
    ProcessorResult,
    RecordProcessor,
    SecurityProcessor,
    ViewProcessor,
    WorkflowProcessor,
)
from .processors.manager import ProcessingResult, ProcessorManager
from .sql_helpers import ModelSQLHelpers, SQLHelpers, XMLIDSQLHelpers

# Import validation and transformation utilities
from .validation import (
    DataTransformer,
    DataValidator,
    FieldTransformer,
    FieldValidator,
    ModelValidator,
    TransformationError,
    TransformationResult,
    TypeConverter,
    ValidationError,
    ValidationResult,
)
from .xmlid_manager import XMLIDManager

__all__ = [
    # Core components
    "DataLoader",
    "XMLDataParser",
    "ParseMode",
    "FieldType",
    "XMLIDManager",
    # SQL helpers
    "SQLHelpers",
    "ModelSQLHelpers",
    "XMLIDSQLHelpers",
    # Processors
    "BaseDataProcessor",
    "ProcessorResult",
    "ProcessorError",
    "RecordProcessor",
    "ViewProcessor",
    "MenuProcessor",
    "ActionProcessor",
    "SecurityProcessor",
    "WorkflowProcessor",
    "ProcessorManager",
    "ProcessingResult",
    # Validation and transformation
    "DataValidator",
    "FieldValidator",
    "ModelValidator",
    "ValidationResult",
    "ValidationError",
    "DataTransformer",
    "FieldTransformer",
    "TypeConverter",
    "TransformationResult",
    "TransformationError",
    # Exceptions
    "DataLoadingError",
    "XMLParsingError",
    "ModelNotFoundError",
    "RecordCreationError",
    "XMLIDError",
]
