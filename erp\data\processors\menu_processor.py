"""
Menu Processor for ERP system

Specialized processor for handling menu items with proper hierarchy,
action references, and access control.
"""

from typing import Any, Dict, List, Optional

from ..sql_helpers import ModelSQLHelpers, SQLHelpers
from ..xmlid_manager import XMLIDManager
from .base import BaseDataProcessor


class MenuProcessor(BaseDataProcessor):
    """
    Processor for menu items (ir.ui.menu)

    Handles creation and updating of menu items with proper hierarchy,
    action references, and sequence management.
    """

    def __init__(self, db_manager, name: str = "MenuProcessor"):
        super().__init__(db_manager, name)

        # Initialize SQL helpers
        self.sql = SQLHelpers(db_manager)
        self.model_sql = ModelSQLHelpers(self.sql)
        self.xmlid_manager = XMLIDManager(db_manager)

        # Menu hierarchy tracking
        self.menu_hierarchy = {}
        self.processed_menus = set()

    def can_process(self, item: Dict[str, Any]) -> bool:
        """Check if this processor can handle the given item"""
        if not isinstance(item, dict):
            return False

        model = item.get("model")
        element_type = item.get("element_type")

        return model == "ir.ui.menu" or element_type == "menuitem"

    def get_supported_models(self) -> List[str]:
        """Get list of models this processor supports"""
        return ["ir.ui.menu"]

    def get_processing_order(self) -> int:
        """Get processing order (process menus after views and actions)"""
        return 30

    async def _process_item(self, item: Dict[str, Any], **kwargs) -> bool:
        """Process a single menu item"""
        xml_id = item.get("xml_id")
        values = item.get("values", {})
        noupdate = item.get("noupdate", False)
        element_type = item.get("element_type")

        try:
            # Handle menuitem element type
            if element_type == "menuitem":
                values = await self._convert_menuitem_attributes(item, values)

            # Validate menu data
            validation_result = await self._validate_menu_data(values)
            if not validation_result["valid"]:
                for error in validation_result["errors"]:
                    self.result.add_error(
                        f"Menu validation error for {xml_id}: {error}"
                    )
                return False

            # Process menu-specific fields
            processed_values = await self._process_menu_values(values)
            if processed_values is None:
                return False

            # Check if menu exists
            existing_record_id = None
            if xml_id:
                existing_record_id = await self._find_record_by_xmlid(xml_id)

            if existing_record_id:
                # Update existing menu
                if not noupdate:
                    success = await self._update_menu(
                        existing_record_id, processed_values
                    )
                    if success:
                        self.logger.debug(f"Updated menu {xml_id}")
                        self.processed_menus.add(xml_id)
                        return True
                else:
                    self.logger.debug(f"Skipped updating menu {xml_id} (noupdate=True)")
                    self.processed_menus.add(xml_id)
                    return True
            else:
                # Create new menu
                new_record_id = await self._create_menu(processed_values)
                if new_record_id:
                    # Store XML ID mapping
                    if xml_id:
                        await self._store_xmlid_mapping(xml_id, new_record_id)

                    self.logger.debug(f"Created menu {xml_id or 'no-id'}")
                    self.processed_menus.add(xml_id)
                    return True

            return False

        except Exception as e:
            error_msg = f"Failed to process menu {xml_id or 'no-id'}: {e}"
            self.result.add_error(error_msg)
            return False

    async def _convert_menuitem_attributes(
        self, item: Dict[str, Any], values: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Convert menuitem element attributes to menu field values"""
        # Extract attributes from the original item
        converted_values = values.copy()

        # Map common menuitem attributes to menu fields
        attribute_mapping = {
            "name": "name",
            "parent": "parent_id",
            "action": "action",
            "sequence": "sequence",
            "groups": "groups_id",
            "web_icon": "web_icon",
        }

        for attr_name, field_name in attribute_mapping.items():
            if attr_name in values and field_name not in converted_values:
                converted_values[field_name] = values[attr_name]

        return converted_values

    async def _validate_menu_data(self, values: Dict[str, Any]) -> Dict[str, Any]:
        """Validate menu data structure"""
        result = {"valid": True, "errors": [], "warnings": []}

        # Check required fields
        if "name" not in values:
            result["errors"].append("Missing required field: name")
            result["valid"] = False

        # Validate parent reference if present
        if "parent_id" in values:
            parent_def = values["parent_id"]
            if isinstance(parent_def, dict) and parent_def.get("type") == "ref":
                parent_ref = parent_def.get("value")
                if parent_ref and not await self._validate_parent_exists(parent_ref):
                    result["warnings"].append(
                        f"Parent menu reference may not exist: {parent_ref}"
                    )

        # Validate action reference if present
        if "action" in values:
            action_def = values["action"]
            if isinstance(action_def, dict) and action_def.get("type") == "ref":
                action_ref = action_def.get("value")
                if action_ref and not await self._validate_action_exists(action_ref):
                    result["warnings"].append(
                        f"Action reference may not exist: {action_ref}"
                    )

        return result

    async def _validate_parent_exists(self, parent_ref: str) -> bool:
        """Validate that parent menu exists"""
        try:
            parent_id = await self.xmlid_manager.resolve_xmlid_to_record_id(parent_ref)
            return parent_id is not None
        except Exception:
            return False

    async def _validate_action_exists(self, action_ref: str) -> bool:
        """Validate that action exists"""
        try:
            action_id = await self.xmlid_manager.resolve_xmlid_to_record_id(action_ref)
            return action_id is not None
        except Exception:
            return False

    async def _process_menu_values(
        self, values: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Process menu-specific field values"""
        processed = {}

        for field_name, field_def in values.items():
            try:
                if field_name == "parent_id":
                    # Special handling for parent reference
                    processed[field_name] = await self._process_parent_field(field_def)
                elif field_name in ["action", "action_id"]:
                    # Special handling for action reference
                    processed[field_name] = await self._process_action_field(field_def)
                elif field_name == "groups_id":
                    # Special handling for groups (Many2many)
                    processed[field_name] = await self._process_groups_field(field_def)
                elif field_name == "sequence":
                    # Special handling for sequence (integer)
                    processed[field_name] = await self._process_sequence_field(
                        field_def
                    )
                else:
                    # Standard field processing
                    processed[field_name] = await self._process_standard_field(
                        field_def
                    )

            except Exception as e:
                error_msg = f"Error processing menu field {field_name}: {e}"
                self.result.add_error(error_msg)
                return None

        return processed

    async def _process_parent_field(self, parent_def: Any) -> Optional[str]:
        """Process parent menu reference"""
        if isinstance(parent_def, dict):
            if parent_def.get("type") == "ref":
                ref_value = parent_def.get("value")
                return await self._resolve_reference(ref_value)
            else:
                return parent_def.get("value")
        else:
            return str(parent_def) if parent_def else None

    async def _process_action_field(self, action_def: Any) -> Optional[str]:
        """Process action reference"""
        if isinstance(action_def, dict):
            if action_def.get("type") == "ref":
                ref_value = action_def.get("value")
                return await self._resolve_reference(ref_value)
            else:
                return action_def.get("value")
        else:
            return str(action_def) if action_def else None

    async def _process_groups_field(self, groups_def: Any) -> Optional[str]:
        """Process groups field (Many2many)"""
        if isinstance(groups_def, dict):
            if groups_def.get("type") == "ref":
                ref_value = groups_def.get("value")
                # For Many2many, this might be a comma-separated list
                if "," in ref_value:
                    group_refs = [ref.strip() for ref in ref_value.split(",")]
                    resolved_ids = []
                    for group_ref in group_refs:
                        group_id = await self._resolve_reference(group_ref)
                        if group_id:
                            resolved_ids.append(group_id)
                    return ",".join(resolved_ids) if resolved_ids else None
                else:
                    return await self._resolve_reference(ref_value)
            else:
                return groups_def.get("value")
        else:
            return str(groups_def) if groups_def else None

    async def _process_sequence_field(self, sequence_def: Any) -> int:
        """Process sequence field"""
        if isinstance(sequence_def, dict):
            value = sequence_def.get("value", "10")
        else:
            value = sequence_def or "10"

        try:
            return int(value)
        except (ValueError, TypeError):
            return 10  # Default sequence

    async def _process_standard_field(self, field_def: Any) -> Any:
        """Process standard field values"""
        if isinstance(field_def, dict):
            field_type = field_def.get("type")
            field_value = field_def.get("value")

            if field_type == "ref":
                return await self._resolve_reference(field_value)
            elif field_type == "eval":
                return self._evaluate_expression(field_value)
            else:
                return field_value
        else:
            return field_def

    async def _resolve_reference(self, ref_value: str) -> Optional[str]:
        """Resolve a reference to another record"""
        try:
            return await self.xmlid_manager.resolve_xmlid_to_record_id(
                ref_value,
                context=f"menu reference in {self.context.get('current_model', 'unknown model')}",
            )
        except Exception as e:
            self.result.add_error(f"Failed to resolve reference {ref_value}: {e}")
            return None

    def _evaluate_expression(self, expression: str) -> Any:
        """Safely evaluate a Python expression"""
        try:
            if expression == "True":
                return True
            elif expression == "False":
                return False
            elif expression == "None":
                return None
            elif expression.startswith("'") and expression.endswith("'"):
                return expression[1:-1]
            elif expression.startswith('"') and expression.endswith('"'):
                return expression[1:-1]
            else:
                try:
                    return int(expression)
                except ValueError:
                    try:
                        return float(expression)
                    except ValueError:
                        return expression
        except Exception:
            return expression

    async def _find_record_by_xmlid(self, xml_id: str) -> Optional[str]:
        """Find a record ID by its XML ID"""
        try:
            return await self.xmlid_manager.resolve_xmlid_to_record_id(xml_id)
        except Exception:
            return None

    async def _create_menu(self, values: Dict[str, Any]) -> Optional[str]:
        """Create a new menu record"""
        try:
            return await self.model_sql.create_record("ir.ui.menu", values)
        except Exception as e:
            self.result.add_error(f"Failed to create menu: {e}")
            return None

    async def _update_menu(self, record_id: str, values: Dict[str, Any]) -> bool:
        """Update an existing menu record"""
        try:
            return await self.model_sql.update_record("ir.ui.menu", record_id, values)
        except Exception as e:
            self.result.add_error(f"Failed to update menu {record_id}: {e}")
            return False

    async def _store_xmlid_mapping(self, xml_id: str, record_id: str):
        """Store XML ID to record ID mapping"""
        try:
            if "." in xml_id:
                module, name = xml_id.split(".", 1)
            else:
                module = self.context.get("addon_name", "base")
                name = xml_id

            await self.xmlid_manager.create_xmlid_mapping(
                module=module, name=name, model="ir.ui.menu", res_id=record_id
            )
        except Exception as e:
            self.result.add_warning(f"Failed to store XML ID mapping for {xml_id}: {e}")
