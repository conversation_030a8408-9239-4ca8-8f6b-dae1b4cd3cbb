"""
Integration tests for single source of truth pattern for addon paths

This module tests that all components use the configuration's addons_paths
property consistently and that the addon path resolver works correctly.
"""

import os
from unittest.mock import patch

import pytest

from erp.addons.utils.path_resolver import AddonPathResolver, get_addon_path_resolver
from erp.config import config


class TestSingleSourceOfTruth:
    """Test single source of truth pattern for addon paths"""

    def test_path_resolver_uses_configuration(self):
        """Test that path resolver gets paths from configuration"""
        resolver = AddonPathResolver()

        # Should get paths from config
        assert resolver.addons_paths == config.addons_paths
        assert resolver.primary_addons_path == config.addons_path

    def test_global_resolver_instance(self):
        """Test that global resolver instance works"""
        resolver1 = get_addon_path_resolver()
        resolver2 = get_addon_path_resolver()

        # Should be the same instance
        assert resolver1 is resolver2

    @pytest.fixture
    def temp_addon_structure(self, tmp_path):
        """Create temporary addon structure for testing"""
        # Create multiple addon paths
        addons_path1 = tmp_path / "addons"
        addons_path2 = tmp_path / "custom_addons"

        addons_path1.mkdir()
        addons_path2.mkdir()

        # Create test addon in first path
        test_addon1 = addons_path1 / "test_addon"
        test_addon1.mkdir()
        (test_addon1 / "__init__.py").write_text("")
        (test_addon1 / "__manifest__.py").write_text(
            """
{
    'name': 'Test Addon',
    'version': '1.0.0',
    'data': ['data/test_data.xml'],
    'installable': True,
}
"""
        )
        (test_addon1 / "data").mkdir()
        (test_addon1 / "data" / "test_data.xml").write_text("<data></data>")

        # Create another addon in second path
        test_addon2 = addons_path2 / "custom_addon"
        test_addon2.mkdir()
        (test_addon2 / "__init__.py").write_text("")
        (test_addon2 / "__manifest__.py").write_text(
            """
{
    'name': 'Custom Addon',
    'version': '1.0.0',
    'installable': True,
}
"""
        )

        return {
            "addons_path1": str(addons_path1),
            "addons_path2": str(addons_path2),
            "test_addon1": str(test_addon1),
            "test_addon2": str(test_addon2),
        }

    def test_path_resolver_finds_addons_in_multiple_paths(self, temp_addon_structure):
        """Test that path resolver can find addons in multiple configured paths"""
        paths = [
            temp_addon_structure["addons_path1"],
            temp_addon_structure["addons_path2"],
        ]

        with patch("erp.addons.utils.path_resolver.config") as mock_config:
            mock_config.addons_paths = paths
            resolver = AddonPathResolver()

            # Should find addon in first path
            addon1_path = resolver.find_addon_path("test_addon")
            assert addon1_path == temp_addon_structure["test_addon1"]

            # Should find addon in second path
            addon2_path = resolver.find_addon_path("custom_addon")
            assert addon2_path == temp_addon_structure["test_addon2"]

            # Should return None for non-existent addon
            assert resolver.find_addon_path("nonexistent") is None

    def test_path_resolver_manifest_operations(self, temp_addon_structure):
        """Test manifest-related operations"""
        paths = [temp_addon_structure["addons_path1"]]

        with patch("erp.addons.utils.path_resolver.config") as mock_config:
            mock_config.addons_paths = paths
            resolver = AddonPathResolver()

            # Should find manifest path
            manifest_path = resolver.get_addon_manifest_path("test_addon")
            assert manifest_path is not None
            assert manifest_path.endswith("__manifest__.py")
            assert os.path.exists(manifest_path)

            # Should return None for non-existent addon
            assert resolver.get_addon_manifest_path("nonexistent") is None

    def test_path_resolver_data_file_operations(self, temp_addon_structure):
        """Test data file path resolution"""
        paths = [temp_addon_structure["addons_path1"]]

        with patch("erp.addons.utils.path_resolver.config") as mock_config:
            mock_config.addons_paths = paths
            resolver = AddonPathResolver()

            # Should find data file
            data_file_path = resolver.get_addon_data_files_path(
                "test_addon", "data/test_data.xml"
            )
            assert data_file_path is not None
            assert data_file_path.endswith("test_data.xml")
            assert os.path.exists(data_file_path)

            # Should return None for non-existent file
            assert (
                resolver.get_addon_data_files_path("test_addon", "nonexistent.xml")
                is None
            )

    def test_path_resolver_list_all_addons(self, temp_addon_structure):
        """Test listing all addons across multiple paths"""
        paths = [
            temp_addon_structure["addons_path1"],
            temp_addon_structure["addons_path2"],
        ]

        with patch("erp.addons.utils.path_resolver.config") as mock_config:
            mock_config.addons_paths = paths
            resolver = AddonPathResolver()

            all_addons = resolver.list_all_addons()

            assert "test_addon" in all_addons
            assert "custom_addon" in all_addons
            assert all_addons["test_addon"] == temp_addon_structure["test_addon1"]
            assert all_addons["custom_addon"] == temp_addon_structure["test_addon2"]

    def test_path_resolver_caching(self, temp_addon_structure):
        """Test that path resolver caches results"""
        paths = [temp_addon_structure["addons_path1"]]

        with patch("erp.addons.utils.path_resolver.config") as mock_config:
            mock_config.addons_paths = paths
            resolver = AddonPathResolver()

            # First call should cache the result
            path1 = resolver.find_addon_path("test_addon")
            assert path1 is not None

            # Second call should use cache
            path2 = resolver.find_addon_path("test_addon")
            assert path1 == path2

            # Cache should contain the addon
            assert "test_addon" in resolver._addon_cache

    def test_path_resolver_cache_invalidation(self, temp_addon_structure):
        """Test cache invalidation when addon is removed"""
        paths = [temp_addon_structure["addons_path1"]]

        with patch("erp.addons.utils.path_resolver.config") as mock_config:
            mock_config.addons_paths = paths
            resolver = AddonPathResolver()

            # Find addon (should cache it)
            path1 = resolver.find_addon_path("test_addon")
            assert path1 is not None
            assert "test_addon" in resolver._addon_cache

            # Remove the addon directory
            import shutil

            shutil.rmtree(temp_addon_structure["test_addon1"])

            # Should detect that cached path no longer exists and remove from cache
            path2 = resolver.find_addon_path("test_addon")
            assert path2 is None
            assert "test_addon" not in resolver._addon_cache

    def test_path_resolver_validation(self, temp_addon_structure):
        """Test configuration validation"""
        # Test with valid paths
        paths = [
            temp_addon_structure["addons_path1"],
            temp_addon_structure["addons_path2"],
        ]

        with patch("erp.addons.utils.path_resolver.config") as mock_config:
            mock_config.addons_paths = paths
            resolver = AddonPathResolver()
            errors = resolver.validate_configuration()
            assert len(errors) == 0

        # Test with invalid paths
        invalid_paths = ["/nonexistent/path1", "/nonexistent/path2"]

        with patch("erp.addons.utils.path_resolver.config") as mock_config:
            mock_config.addons_paths = invalid_paths
            resolver = AddonPathResolver()
            errors = resolver.validate_configuration()
            assert len(errors) == 2
            assert all("does not exist" in error for error in errors)

        # Test with empty paths
        with patch("erp.addons.utils.path_resolver.config") as mock_config:
            mock_config.addons_paths = []
            resolver = AddonPathResolver()
            errors = resolver.validate_configuration()
            assert len(errors) == 1
            assert "No addon paths configured" in errors[0]


class TestComponentIntegration:
    """Test that components use the path resolver correctly"""

    def test_ir_metadata_populator_integration(self):
        """Test that IR metadata populator can be imported and used"""
        from erp.addons.installers.components.ir_metadata_populator import (
            IRMetadataPopulator,
        )

        populator = IRMetadataPopulator()
        assert populator is not None
        assert hasattr(populator, "populate_addon_ir_metadata")

    def test_xml_data_loader_integration(self):
        """Test that XML data loader uses path resolver"""
        from erp.addons.installers.components.xml_data_loader import XMLDataLoader

        loader = XMLDataLoader()
        assert loader is not None
        assert hasattr(loader, "path_resolver")
        assert loader.path_resolver is not None

    def test_addon_installer_integration(self):
        """Test that addon installer can be imported and uses components"""
        from erp.addons.installers import AddonInstaller

        installer = AddonInstaller()
        assert installer is not None
        assert hasattr(installer, "utilities")


class TestConvenienceFunctions:
    """Test convenience functions work correctly"""

    def test_convenience_functions_import(self):
        """Test that convenience functions can be imported"""
        from erp.addons.utils.path_resolver import (
            find_addon_path,
            get_addon_data_file_path,
            get_addon_manifest_path,
            list_all_addons,
        )

        # Functions should be callable
        assert callable(find_addon_path)
        assert callable(get_addon_manifest_path)
        assert callable(get_addon_data_file_path)
        assert callable(list_all_addons)

    def test_convenience_functions_delegate_to_resolver(self, temp_addon_structure):
        """Test that convenience functions delegate to the global resolver"""
        from erp.addons.utils.path_resolver import find_addon_path

        paths = [temp_addon_structure["addons_path1"]]

        with patch("erp.addons.utils.path_resolver.config") as mock_config:
            mock_config.addons_paths = paths
            # Should find the addon using convenience function
            addon_path = find_addon_path("test_addon")
            assert addon_path == temp_addon_structure["test_addon1"]


class TestInstallerModularity:
    """Test that installer components are properly modularized"""

    def test_components_are_separate_modules(self):
        """Test that components are in separate modules"""
        # Should be able to import components separately
        from erp.addons.installers.components.ir_metadata_populator import (
            IRMetadataPopulator,
        )
        from erp.addons.installers.components.xml_data_loader import XMLDataLoader

        # Components should be independent
        ir_populator = IRMetadataPopulator()
        xml_loader = XMLDataLoader()

        assert ir_populator is not None
        assert xml_loader is not None
        assert type(ir_populator) != type(xml_loader)

    def test_components_have_focused_responsibilities(self):
        """Test that each component has focused responsibilities"""
        from erp.addons.installers.components.ir_metadata_populator import (
            IRMetadataPopulator,
        )
        from erp.addons.installers.components.xml_data_loader import XMLDataLoader

        # IR metadata populator should only handle IR operations
        ir_methods = [
            method for method in dir(IRMetadataPopulator) if not method.startswith("_")
        ]
        assert "populate_addon_ir_metadata" in ir_methods
        assert "remove_addon_ir_metadata" in ir_methods
        assert "validate_ir_metadata" in ir_methods

        # XML data loader should only handle XML operations
        xml_methods = [
            method for method in dir(XMLDataLoader) if not method.startswith("_")
        ]
        assert "load_addon_data_files" in xml_methods
        assert "validate_data_files_exist" in xml_methods
        assert "get_addon_data_files_list" in xml_methods
