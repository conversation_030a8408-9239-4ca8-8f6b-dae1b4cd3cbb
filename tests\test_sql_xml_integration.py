"""
Integration tests for SQL-based XML loading system

This test suite verifies that the complete SQL-based XML loading system
works end-to-end without any dependency on AppRegistry.
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from erp.addons.installers.components.xml_data_loader import XMLDataLoader
from erp.data.loader import DataLoader
from erp.data.xmlid_manager import XMLIDManager
from erp.database.connection.manager import DatabaseManager


class TestSQLXMLIntegration:
    """Integration tests for SQL-based XML loading"""

    @pytest.fixture
    def sample_xml_content(self):
        """Sample XML content for testing"""
        return """<?xml version="1.0" encoding="utf-8"?>
<data>
    <record id="test_category" model="ir.module.category">
        <field name="name">Test Category</field>
        <field name="description">A test category for testing</field>
        <field name="sequence">10</field>
    </record>
    
    <record id="test_group" model="res.groups">
        <field name="name">Test Group</field>
        <field name="category_id" ref="test_category"/>
        <field name="comment">Test group with reference</field>
    </record>
    
    <record id="test_user" model="res.users">
        <field name="name">Test User</field>
        <field name="login">testuser</field>
        <field name="email"><EMAIL></field>
        <field name="active" eval="True"/>
        <field name="groups_id" ref="test_group"/>
    </record>
</data>"""

    @pytest.fixture
    def mock_db_manager(self):
        """Mock database manager with realistic behavior"""
        db_manager = AsyncMock(spec=DatabaseManager)

        # Mock table existence checks
        def table_exists_side_effect(query, table_name):
            # Simulate that our test tables exist
            test_tables = [
                "ir_module_category",
                "res_groups",
                "res_users",
                "ir_model_data",
            ]
            return table_name in test_tables

        db_manager.fetchval.side_effect = table_exists_side_effect

        # Mock column queries
        def fetch_side_effect(query, *args):
            if "information_schema.columns" in query:
                # Return standard columns for any table
                return [
                    {"column_name": "id"},
                    {"column_name": "name"},
                    {"column_name": "create_date"},
                    {"column_name": "write_date"},
                ]
            return []

        db_manager.fetch.side_effect = fetch_side_effect

        # Mock record operations
        db_manager.fetchrow.return_value = None  # No existing records
        db_manager.execute.return_value = "INSERT 0 1"

        # Track created records
        db_manager._created_records = {}
        db_manager._xmlid_mappings = {}

        def insert_side_effect(query, *args):
            # Generate a fake ID for inserted records
            import uuid

            record_id = str(uuid.uuid4())

            # Store the record data for later retrieval
            if "ir_model_data" in query:
                # This is an XML ID mapping
                module, name, model, res_id = args[1:5]  # Skip the ID
                xmlid_key = f"{module}.{name}"
                db_manager._xmlid_mappings[xmlid_key] = {
                    "model": model,
                    "res_id": res_id,
                    "id": res_id,
                }
            else:
                # This is a regular record
                db_manager._created_records[record_id] = {
                    "id": record_id,
                    "query": query,
                    "args": args,
                }

            return record_id

        db_manager.fetchval.side_effect = lambda query, *args: (
            insert_side_effect(query, *args)
            if "INSERT" in query or "RETURNING" in query
            else table_exists_side_effect(query, args[0] if args else "")
        )

        # Mock XML ID lookups
        def fetchrow_side_effect(query, *args):
            if "ir_model_data" in query and "WHERE" in query:
                # This is an XML ID lookup
                for xmlid, data in db_manager._xmlid_mappings.items():
                    if any(arg in xmlid for arg in args if isinstance(arg, str)):
                        return data
            return None

        db_manager.fetchrow.side_effect = fetchrow_side_effect

        return db_manager

    @pytest.mark.asyncio
    async def test_complete_xml_loading_workflow(
        self, sample_xml_content, mock_db_manager
    ):
        """Test the complete XML loading workflow"""
        # Create DataLoader
        loader = DataLoader(mock_db_manager)

        # Load the XML content
        result = await loader.load_data_content(sample_xml_content, "test_module")

        # Verify the result
        assert result["loaded"] > 0
        assert result["errors"] == []

        # Verify that records were created
        assert len(mock_db_manager._created_records) > 0

        # Verify that XML ID mappings were created
        assert len(mock_db_manager._xmlid_mappings) > 0

        # Check specific XML IDs
        assert "test_module.test_category" in mock_db_manager._xmlid_mappings
        assert "test_module.test_group" in mock_db_manager._xmlid_mappings
        assert "test_module.test_user" in mock_db_manager._xmlid_mappings

    @pytest.mark.asyncio
    async def test_xml_reference_resolution(self, mock_db_manager):
        """Test that XML references are properly resolved"""
        loader = DataLoader(mock_db_manager)

        # First, create a record that will be referenced
        category_xml = """<?xml version="1.0" encoding="utf-8"?>
<data>
    <record id="test_category" model="ir.module.category">
        <field name="name">Test Category</field>
    </record>
</data>"""

        await loader.load_data_content(category_xml, "test_module")

        # Now create a record that references the first one
        group_xml = """<?xml version="1.0" encoding="utf-8"?>
<data>
    <record id="test_group" model="res.groups">
        <field name="name">Test Group</field>
        <field name="category_id" ref="test_category"/>
    </record>
</data>"""

        result = await loader.load_data_content(group_xml, "test_module")

        # Verify the reference was resolved
        assert result["loaded"] > 0
        assert result["errors"] == []

    @pytest.mark.asyncio
    async def test_xmlid_manager_independence(self, mock_db_manager):
        """Test that XMLIDManager works independently of AppRegistry"""
        xmlid_manager = XMLIDManager(mock_db_manager)

        # Create an XML ID mapping
        success = await xmlid_manager.create_xmlid_mapping(
            "test_module", "test_record", "test.model", "record-123"
        )
        assert success is True

        # Resolve the XML ID
        result = await xmlid_manager.resolve_xmlid("test_module.test_record")
        assert result is not None
        assert result["model"] == "test.model"
        assert result["res_id"] == "record-123"

        # Test record ID resolution
        record_id = await xmlid_manager.resolve_xmlid_to_record_id(
            "test_module.test_record"
        )
        assert record_id == "record-123"

    @pytest.mark.asyncio
    async def test_xml_data_loader_component(self, mock_db_manager):
        """Test the XMLDataLoader component"""
        xml_loader = XMLDataLoader()

        # Mock the path resolver and manifest reading
        with (
            patch.object(xml_loader.path_resolver, "find_addon_path") as mock_find_path,
            patch.object(
                xml_loader.path_resolver, "get_addon_manifest_path"
            ) as mock_manifest_path,
            patch.object(
                xml_loader.path_resolver, "get_addon_data_files_path"
            ) as mock_data_path,
            patch("builtins.open", create=True) as mock_open,
        ):

            # Setup mocks
            mock_find_path.return_value = "/fake/addon/path"
            mock_manifest_path.return_value = "/fake/addon/__manifest__.py"
            mock_data_path.return_value = "/fake/addon/data/test.xml"

            # Mock manifest content
            mock_manifest = MagicMock()
            mock_manifest.read.return_value = "{'data': ['data/test.xml']}"
            mock_open.return_value.__enter__.return_value = mock_manifest

            # Mock XML file content
            mock_xml = MagicMock()
            mock_xml.read.return_value = """<?xml version="1.0" encoding="utf-8"?>
<data>
    <record id="test_record" model="test.model">
        <field name="name">Test Record</field>
    </record>
</data>"""

            # Configure open to return different mocks based on file path
            def open_side_effect(path, *args, **kwargs):
                if path.endswith("__manifest__.py"):
                    return mock_manifest
                elif path.endswith(".xml"):
                    return mock_xml
                return MagicMock()

            mock_open.side_effect = open_side_effect

            # Test loading addon data files
            result = await xml_loader.load_addon_data_files(
                mock_db_manager, "test_addon"
            )

            # Verify the result
            assert result.get("success", False) is True
            assert result.get("total_loaded", 0) > 0

    @pytest.mark.asyncio
    async def test_error_handling(self, mock_db_manager):
        """Test error handling in SQL-based XML loading"""
        loader = DataLoader(mock_db_manager)

        # Test with invalid XML
        invalid_xml = """<?xml version="1.0" encoding="utf-8"?>
<data>
    <record id="test_record" model="nonexistent.model">
        <field name="name">Test Record</field>
    </record>
</data>"""

        # Mock table_exists to return False for nonexistent model
        original_fetchval = mock_db_manager.fetchval.side_effect

        def fetchval_with_error(query, *args):
            if args and args[0] == "nonexistent_model":
                return False
            return original_fetchval(query, *args)

        mock_db_manager.fetchval.side_effect = fetchval_with_error

        result = await loader.load_data_content(invalid_xml, "test_module")

        # Should have errors
        assert len(result["errors"]) > 0
        assert result["loaded"] == 0

    @pytest.mark.asyncio
    async def test_noupdate_functionality(self, mock_db_manager):
        """Test noupdate functionality"""
        loader = DataLoader(mock_db_manager)

        # First, create a record
        xml_content = """<?xml version="1.0" encoding="utf-8"?>
<data>
    <record id="test_record" model="test.model">
        <field name="name">Original Name</field>
    </record>
</data>"""

        await loader.load_data_content(xml_content, "test_module")

        # Now try to update it with noupdate=True
        update_xml = """<?xml version="1.0" encoding="utf-8"?>
<data>
    <record id="test_record" model="test.model" noupdate="1">
        <field name="name">Updated Name</field>
    </record>
</data>"""

        # Mock that the record exists
        mock_db_manager.fetchrow.return_value = {"id": "existing-record-123"}

        result = await loader.load_data_content(update_xml, "test_module")

        # Should be successful but not actually update
        assert result["loaded"] > 0
        assert result["errors"] == []

    @pytest.mark.asyncio
    async def test_eval_field_processing(self, mock_db_manager):
        """Test evaluation of field expressions"""
        loader = DataLoader(mock_db_manager)

        xml_content = """<?xml version="1.0" encoding="utf-8"?>
<data>
    <record id="test_record" model="test.model">
        <field name="name">Test Record</field>
        <field name="active" eval="True"/>
        <field name="sequence" eval="10"/>
        <field name="description" eval="'Evaluated String'"/>
    </record>
</data>"""

        result = await loader.load_data_content(xml_content, "test_module")

        # Should process eval fields correctly
        assert result["loaded"] > 0
        assert result["errors"] == []

    @pytest.mark.asyncio
    async def test_cache_functionality(self, mock_db_manager):
        """Test XML ID caching functionality"""
        xmlid_manager = XMLIDManager(mock_db_manager)

        # Create an XML ID mapping
        await xmlid_manager.create_xmlid_mapping(
            "test_module", "test_record", "test.model", "record-123"
        )

        # First resolution should hit the database
        result1 = await xmlid_manager.resolve_xmlid("test_module.test_record")

        # Second resolution should use cache
        result2 = await xmlid_manager.resolve_xmlid("test_module.test_record")

        assert result1 == result2

        # Test cache clearing
        xmlid_manager.clear_cache()

        # After clearing cache, should hit database again
        result3 = await xmlid_manager.resolve_xmlid("test_module.test_record")
        assert result3 == result1
