"""
SQL query logging functionality
"""

import logging

from ...config import config
from ...logging import get_logger, log_structured


class SQLLogger:
    """SQL query logger with configurable options"""

    def __init__(self, db_name: str):
        self.db_name = db_name
        self.logger = get_logger(f"{__name__}.{self.db_name}")

        # SQL logging configuration
        self._sql_debug_enabled = config.get(
            "options", "log_level", "info"
        ).lower() == "debug" and config.getboolean(
            "options", "sql_logging_enabled", True
        )
        self._max_query_length = config.getint(
            "options", "sql_logging_max_query_length", 500
        )
        self._max_params_length = config.getint(
            "options", "sql_logging_max_params_length", 200
        )

    def log_query(
        self,
        query: str,
        params: tuple = None,
        duration: float = None,
        result_info: str = None,
    ):
        """Log SQL query at debug level if enabled"""
        if not self._sql_debug_enabled:
            return

        # Clean up query for logging (remove extra whitespace)
        clean_query = " ".join(query.split())

        log_data = {
            "operation": "sql_query",
            "database": self.db_name,
            "query": (
                clean_query[: self._max_query_length] + "..."
                if len(clean_query) > self._max_query_length
                else clean_query
            ),
        }

        if params:
            # Convert params to string, truncating if too long
            params_str = str(params)
            log_data["params"] = (
                params_str[: self._max_params_length] + "..."
                if len(params_str) > self._max_params_length
                else params_str
            )

        if duration is not None:
            log_data["duration"] = duration  # Keep as float for formatter

        if result_info:
            log_data["result"] = result_info

        # Create a concise message for the log
        message = f"🗄️ SQL: {clean_query[:100]}{'...' if len(clean_query) > 100 else ''}"
        if duration is not None:
            message += f" ({duration:.3f}s)"
        if result_info:
            message += f" → {result_info}"

        log_structured(self.logger, logging.DEBUG, message, **log_data)

    @property
    def is_enabled(self) -> bool:
        """Check if SQL logging is enabled"""
        return self._sql_debug_enabled
