"""
Test suite for TOTP functionality
"""

import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch


class TestTOTPFunctionality:
    """Test TOTP authentication functionality"""
    
    @pytest.fixture
    def totp_user_model(self):
        """Create a TOTP user model instance for testing"""
        from addons.auth_totp.models.res_users_totp import ResUsersTotp
        return ResUsersTotp
    
    @pytest.fixture
    def sample_user_data(self):
        """Sample user data for testing"""
        return {
            'name': 'Test User',
            'email': '<EMAIL>',
            'login': 'testuser',
            'active': True,
            'totp_enabled': False
        }
    
    def test_totp_model_attributes(self, totp_user_model):
        """Test TOTP model has correct attributes"""
        assert totp_user_model._name == 'res.users'
        assert totp_user_model._inherit == 'res.users'
        
        # Check TOTP fields exist
        fields = totp_user_model._fields
        assert 'totp_secret' in fields
        assert 'totp_enabled' in fields
        assert 'totp_backup_codes' in fields
    
    def test_user_initialization_with_totp(self, totp_user_model, sample_user_data):
        """Test user initialization with TOTP data"""
        user = totp_user_model(**sample_user_data)
        
        assert user.name == 'Test User'
        assert user.email == '<EMAIL>'
        assert user.login == 'testuser'
        assert user.totp_enabled is False
        assert user.totp_secret is None
    
    @patch('addons.auth_totp.models.res_users_totp.pyotp')
    def test_generate_totp_secret(self, mock_pyotp, totp_user_model):
        """Test TOTP secret generation"""
        mock_pyotp.random_base32.return_value = 'JBSWY3DPEHPK3PXP'
        
        user = totp_user_model(login='testuser')
        secret = user.generate_totp_secret()
        
        assert secret == 'JBSWY3DPEHPK3PXP'
        assert user.totp_secret == 'JBSWY3DPEHPK3PXP'
        mock_pyotp.random_base32.assert_called_once()
    
    def test_generate_totp_secret_without_pyotp(self, totp_user_model):
        """Test TOTP secret generation without pyotp library"""
        user = totp_user_model(login='testuser')
        
        with patch('addons.auth_totp.models.res_users_totp.pyotp', None):
            with pytest.raises(ImportError, match="pyotp library is required"):
                user.generate_totp_secret()
    
    @patch('addons.auth_totp.models.res_users_totp.pyotp')
    @patch('addons.auth_totp.models.res_users_totp.qrcode')
    def test_get_totp_qr_code(self, mock_qrcode, mock_pyotp, totp_user_model):
        """Test QR code generation"""
        # Mock TOTP
        mock_totp = MagicMock()
        mock_totp.provisioning_uri.return_value = 'otpauth://totp/test'
        mock_pyotp.totp.TOTP.return_value = mock_totp
        
        # Mock QR code
        mock_qr = MagicMock()
        mock_img = MagicMock()
        mock_qrcode.QRCode.return_value = mock_qr
        mock_qr.make_image.return_value = mock_img
        
        # Mock image save
        with patch('io.BytesIO') as mock_bytesio, \
             patch('base64.b64encode') as mock_b64encode:
            
            mock_buffer = MagicMock()
            mock_bytesio.return_value = mock_buffer
            mock_b64encode.return_value = b'base64data'
            
            user = totp_user_model(
                login='testuser',
                email='<EMAIL>',
                totp_secret='JBSWY3DPEHPK3PXP'
            )
            
            qr_code = user.get_totp_qr_code('Test App')
            
            assert qr_code == 'base64data'
            mock_totp.provisioning_uri.assert_called_once_with(
                name='<EMAIL>',
                issuer_name='Test App'
            )
    
    def test_get_totp_qr_code_without_libraries(self, totp_user_model):
        """Test QR code generation without required libraries"""
        user = totp_user_model(login='testuser', totp_secret='JBSWY3DPEHPK3PXP')
        
        with patch('addons.auth_totp.models.res_users_totp.pyotp', None):
            qr_code = user.get_totp_qr_code()
            assert qr_code is None
    
    @patch('addons.auth_totp.models.res_users_totp.pyotp')
    def test_verify_totp_code_valid(self, mock_pyotp, totp_user_model):
        """Test TOTP code verification with valid code"""
        mock_totp = MagicMock()
        mock_totp.verify.return_value = True
        mock_pyotp.TOTP.return_value = mock_totp
        
        user = totp_user_model(
            login='testuser',
            totp_secret='JBSWY3DPEHPK3PXP',
            totp_enabled=True
        )
        
        result = user.verify_totp_code('123456')
        
        assert result is True
        mock_pyotp.TOTP.assert_called_once_with('JBSWY3DPEHPK3PXP')
        mock_totp.verify.assert_called_once_with('123456', valid_window=1)
    
    @patch('addons.auth_totp.models.res_users_totp.pyotp')
    def test_verify_totp_code_invalid(self, mock_pyotp, totp_user_model):
        """Test TOTP code verification with invalid code"""
        mock_totp = MagicMock()
        mock_totp.verify.return_value = False
        mock_pyotp.TOTP.return_value = mock_totp
        
        user = totp_user_model(
            login='testuser',
            totp_secret='JBSWY3DPEHPK3PXP',
            totp_enabled=True
        )
        
        result = user.verify_totp_code('000000')
        
        assert result is False
    
    def test_verify_totp_code_disabled(self, totp_user_model):
        """Test TOTP code verification when TOTP is disabled"""
        user = totp_user_model(
            login='testuser',
            totp_secret='JBSWY3DPEHPK3PXP',
            totp_enabled=False
        )
        
        result = user.verify_totp_code('123456')
        assert result is False
    
    @patch('addons.auth_totp.models.res_users_totp.secrets')
    def test_generate_backup_codes(self, mock_secrets, totp_user_model):
        """Test backup codes generation"""
        mock_secrets.token_hex.side_effect = ['abcd1234', 'efgh5678', 'ijkl9012']
        
        user = totp_user_model(login='testuser')
        codes = user.generate_backup_codes(3)
        
        assert len(codes) == 3
        assert codes == ['ABCD1234', 'EFGH5678', 'IJKL9012']
        assert user.totp_backup_codes == 'ABCD1234\nEFGH5678\nIJKL9012'
    
    def test_verify_backup_code_valid(self, totp_user_model):
        """Test backup code verification with valid code"""
        user = totp_user_model(
            login='testuser',
            totp_backup_codes='ABCD1234\nEFGH5678\nIJKL9012'
        )
        
        result = user.verify_backup_code('EFGH5678')
        
        assert result is True
        # Code should be removed after use
        assert 'EFGH5678' not in user.totp_backup_codes
        assert 'ABCD1234' in user.totp_backup_codes
        assert 'IJKL9012' in user.totp_backup_codes
    
    def test_verify_backup_code_invalid(self, totp_user_model):
        """Test backup code verification with invalid code"""
        user = totp_user_model(
            login='testuser',
            totp_backup_codes='ABCD1234\nEFGH5678\nIJKL9012'
        )
        
        result = user.verify_backup_code('INVALID')
        
        assert result is False
        # Codes should remain unchanged
        assert user.totp_backup_codes == 'ABCD1234\nEFGH5678\nIJKL9012'
    
    def test_verify_backup_code_no_codes(self, totp_user_model):
        """Test backup code verification when no codes exist"""
        user = totp_user_model(login='testuser')
        
        result = user.verify_backup_code('ABCD1234')
        assert result is False
    
    @patch('addons.auth_totp.models.res_users_totp.pyotp')
    async def test_enable_totp(self, mock_pyotp, totp_user_model):
        """Test enabling TOTP for user"""
        mock_pyotp.random_base32.return_value = 'JBSWY3DPEHPK3PXP'
        
        user = totp_user_model(login='testuser', email='<EMAIL>')
        user.write = AsyncMock()
        user.get_totp_qr_code = MagicMock(return_value='qr_code_data')
        
        with patch.object(user, 'generate_backup_codes') as mock_gen_codes:
            mock_gen_codes.return_value = ['CODE1', 'CODE2']
            
            result = await user.enable_totp('Test App')
            
            assert result['secret'] == 'JBSWY3DPEHPK3PXP'
            assert result['qr_code'] == 'qr_code_data'
            assert result['backup_codes'] == ['CODE1', 'CODE2']
            
            user.write.assert_called_once()
            write_args = user.write.call_args[0][0]
            assert write_args['totp_enabled'] is True
    
    async def test_disable_totp(self, totp_user_model):
        """Test disabling TOTP for user"""
        user = totp_user_model(
            login='testuser',
            totp_enabled=True,
            totp_secret='JBSWY3DPEHPK3PXP'
        )
        user.write = AsyncMock()
        
        await user.disable_totp()
        
        user.write.assert_called_once_with({
            'totp_enabled': False,
            'totp_secret': False,
            'totp_backup_codes': False
        })
    
    async def test_authenticate_with_totp_success(self, totp_user_model):
        """Test authentication with TOTP success"""
        user = totp_user_model(
            login='testuser',
            totp_enabled=True,
            totp_secret='JBSWY3DPEHPK3PXP'
        )
        user.authenticate = AsyncMock(return_value=user)
        user.verify_totp_code = MagicMock(return_value=True)
        
        result = await user.authenticate_with_totp('testuser', 'password', '123456')
        
        assert result == user
        user.authenticate.assert_called_once_with('testuser', 'password')
        user.verify_totp_code.assert_called_once_with('123456')
    
    async def test_authenticate_with_totp_backup_code(self, totp_user_model):
        """Test authentication with backup code"""
        user = totp_user_model(
            login='testuser',
            totp_enabled=True,
            totp_secret='JBSWY3DPEHPK3PXP'
        )
        user.authenticate = AsyncMock(return_value=user)
        user.verify_totp_code = MagicMock(return_value=False)
        user.verify_backup_code = MagicMock(return_value=True)
        
        result = await user.authenticate_with_totp('testuser', 'password', 'BACKUP123')
        
        assert result == user
        user.verify_totp_code.assert_called_once_with('BACKUP123')
        user.verify_backup_code.assert_called_once_with('BACKUP123')
    
    async def test_authenticate_with_totp_no_code(self, totp_user_model):
        """Test authentication with TOTP enabled but no code provided"""
        user = totp_user_model(
            login='testuser',
            totp_enabled=True,
            totp_secret='JBSWY3DPEHPK3PXP'
        )
        user.authenticate = AsyncMock(return_value=user)
        
        result = await user.authenticate_with_totp('testuser', 'password')
        
        assert result is False
    
    async def test_authenticate_with_totp_invalid_code(self, totp_user_model):
        """Test authentication with invalid TOTP code"""
        user = totp_user_model(
            login='testuser',
            totp_enabled=True,
            totp_secret='JBSWY3DPEHPK3PXP'
        )
        user.authenticate = AsyncMock(return_value=user)
        user.verify_totp_code = MagicMock(return_value=False)
        user.verify_backup_code = MagicMock(return_value=False)
        
        result = await user.authenticate_with_totp('testuser', 'password', '000000')
        
        assert result is False
