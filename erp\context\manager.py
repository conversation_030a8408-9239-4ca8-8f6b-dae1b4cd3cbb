"""
Context manager for ERP system
"""

import contextvars
from contextlib import asynccontextmanager
from typing import TYPE_CHECKING, Any, Dict, Optional

from .storage import AsyncLocalStorage

if TYPE_CHECKING:
    from ..environment import Environment


class ContextManager:
    """
    Global context manager for the ERP system
    Manages different types of context storage
    """

    # Environment context storage
    environment: AsyncLocalStorage["Environment"] = AsyncLocalStorage("erp.environment")

    # Request context storage
    request: AsyncLocalStorage[Dict[str, Any]] = AsyncLocalStorage("erp.request")

    # Database context storage
    database: AsyncLocalStorage[str] = AsyncLocalStorage("erp.database")

    # User context storage
    user: AsyncLocalStorage[int] = AsyncLocalStorage("erp.user")

    @classmethod
    def get_environment(cls) -> Optional["Environment"]:
        """Get current environment from context"""
        return cls.environment.get()

    @classmethod
    def set_environment(cls, env: "Environment") -> contextvars.Token:
        """Set current environment in context"""
        return cls.environment.set(env)

    @classmethod
    def get_request_context(cls) -> Dict[str, Any]:
        """Get current request context"""
        return cls.request.get() or {}

    @classmethod
    def set_request_context(cls, context: Dict[str, Any]) -> contextvars.Token:
        """Set current request context"""
        return cls.request.set(context)

    @classmethod
    def get_database(cls) -> Optional[str]:
        """Get current database name from context"""
        return cls.database.get()

    @classmethod
    def set_database(cls, db_name: str) -> contextvars.Token:
        """Set current database name in context"""
        return cls.database.set(db_name)

    @classmethod
    def get_user(cls) -> Optional[int]:
        """Get current user ID from context"""
        return cls.user.get()

    @classmethod
    def set_user(cls, user_id: int) -> contextvars.Token:
        """Set current user ID in context"""
        return cls.user.set(user_id)

    @classmethod
    @asynccontextmanager
    async def with_context(cls, env: "Environment" = None, **context):
        """Context manager to run code with specific context"""
        tokens = []

        try:
            if env:
                tokens.append(cls.set_environment(env))
                tokens.append(cls.set_database(env.cr.db_name))
                tokens.append(cls.set_user(env.uid))

            if context:
                current_context = cls.get_request_context()
                new_context = {**current_context, **context}
                tokens.append(cls.set_request_context(new_context))

            yield

        finally:
            # Reset tokens in reverse order
            for token in reversed(tokens):
                if hasattr(token, "var"):
                    token.var.reset(token)
