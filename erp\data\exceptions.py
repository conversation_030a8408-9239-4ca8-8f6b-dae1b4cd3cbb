"""
Exceptions for data loading system
"""


class DataLoadingError(Exception):
    """Base exception for data loading errors"""

    pass


class XMLParsingError(DataLoadingError):
    """Exception raised when XML parsing fails"""

    pass


class ModelNotFoundError(DataLoadingError):
    """Exception raised when referenced model is not found"""

    pass


class RecordCreationError(DataLoadingError):
    """Exception raised when record creation fails"""

    pass


class XMLIDError(DataLoadingError):
    """Exception raised when XML ID operations fail"""

    pass


class XMLIDNotFoundError(XMLIDError):
    """Exception raised when a referenced XML ID is not found"""

    def __init__(self, xml_id: str, context: str = None):
        """
        Initialize XMLIDNotFoundError.

        Args:
            xml_id: The XML ID that was not found
            context: Additional context about where the error occurred
        """
        self.xml_id = xml_id
        self.context = context

        message = f"XML ID not found: {xml_id}"
        if context:
            message += f" (in {context})"

        super().__init__(message)
