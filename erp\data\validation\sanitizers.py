"""
Data Sanitization Utilities for ERP system

Provides data cleaning, normalization, and sanitization capabilities
for data loading operations.
"""

import html
import re
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, List, Union

from ...logging import get_logger


@dataclass
class SanitizationResult:
    """Result of sanitization operation"""

    success: bool = True
    sanitized_value: Any = None
    original_value: Any = None
    changes_made: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def add_change(self, description: str):
        """Add a change description"""
        self.changes_made.append(description)

    def add_warning(self, message: str):
        """Add a warning message"""
        self.warnings.append(message)


class ValueSanitizer:
    """Base sanitizer for values"""

    def __init__(self):
        self.logger = get_logger(__name__)

    def sanitize_string(
        self, value: str, options: Dict[str, Any] = None
    ) -> SanitizationResult:
        """Sanitize string value"""
        result = SanitizationResult(original_value=value)
        options = options or {}

        if not isinstance(value, str):
            result.sanitized_value = value
            return result

        sanitized = value

        # Strip whitespace
        if options.get("strip", True):
            stripped = sanitized.strip()
            if stripped != sanitized:
                result.add_change("Stripped leading/trailing whitespace")
                sanitized = stripped

        # Normalize whitespace
        if options.get("normalize_whitespace", True):
            normalized = re.sub(r"\s+", " ", sanitized)
            if normalized != sanitized:
                result.add_change("Normalized internal whitespace")
                sanitized = normalized

        # Remove control characters
        if options.get("remove_control_chars", True):
            cleaned = re.sub(r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "", sanitized)
            if cleaned != sanitized:
                result.add_change("Removed control characters")
                sanitized = cleaned

        # HTML escape
        if options.get("html_escape", False):
            escaped = html.escape(sanitized)
            if escaped != sanitized:
                result.add_change("HTML escaped special characters")
                sanitized = escaped

        # Length limit
        max_length = options.get("max_length")
        if max_length and len(sanitized) > max_length:
            truncated = sanitized[:max_length]
            result.add_change(f"Truncated to {max_length} characters")
            result.add_warning(
                f"String truncated from {len(sanitized)} to {max_length} characters"
            )
            sanitized = truncated

        # Case conversion
        case_option = options.get("case")
        if case_option == "lower":
            sanitized = sanitized.lower()
            result.add_change("Converted to lowercase")
        elif case_option == "upper":
            sanitized = sanitized.upper()
            result.add_change("Converted to uppercase")
        elif case_option == "title":
            sanitized = sanitized.title()
            result.add_change("Converted to title case")

        result.sanitized_value = sanitized
        return result

    def sanitize_number(
        self, value: Union[int, float], options: Dict[str, Any] = None
    ) -> SanitizationResult:
        """Sanitize numeric value"""
        result = SanitizationResult(original_value=value)
        options = options or {}

        if not isinstance(value, (int, float)):
            result.sanitized_value = value
            return result

        sanitized = value

        # Range clamping
        min_value = options.get("min_value")
        max_value = options.get("max_value")

        if min_value is not None and sanitized < min_value:
            sanitized = min_value
            result.add_change(f"Clamped to minimum value: {min_value}")
            result.add_warning(f"Value {value} was below minimum {min_value}")

        if max_value is not None and sanitized > max_value:
            sanitized = max_value
            result.add_change(f"Clamped to maximum value: {max_value}")
            result.add_warning(f"Value {value} was above maximum {max_value}")

        # Precision rounding for floats
        if isinstance(sanitized, float):
            precision = options.get("precision")
            if precision is not None:
                rounded = round(sanitized, precision)
                if rounded != sanitized:
                    result.add_change(f"Rounded to {precision} decimal places")
                    sanitized = rounded

        result.sanitized_value = sanitized
        return result

    def sanitize_list(
        self, value: List[Any], options: Dict[str, Any] = None
    ) -> SanitizationResult:
        """Sanitize list value"""
        result = SanitizationResult(original_value=value)
        options = options or {}

        if not isinstance(value, list):
            result.sanitized_value = value
            return result

        sanitized = value.copy()

        # Remove duplicates
        if options.get("remove_duplicates", False):
            unique_list = []
            seen = set()
            for item in sanitized:
                if item not in seen:
                    unique_list.append(item)
                    seen.add(item)
            if len(unique_list) != len(sanitized):
                result.add_change(
                    f"Removed {len(sanitized) - len(unique_list)} duplicate items"
                )
                sanitized = unique_list

        # Remove None values
        if options.get("remove_none", True):
            filtered = [item for item in sanitized if item is not None]
            if len(filtered) != len(sanitized):
                result.add_change(
                    f"Removed {len(sanitized) - len(filtered)} None values"
                )
                sanitized = filtered

        # Remove empty strings
        if options.get("remove_empty_strings", True):
            filtered = [
                item
                for item in sanitized
                if not (isinstance(item, str) and not item.strip())
            ]
            if len(filtered) != len(sanitized):
                result.add_change(
                    f"Removed {len(sanitized) - len(filtered)} empty strings"
                )
                sanitized = filtered

        # Length limit
        max_length = options.get("max_length")
        if max_length and len(sanitized) > max_length:
            truncated = sanitized[:max_length]
            result.add_change(f"Truncated list to {max_length} items")
            result.add_warning(
                f"List truncated from {len(sanitized)} to {max_length} items"
            )
            sanitized = truncated

        result.sanitized_value = sanitized
        return result


class FieldSanitizer:
    """Sanitizer for individual fields"""

    def __init__(self, field_name: str):
        self.field_name = field_name
        self.value_sanitizer = ValueSanitizer()
        self.custom_sanitizers: List[Callable] = []
        self.sanitization_options: Dict[str, Any] = {}
        self.logger = get_logger(__name__)

    def set_options(self, **options):
        """Set sanitization options"""
        self.sanitization_options.update(options)
        return self

    def add_custom_sanitizer(self, sanitizer: Callable):
        """Add custom sanitizer function"""
        self.custom_sanitizers.append(sanitizer)
        return self

    def sanitize(
        self, value: Any, context: Dict[str, Any] = None
    ) -> SanitizationResult:
        """Sanitize field value"""
        current_value = value
        final_result = SanitizationResult(original_value=value)

        # Apply built-in sanitization based on type
        if isinstance(current_value, str):
            result = self.value_sanitizer.sanitize_string(
                current_value, self.sanitization_options
            )
        elif isinstance(current_value, (int, float)):
            result = self.value_sanitizer.sanitize_number(
                current_value, self.sanitization_options
            )
        elif isinstance(current_value, list):
            result = self.value_sanitizer.sanitize_list(
                current_value, self.sanitization_options
            )
        else:
            result = SanitizationResult(
                original_value=current_value, sanitized_value=current_value
            )

        current_value = result.sanitized_value
        final_result.changes_made.extend(result.changes_made)
        final_result.warnings.extend(result.warnings)

        # Apply custom sanitizers
        for sanitizer in self.custom_sanitizers:
            try:
                custom_result = sanitizer(current_value, context)
                if isinstance(custom_result, SanitizationResult):
                    current_value = custom_result.sanitized_value
                    final_result.changes_made.extend(custom_result.changes_made)
                    final_result.warnings.extend(custom_result.warnings)
                else:
                    # Assume the result is the sanitized value
                    current_value = custom_result
                    final_result.add_change("Applied custom sanitizer")
            except Exception as e:
                final_result.add_warning(f"Custom sanitizer error: {e}")

        final_result.sanitized_value = current_value
        return final_result


class DataSanitizer:
    """Main data sanitizer"""

    def __init__(self):
        self.field_sanitizers: Dict[str, FieldSanitizer] = {}
        self.model_sanitizers: Dict[str, Dict[str, FieldSanitizer]] = {}
        self.global_sanitizers: List[Callable] = []
        self.logger = get_logger(__name__)

    def field(self, field_name: str) -> FieldSanitizer:
        """Get or create field sanitizer"""
        if field_name not in self.field_sanitizers:
            self.field_sanitizers[field_name] = FieldSanitizer(field_name)
        return self.field_sanitizers[field_name]

    def model_field(self, model_name: str, field_name: str) -> FieldSanitizer:
        """Get or create model-specific field sanitizer"""
        if model_name not in self.model_sanitizers:
            self.model_sanitizers[model_name] = {}
        if field_name not in self.model_sanitizers[model_name]:
            self.model_sanitizers[model_name][field_name] = FieldSanitizer(field_name)
        return self.model_sanitizers[model_name][field_name]

    def add_global_sanitizer(self, sanitizer: Callable):
        """Add global sanitizer"""
        self.global_sanitizers.append(sanitizer)
        return self

    def sanitize_data(
        self, data: List[Dict[str, Any]], context: Dict[str, Any] = None
    ) -> List[SanitizationResult]:
        """Sanitize a list of data records"""
        results = []
        context = context or {}

        for i, record in enumerate(data):
            try:
                result = self.sanitize_record(record, context)
                results.append(result)
            except Exception as e:
                error_result = SanitizationResult(original_value=record)
                error_result.add_warning(f"Error sanitizing record {i}: {e}")
                error_result.sanitized_value = record  # Keep original on error
                results.append(error_result)

        return results

    def sanitize_record(
        self, record: Dict[str, Any], context: Dict[str, Any] = None
    ) -> SanitizationResult:
        """Sanitize a single record"""
        result = SanitizationResult(original_value=record)
        context = context or {}

        try:
            sanitized_record = record.copy()
            model_name = record.get("model")
            values = record.get("values", {})
            sanitized_values = {}

            # Sanitize field values
            for field_name, field_value in values.items():
                field_result = self._sanitize_field_value(
                    field_name, field_value, model_name, context
                )

                sanitized_values[field_name] = field_result.sanitized_value
                result.changes_made.extend(
                    [f"{field_name}: {change}" for change in field_result.changes_made]
                )
                result.warnings.extend(field_result.warnings)

            sanitized_record["values"] = sanitized_values

            # Apply global sanitizers
            for sanitizer in self.global_sanitizers:
                try:
                    global_result = sanitizer(sanitized_record, context)
                    if isinstance(global_result, SanitizationResult):
                        sanitized_record = global_result.sanitized_value
                        result.changes_made.extend(global_result.changes_made)
                        result.warnings.extend(global_result.warnings)
                    else:
                        sanitized_record = global_result
                        result.add_change("Applied global sanitizer")
                except Exception as e:
                    result.add_warning(f"Global sanitizer error: {e}")

            result.sanitized_value = sanitized_record

        except Exception as e:
            result.add_warning(f"Error sanitizing record: {e}")
            result.sanitized_value = record  # Keep original on error

        return result

    def _sanitize_field_value(
        self,
        field_name: str,
        field_value: Any,
        model_name: str,
        context: Dict[str, Any],
    ) -> SanitizationResult:
        """Sanitize a single field value"""
        # Check for model-specific sanitizer first
        if model_name and model_name in self.model_sanitizers:
            if field_name in self.model_sanitizers[model_name]:
                return self.model_sanitizers[model_name][field_name].sanitize(
                    field_value, context
                )

        # Check for global field sanitizer
        if field_name in self.field_sanitizers:
            return self.field_sanitizers[field_name].sanitize(field_value, context)

        # No sanitization needed
        return SanitizationResult(
            original_value=field_value, sanitized_value=field_value
        )
