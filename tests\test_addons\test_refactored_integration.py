"""
Integration test suite for refactored addon system

This module tests:
- Integration between all refactored components
- Backward compatibility with existing code
- Complete addon lifecycle using new modular structure
- Cross-module communication and dependencies
- End-to-end workflow validation
"""

from unittest.mock import MagicMock, patch

import pytest

# Import all refactored components
from erp.addons import (  # Core components; Core functionality; Installers; Managers; Hooks; Exceptions
    AddonError,
    AddonInstallationError,
    AddonInstaller,
    AddonIRManager,
    AddonManager,
    AddonManifest,
    AddonStateManager,
    CircularDependencyError,
    DependencyError,
    DependencyManager,
    HookContext,
    HookType,
    MissingDependencyError,
    addon_ir_manager,
    core_ir_population_hook,
    ensure_addon_import,
    get_addon_module,
    get_hook_registry,
)

# Import modular components directly
from erp.addons.exceptions import AddonManifestError
from erp.addons.hooks import post_install_hook, pre_install_hook
from erp.addons.installers import InstallationContext
from erp.addons.managers import <PERSON>donInfo, AddonState
from erp.addons.utils import discover_addons_in_path, validate_addon_name


class TestRefactoredSystemIntegration:
    """Test integration of the entire refactored addon system"""

    def test_all_components_importable_from_main_package(self):
        """Test that all components can be imported from main addons package"""
        components = [
            AddonManifest,
            get_addon_module,
            ensure_addon_import,
            core_ir_population_hook,
            AddonIRManager,
            addon_ir_manager,
            AddonInstaller,
            AddonManager,
            DependencyManager,
            AddonStateManager,
            HookType,
            HookContext,
            get_hook_registry,
            AddonError,
            DependencyError,
            CircularDependencyError,
            MissingDependencyError,
            AddonInstallationError,
        ]

        for component in components:
            assert component is not None

    def test_modular_components_importable_directly(self):
        """Test that modular components can be imported directly from their packages"""
        # Test exceptions
        assert AddonManifestError is not None

        # Test hooks
        assert pre_install_hook is not None
        assert post_install_hook is not None

        # Test installers
        assert InstallationContext is not None

        # Test managers
        assert AddonState is not None
        assert AddonInfo is not None

        # Test utils
        assert validate_addon_name is not None
        assert discover_addons_in_path is not None

    @pytest.mark.asyncio
    async def test_complete_addon_lifecycle_workflow(self):
        """Test complete addon lifecycle using refactored components"""
        mock_env = MagicMock()
        mock_env.cr = MagicMock()

        # Create managers
        addon_manager = AddonManager(mock_env)
        dependency_manager = DependencyManager(mock_env)
        state_manager = AddonStateManager(mock_env)

        # Create installer
        installer = AddonInstaller(mock_env)

        # Mock all the operations
        with (
            patch.object(
                dependency_manager,
                "resolve_dependencies",
                return_value=["base", "test_addon"],
            ),
            patch.object(
                dependency_manager, "check_circular_dependencies", return_value=False
            ),
            patch.object(
                dependency_manager, "get_missing_dependencies", return_value=[]
            ),
            patch.object(state_manager, "set_addon_state", return_value=True),
            patch.object(
                state_manager, "get_addon_state", return_value=AddonState.UNINSTALLED
            ),
            patch.object(
                installer,
                "install",
                return_value={"success": True, "message": "Installed"},
            ),
            patch.object(
                addon_manager, "install_addon", return_value={"success": True}
            ),
        ):

            # Simulate complete workflow
            addon_name = "test_addon"
            addon_path = "/path/to/addon"
            dependencies = ["base"]

            # 1. Check current state
            current_state = await state_manager.get_addon_state(addon_name)
            assert current_state == AddonState.UNINSTALLED

            # 2. Resolve dependencies
            install_order = await dependency_manager.resolve_dependencies(
                addon_name, dependencies
            )
            assert addon_name in install_order

            # 3. Check for circular dependencies
            has_circular = await dependency_manager.check_circular_dependencies(
                addon_name, dependencies
            )
            assert has_circular is False

            # 4. Check for missing dependencies
            missing_deps = await dependency_manager.get_missing_dependencies(
                dependencies
            )
            assert missing_deps == []

            # 5. Set state to installing
            state_set = await state_manager.set_addon_state(
                addon_name, AddonState.TO_INSTALL
            )
            assert state_set is True

            # 6. Install addon
            install_result = await installer.install(addon_name, addon_path)
            assert install_result["success"] is True

            # 7. Use high-level manager
            manager_result = await addon_manager.install_addon(addon_name, addon_path)
            assert manager_result["success"] is True

    @pytest.mark.asyncio
    async def test_hook_system_integration(self):
        """Test hook system integration with other components"""
        mock_env = MagicMock()
        mock_env.cr = MagicMock()

        # Create hook context
        context = HookContext(
            hook_type=HookType.POST_INSTALL,
            addon_name="test_addon",
            env=mock_env,
            metadata={"version": "1.0.0"},
        )

        # Test core IR population hook
        with (
            patch(
                "erp.addons.core.core_hooks.SchemaGenerator.sync_model_tables"
            ) as mock_schema,
            patch(
                "erp.addons.core.core_hooks.ir_population_manager.populate_ir_metadata_with_rollback"
            ) as mock_ir,
        ):

            mock_schema.return_value = {
                "sync_successful": True,
                "summary": {"tables_created": 1},
            }
            mock_ir.return_value = {
                "status": "success",
                "models_processed": 2,
                "fields_processed": 10,
            }

            result = await core_ir_population_hook(context)
            assert result is True

        # Test hook registry
        registry = get_hook_registry()
        assert registry is not None

        # Test hook decorators work
        @pre_install_hook(addon_name="test_addon", priority=10)
        def test_pre_hook(ctx):
            return True

        @post_install_hook(addon_name="test_addon", priority=90)
        def test_post_hook(ctx):
            return True

        # Hooks should be registered
        pre_hooks = registry.get_hooks(HookType.PRE_INSTALL, "test_addon")
        post_hooks = registry.get_hooks(HookType.POST_INSTALL, "test_addon")

        # Find our test hooks
        test_pre_found = any(hook.func == test_pre_hook for hook in pre_hooks)
        test_post_found = any(hook.func == test_post_hook for hook in post_hooks)

        assert test_pre_found
        assert test_post_found

    def test_exception_hierarchy_integration(self):
        """Test exception hierarchy and usage"""
        # Test that exceptions can be caught properly
        try:
            raise AddonInstallationError("Test installation error")
        except AddonError as e:
            assert str(e) == "Test installation error"
        except Exception:
            pytest.fail("AddonInstallationError should be caught as AddonError")

        # Test dependency error hierarchy
        try:
            raise CircularDependencyError("Circular dependency detected")
        except DependencyError as e:
            assert str(e) == "Circular dependency detected"
        except Exception:
            pytest.fail("CircularDependencyError should be caught as DependencyError")

        # Test that dependency errors are also addon errors
        try:
            raise MissingDependencyError("Missing dependency")
        except AddonError as e:
            assert str(e) == "Missing dependency"
        except Exception:
            pytest.fail("MissingDependencyError should be caught as AddonError")

    @pytest.mark.asyncio
    async def test_installer_and_manager_integration(self):
        """Test integration between installers and managers"""
        mock_env = MagicMock()

        # Create components
        addon_installer = AddonInstaller()
        addon_manager = AddonManager()
        state_manager = AddonStateManager()

        # Create installation context
        context = InstallationContext(
            env=mock_env,
            addon_name="test_addon",
            addon_path="/path/to/addon",
            operation="install",
        )

        # Mock operations
        with (
            patch.object(addon_installer, "install_addon", return_value=True),
            patch.object(
                addon_manager, "install_addon", return_value=True
            ),
            patch.object(state_manager, "mark_addon_installed", return_value=True),
        ):

            # Test installer integration
            installer_result = await addon_installer.install_addon(
                context.addon_name, mock_env
            )
            assert installer_result is True

            # Test manager integration
            manager_result = await addon_manager.install_addon(
                context.addon_name
            )
            assert manager_result is True

            # Test state management
            state_result = await state_manager.set_addon_state(
                context.addon_name, AddonState.INSTALLED
            )
            assert state_result is True

    def test_utils_integration_with_other_components(self):
        """Test utils integration with other components"""
        # Test that utils can be used by other components
        addon_name = "test_addon_123"

        # Mock utils functions
        with (
            patch(
                "erp.addons.utils.validate_addon_name", return_value=True
            ) as mock_validate,
            patch(
                "erp.addons.utils.discover_addons_in_path",
                return_value=["addon1", "addon2"],
            ) as mock_discover,
        ):

            # Test validation
            is_valid = validate_addon_name(addon_name)
            assert is_valid is True
            mock_validate.assert_called_once_with(addon_name)

            # Test discovery
            addons = discover_addons_in_path("/path/to/addons")
            assert addons == ["addon1", "addon2"]
            mock_discover.assert_called_once_with("/path/to/addons")

    @pytest.mark.asyncio
    async def test_error_handling_across_components(self):
        """Test error handling across different components"""
        mock_env = MagicMock()

        # Create components
        addon_manager = AddonManager(mock_env)
        installer = AddonInstaller(mock_env)

        # Test error propagation
        with patch.object(
            installer,
            "install",
            side_effect=AddonInstallationError("Installation failed"),
        ):

            # The manager should handle installer errors appropriately
            with patch.object(addon_manager, "install_addon") as mock_install:
                mock_install.side_effect = AddonInstallationError("Installation failed")

                try:
                    await addon_manager.install_addon("test_addon", "/path")
                    pytest.fail("Should have raised AddonInstallationError")
                except AddonInstallationError as e:
                    assert str(e) == "Installation failed"
                except Exception:
                    pytest.fail(
                        "Should have raised AddonInstallationError specifically"
                    )

    def test_package_structure_and_imports(self):
        """Test that the package structure supports proper imports"""
        # Test main package imports
        import erp.addons

        assert hasattr(erp.addons, "AddonManager")
        assert hasattr(erp.addons, "AddonInstaller")
        assert hasattr(erp.addons, "HookType")
        assert hasattr(erp.addons, "AddonError")

        # Test subpackage imports
        import erp.addons.core
        import erp.addons.exceptions
        import erp.addons.hooks
        import erp.addons.installers
        import erp.addons.managers
        import erp.addons.utils

        # Each subpackage should have its __all__ defined
        assert hasattr(erp.addons.exceptions, "__all__")
        assert hasattr(erp.addons.hooks, "__all__")
        assert hasattr(erp.addons.installers, "__all__")
        assert hasattr(erp.addons.managers, "__all__")
        assert hasattr(erp.addons.utils, "__all__")
        assert hasattr(erp.addons.core, "__all__")
