{
    'name': 'Two-Factor Authentication (TOTP)',
    'version': '1.0.0',
    'category': 'Authentication',
    'summary': 'Time-based One-Time Password (TOTP) authentication',
    'description': """
Two-Factor Authentication using TOTP
====================================

This module adds Time-based One-Time Password (TOTP) support to the ERP system.
It extends the res.users model with TOTP functionality for enhanced security.

Features:
- TOTP secret generation and management
- QR code generation for authenticator apps
- TOTP verification during login
- User-friendly setup and management interface

Dependencies:
- pyotp: For TOTP generation and verification
- qrcode: For QR code generation
    """,
    'author': 'ERP System',
    'website': '',
    'depends': ['base'],
    'data': [
        'data/auth_totp_data.xml',
    ],
    'external_dependencies': {
        'python': ['pyotp', 'qrcode'],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
}
