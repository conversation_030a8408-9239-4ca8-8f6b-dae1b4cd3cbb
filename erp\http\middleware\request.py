"""
Request processing middleware
Handles request parsing, validation, and context setup
"""

from typing import Any, Dict, Optional

from ...logging import get_logger
from ..interfaces import RouteInfo
from .base import BaseMiddleware

logger = get_logger(__name__)


class RequestProcessingMiddleware(BaseMiddleware):
    """Middleware for processing incoming requests"""

    def __init__(self, priority: int = 70):
        super().__init__(priority)

    async def process_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Process incoming request"""
        try:
            # Set up request context
            await self._setup_request_context(request, route_info)

            # Parse request data
            await self._parse_request_data(request, route_info)

            # Validate request
            await self._validate_request(request, route_info)

            return request

        except Exception as e:
            logger.error(f"Error processing request for {route_info.path}: {e}")
            raise

    async def process_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Process response (cleanup if needed)"""
        return response

    async def _setup_request_context(self, request: Any, route_info: RouteInfo) -> None:
        """Set up request context"""
        try:
            # Store route info in request
            if hasattr(request, "state"):
                request.state.route_info = route_info
            else:
                setattr(request, "_route_info", route_info)

            # Set up database context if needed
            if route_info.scope.value in ("database", "addon"):
                await self._setup_database_context(request, route_info)

        except Exception as e:
            logger.debug(f"Error setting up request context: {e}")

    async def _parse_request_data(self, request: Any, route_info: RouteInfo) -> None:
        """Parse request data based on content type"""
        try:
            if not hasattr(request, "method"):
                return

            # Only parse data for methods that typically have body
            if request.method not in ("POST", "PUT", "PATCH"):
                return

            # Parse JSON data
            if hasattr(request, "headers"):
                content_type = request.headers.get("content-type", "")

                if "application/json" in content_type:
                    await self._parse_json_data(request)
                elif "application/x-www-form-urlencoded" in content_type:
                    await self._parse_form_data(request)
                elif "multipart/form-data" in content_type:
                    await self._parse_multipart_data(request)

        except Exception as e:
            logger.debug(f"Error parsing request data: {e}")

    async def _parse_json_data(self, request: Any) -> None:
        """Parse JSON request data"""
        try:
            if hasattr(request, "json"):
                json_data = await request.json()
                if hasattr(request, "state"):
                    request.state.json_data = json_data
                else:
                    setattr(request, "_json_data", json_data)
        except Exception as e:
            logger.debug(f"Error parsing JSON data: {e}")

    async def _parse_form_data(self, request: Any) -> None:
        """Parse form data"""
        try:
            if hasattr(request, "form"):
                form_data = await request.form()
                if hasattr(request, "state"):
                    request.state.form_data = form_data
                else:
                    setattr(request, "_form_data", form_data)
        except Exception as e:
            logger.debug(f"Error parsing form data: {e}")

    async def _parse_multipart_data(self, request: Any) -> None:
        """Parse multipart form data"""
        try:
            if hasattr(request, "form"):
                form_data = await request.form()
                if hasattr(request, "state"):
                    request.state.multipart_data = form_data
                else:
                    setattr(request, "_multipart_data", form_data)
        except Exception as e:
            logger.debug(f"Error parsing multipart data: {e}")

    async def _validate_request(self, request: Any, route_info: RouteInfo) -> None:
        """Validate request data"""
        try:
            # Basic validation
            if not hasattr(request, "method"):
                raise ValueError("Request missing method")

            # Check if method is allowed
            if request.method.upper() not in [m.upper() for m in route_info.methods]:
                raise ValueError(
                    f"Method {request.method} not allowed for {route_info.path}"
                )

            # Additional validation based on route metadata
            validation_rules = route_info.metadata.get("validation", {})
            if validation_rules:
                await self._apply_validation_rules(request, validation_rules)

        except Exception as e:
            logger.error(f"Request validation failed: {e}")
            raise

    async def _apply_validation_rules(
        self, request: Any, rules: Dict[str, Any]
    ) -> None:
        """Apply custom validation rules"""
        try:
            # Required parameters
            required_params = rules.get("required_params", [])
            if required_params:
                await self._validate_required_params(request, required_params)

            # Parameter types
            param_types = rules.get("param_types", {})
            if param_types:
                await self._validate_param_types(request, param_types)

        except Exception as e:
            logger.error(f"Error applying validation rules: {e}")
            raise

    async def _validate_required_params(
        self, request: Any, required_params: list
    ) -> None:
        """Validate required parameters"""
        # Get all parameters from request
        params = {}

        # Query parameters
        if hasattr(request, "query_params"):
            params.update(dict(request.query_params))

        # JSON data
        json_data = getattr(request, "_json_data", None) or (
            getattr(request.state, "json_data", None)
            if hasattr(request, "state")
            else None
        )
        if json_data and isinstance(json_data, dict):
            params.update(json_data)

        # Form data
        form_data = getattr(request, "_form_data", None) or (
            getattr(request.state, "form_data", None)
            if hasattr(request, "state")
            else None
        )
        if form_data:
            params.update(dict(form_data))

        # Check required parameters
        missing_params = [param for param in required_params if param not in params]
        if missing_params:
            raise ValueError(f"Missing required parameters: {missing_params}")

    async def _validate_param_types(
        self, request: Any, param_types: Dict[str, str]
    ) -> None:
        """Validate parameter types"""
        # This is a simplified implementation
        # In a real system, you'd want more sophisticated type validation
        pass

    async def _setup_database_context(
        self, request: Any, route_info: RouteInfo
    ) -> None:
        """Set up database context for database-scoped routes"""
        try:
            # Get database name from request or route metadata
            db_name = self._get_database_name(request, route_info)

            if db_name:
                # Store database name in request context
                if hasattr(request, "state"):
                    request.state.database = db_name
                else:
                    setattr(request, "_database", db_name)

        except Exception as e:
            logger.debug(f"Error setting up database context: {e}")

    def _get_database_name(self, request: Any, route_info: RouteInfo) -> Optional[str]:
        """Get database name from request or route metadata"""
        try:
            # Check route metadata first
            db_name = route_info.metadata.get("database")
            if db_name:
                return db_name

            # Check query parameters
            if hasattr(request, "query_params"):
                db_name = request.query_params.get("db")
                if db_name:
                    return db_name

            # Check cookies
            if hasattr(request, "cookies"):
                db_name = request.cookies.get("erp_database")
                if db_name:
                    return db_name

            return None

        except Exception as e:
            logger.debug(f"Error getting database name: {e}")
            return None
