"""
Test suite for registry integration scenarios

This module tests:
- Integration between different registry managers
- Cross-component registry operations
- End-to-end registry workflows
"""

import pytest

from erp.database.memory import <PERSON><PERSON><PERSON><PERSON><PERSON>, ModelMetadataManager, RouteManager


class TestRegistryIntegration:
    """Test registry integration scenarios"""

    @pytest.mark.asyncio
    async def test_registry_managers_coordination(self):
        """Test coordination between different registry managers"""
        addon_manager = AddonManager("test_db")
        metadata_manager = ModelMetadataManager("test_db")
        route_manager = RouteManager("test_db")

        # Set addon load order
        await addon_manager.set_addon_load_order(["base", "test_addon"])

        # Add model metadata
        await metadata_manager.set_model_data(
            "test.model",
            {
                "name": "Test Model",
                "fields": {"field1": {"name": "field1", "ttype": "char"}},
            },
        )

        # Register route
        async def test_handler():
            return "test"

        await route_manager.register_route("/test", test_handler)

        # Verify all managers have data
        addon_order = await addon_manager.get_addon_load_order()
        model_data = await metadata_manager.get_model_data("test.model")
        route_data = await route_manager.get_route("/test")

        assert addon_order == ["base", "test_addon"]
        assert model_data is not None
        assert route_data is not None

    @pytest.mark.asyncio
    async def test_full_addon_lifecycle_simulation(self):
        """Test simulating a full addon installation lifecycle"""
        addon_manager = AddonManager("test_db")
        metadata_manager = ModelMetadataManager("test_db")
        route_manager = RouteManager("test_db")

        # Step 1: Set initial addon order
        initial_addons = ["base"]
        await addon_manager.set_addon_load_order(initial_addons)

        # Step 2: Install new addon - update load order
        new_addons = ["base", "new_addon"]
        await addon_manager.set_addon_load_order(new_addons)

        # Step 3: Register addon models
        await metadata_manager.set_model_data(
            "new_addon.model",
            {
                "name": "New Addon Model",
                "info": "Model from new addon",
                "fields": {
                    "name": {"name": "name", "ttype": "char"},
                    "description": {"name": "description", "ttype": "text"},
                },
            },
        )

        # Step 4: Register addon routes
        async def addon_handler():
            return {"status": "success", "addon": "new_addon"}

        await route_manager.register_route("/new_addon/api", addon_handler)

        # Verify complete installation
        final_order = await addon_manager.get_addon_load_order()
        model_data = await metadata_manager.get_model_data("new_addon.model")
        route_data = await route_manager.get_route("/new_addon/api")

        assert final_order == new_addons
        assert model_data["name"] == "New Addon Model"
        assert route_data is not None
        assert route_data["handler"] == addon_handler

    @pytest.mark.asyncio
    async def test_addon_uninstall_cleanup(self):
        """Test cleanup during addon uninstallation"""
        addon_manager = AddonManager("test_db")
        metadata_manager = ModelMetadataManager("test_db")
        route_manager = RouteManager("test_db")

        # Setup: Install addon with data
        await addon_manager.set_addon_load_order(["base", "temp_addon"])
        await metadata_manager.set_model_data(
            "temp_addon.model",
            {
                "name": "Temp Model",
                "fields": {"field1": {"name": "field1", "ttype": "char"}},
            },
        )

        async def temp_handler():
            return "temp"

        await route_manager.register_route("/temp", temp_handler)

        # Simulate uninstall: Remove from load order
        await addon_manager.set_addon_load_order(["base"])

        # Note: In a real implementation, we would also clean up
        # model metadata and routes, but for this test we just
        # verify the addon order change
        final_order = await addon_manager.get_addon_load_order()
        assert "temp_addon" not in final_order
        assert final_order == ["base"]

    @pytest.mark.asyncio
    async def test_multiple_database_isolation(self):
        """Test that different database contexts are isolated"""
        # Create managers for different databases
        db1_addon = AddonManager("db1")
        db1_metadata = ModelMetadataManager("db1")

        db2_addon = AddonManager("db2")
        db2_metadata = ModelMetadataManager("db2")

        # Set different data for each database
        await db1_addon.set_addon_load_order(["base", "db1_addon"])
        await db1_metadata.set_model_data("db1.model", {"name": "DB1 Model"})

        await db2_addon.set_addon_load_order(["base", "db2_addon"])
        await db2_metadata.set_model_data("db2.model", {"name": "DB2 Model"})

        # Verify isolation
        db1_order = await db1_addon.get_addon_load_order()
        db2_order = await db2_addon.get_addon_load_order()

        db1_model = await db1_metadata.get_model_data("db1.model")
        db2_model = await db2_metadata.get_model_data("db2.model")

        assert db1_order == ["base", "db1_addon"]
        assert db2_order == ["base", "db2_addon"]
        assert db1_model["name"] == "DB1 Model"
        assert db2_model["name"] == "DB2 Model"

        # Cross-database queries should return None
        assert await db1_metadata.get_model_data("db2.model") is None
        assert await db2_metadata.get_model_data("db1.model") is None

    @pytest.mark.asyncio
    async def test_registry_stats_aggregation(self):
        """Test aggregating statistics across registry managers"""
        addon_manager = AddonManager("test_db")
        metadata_manager = ModelMetadataManager("test_db")
        route_manager = RouteManager("test_db")

        # Setup test data
        await addon_manager.set_addon_load_order(["base", "addon1", "addon2"])

        await metadata_manager.set_model_data(
            "model1", {"name": "Model 1", "fields": {"field1": {}, "field2": {}}}
        )
        await metadata_manager.set_model_data(
            "model2", {"name": "Model 2", "fields": {"field3": {}}}
        )

        async def handler1():
            return "1"

        async def handler2():
            return "2"

        await route_manager.register_route("/route1", handler1)
        await route_manager.register_route("/route2", handler2)

        # Get stats from each manager
        addon_order = await addon_manager.get_addon_load_order()
        metadata_stats = metadata_manager.get_metadata_stats()
        route_stats = route_manager.get_route_stats()

        # Verify aggregated stats
        assert len(addon_order) == 3
        assert metadata_stats["models_count"] == 2
        assert metadata_stats["total_fields_count"] == 3
        assert route_stats["routes_count"] == 2
