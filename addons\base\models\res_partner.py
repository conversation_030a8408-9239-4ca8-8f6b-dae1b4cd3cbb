"""
Partner model - Contacts and companies management

This model represents partners (contacts, customers, suppliers, companies)
in the ERP system with essential contact information and address management.
"""

from erp.models import Model
from erp import fields
from erp.api import depends


class ResPartner(Model):
    """Partners - contacts, customers, suppliers, companies"""

    _name = 'res.partner'
    _description = 'Contact'

    # Basic information
    name = fields.Char(
        string='Name', 
        required=True, 
        size=100, 
        index=True,
        help='Name of the contact or company'
    )

    is_company = fields.Boolean(
        string='Is a Company', 
        default=False,
        help='Check if the partner is a company, otherwise it is a person'
    )

    parent_id = fields.Many2One(
        'res.partner', 
        string='Related Company', 
        index=True,
        help='Parent company if this is a contact person'
    )

    # Contact information
    email = fields.Char(
        string='Email', 
        size=255, 
        index=True,
        help='Email address'
    )

    phone = fields.Char(
        string='Phone', 
        size=32,
        help='Phone number'
    )

    mobile = fields.Char(
        string='Mobile', 
        size=32,
        help='Mobile phone number'
    )

    website = fields.Char(
        string='Website', 
        size=255,
        help='Website URL'
    )

    # Address fields
    street = fields.Char(
        string='Street', 
        size=128,
        help='Street address'
    )

    street2 = fields.Char(
        string='Street2', 
        size=128,
        help='Additional address line'
    )

    city = fields.Char(
        string='City', 
        size=64,
        help='City name'
    )

    state_id = fields.Many2One(
        'res.country.state', 
        string='State', 
        index=True,
        help='State or province'
    )

    zip = fields.Char(
        string='Zip', 
        size=24,
        help='Postal code'
    )

    country_id = fields.Many2One(
        'res.country', 
        string='Country', 
        index=True,
        help='Country'
    )

    # Computed fields
    display_name = fields.Char(
        string='Display Name', 
        compute='_compute_display_name', 
        readonly=True,
        help='Display name for the partner'
    )

    formatted_address = fields.Text(
        string='Formatted Address',
        compute='_compute_formatted_address',
        readonly=True,
        help='Complete formatted address using country format'
    )

    # Status and categorization
    active = fields.Boolean(
        string='Active', 
        default=True, 
        index=True,
        help='Uncheck to archive the partner'
    )

    customer_rank = fields.Integer(
        string='Customer Rank', 
        default=0,
        help='Rank for customer prioritization'
    )

    supplier_rank = fields.Integer(
        string='Supplier Rank', 
        default=0,
        help='Rank for supplier prioritization'
    )

    # Additional information
    comment = fields.Text(
        string='Notes',
        help='Internal notes about this partner'
    )

    @depends('name', 'is_company', 'parent_id')
    def _compute_display_name(self):
        """Compute display name based on partner type and hierarchy"""
        for partner in self:
            if partner.is_company or not partner.parent_id:
                partner.display_name = partner.name
            else:
                # For contacts, show "Contact Name (Company Name)"
                partner.display_name = f"{partner.name} ({partner.parent_id.name})"

    @depends('street', 'street2', 'city', 'state_id', 'zip', 'country_id')
    def _compute_formatted_address(self):
        """Compute formatted address using country-specific format"""
        for partner in self:
            if partner.country_id and partner.country_id.address_format:
                # Use country's format_address method
                partner.formatted_address = partner.country_id.format_address(partner)
            else:
                # Fallback to simple format
                partner.formatted_address = partner._format_address_simple()

    def _format_address_simple(self):
        """Simple address formatting fallback"""
        address_parts = []
        
        if self.name:
            address_parts.append(self.name)
        if self.street:
            address_parts.append(self.street)
        if self.street2:
            address_parts.append(self.street2)
        
        city_line = []
        if self.city:
            city_line.append(self.city)
        if self.state_id and self.state_id.code:
            city_line.append(self.state_id.code)
        if self.zip:
            city_line.append(self.zip)
        
        if city_line:
            address_parts.append(' '.join(city_line))
        
        if self.country_id:
            address_parts.append(self.country_id.name)
        
        return '\n'.join(address_parts)

    async def name_get(self):
        """Return display name for partner"""
        result = []
        for partner in self:
            result.append((partner.id, partner.display_name or partner.name))
        return result

    def _get_address_fields(self):
        """Get list of address fields for this partner"""
        return ['street', 'street2', 'city', 'state_id', 'zip', 'country_id']

    async def copy_address_from(self, other_partner):
        """Copy address information from another partner"""
        if not other_partner:
            return
        
        address_fields = self._get_address_fields()
        values = {}
        
        for field in address_fields:
            if hasattr(other_partner, field):
                value = getattr(other_partner, field)
                # Handle Many2One fields
                if hasattr(value, 'id'):
                    values[field] = value.id
                else:
                    values[field] = value
        
        await self.write(values)
