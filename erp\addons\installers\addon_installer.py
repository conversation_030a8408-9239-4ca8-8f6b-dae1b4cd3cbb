"""
Addon Installer - Handles addon installation process

This module provides the AddonInstaller class for installing, uninstalling,
and upgrading addons. Separate from discovery/registration for clear separation of concerns.
"""

from typing import TYPE_CHECKING

from ...logging import get_logger
from ..hooks import HookType, get_hook_registry
from .installation_context import InstallationContext, InstallationUtilities

if TYPE_CHECKING:
    from ...environment import Environment


class AddonInstaller:
    """
    Handles addon installation process.
    This is separate from discovery/registration - it only deals with:
    1. Executing installation hooks
    2. Database operations during installation
    3. Installation state management

    Does NOT handle:
    - Addon discovery
    - Python module importing
    - Model/route registration
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.utilities = InstallationUtilities()

    async def _mark_addon_as_installed(self, env: "Environment", addon_name: str) -> None:
        """
        Mark an addon as installed in the ir.module.module table.

        Args:
            env: Environment for database operations
            addon_name: Name of the addon to mark as installed
        """
        from ..utils import update_addon_state_in_module_table

        success = await update_addon_state_in_module_table(
            env.cr, addon_name, "installed"
        )
        if not success:
            self.logger.warning(f"Failed to mark addon '{addon_name}' as installed in database")
        else:
            self.logger.debug(f"Marked addon '{addon_name}' as installed in database")

    async def _mark_addon_as_uninstalled(self, env: "Environment", addon_name: str) -> None:
        """
        Mark an addon as uninstalled in the ir.module.module table.

        Args:
            env: Environment for database operations
            addon_name: Name of the addon to mark as uninstalled
        """
        from ..utils import unregister_addon_from_module_table

        success = await unregister_addon_from_module_table(env.cr, addon_name)
        if not success:
            self.logger.warning(f"Failed to mark addon '{addon_name}' as uninstalled in database")
        else:
            self.logger.debug(f"Marked addon '{addon_name}' as uninstalled in database")

    async def install_addon(self, addon_name: str, env: "Environment") -> bool:
        """
        Install an addon by executing its installation hooks.
        The addon must already be registered (Python module imported) before calling this.

        Args:
            addon_name: Name of the addon to install
            env: Environment for database operations

        Returns:
            True if installation successful, False otherwise
        """
        context = InstallationContext(addon_name, env, "install")
        context.logger.info(f"🔧 Installing addon: {addon_name}")

        # Addon registry has been removed - no automatic registration verification

        # Get hook registry
        hook_registry = get_hook_registry()

        try:
            # Execute pre-install hooks with fail-fast behavior
            context.log_step("Executing pre-install hooks")
            await hook_registry.execute_hooks(HookType.PRE_INSTALL, addon_name, env, fail_fast=True)

            # Execute post-install hooks (includes addon-specific hooks) with fail-fast behavior
            context.log_step("Executing post-install hooks")
            await hook_registry.execute_hooks(HookType.POST_INSTALL, addon_name, env, fail_fast=True)

            # Core IR population is now handled by core_hooks.py post-install hook
            # This ensures IR population happens after schema sync completion
            # and is applied consistently across all addon installations

            # Mark addon as installed in the database
            context.log_step("Marking addon as installed in database")
            await self._mark_addon_as_installed(env, addon_name)

            # Update registry after successful installation
            # Note: Transaction management is handled by TransactionMiddleware for HTTP requests
            # For CLI operations, transactions are managed by the calling context
            await self.utilities.update_registry_after_action(env, addon_name, "install")

            context.log_success()
            return True

        except RuntimeError as e:
            # Hook execution failed - log error and return False
            context.log_error(e, f"Hook execution failed during installation of addon {addon_name}")
            return False
        except Exception as e:
            # Unexpected error during installation
            context.log_error(e, f"Unexpected error during installation of addon {addon_name}")
            return False

    async def uninstall_addon(self, addon_name: str, env: "Environment") -> bool:
        """
        Uninstall an addon by executing its uninstallation hooks.

        Args:
            addon_name: Name of the addon to uninstall
            env: Environment for database operations

        Returns:
            True if uninstallation successful, False otherwise
        """
        context = InstallationContext(addon_name, env, "uninstall")
        context.logger.info(f"🗑️ Uninstalling addon: {addon_name}")

        # Get hook registry
        hook_registry = get_hook_registry()

        try:
            # Execute pre-uninstall hooks with fail-fast behavior
            context.log_step("Executing pre-uninstall hooks")
            await hook_registry.execute_hooks(HookType.PRE_UNINSTALL, addon_name, env, fail_fast=True)

            # Execute post-uninstall hooks with fail-fast behavior
            context.log_step("Executing post-uninstall hooks")
            await hook_registry.execute_hooks(HookType.POST_UNINSTALL, addon_name, env, fail_fast=True)

            # Mark addon as uninstalled in the database
            context.log_step("Marking addon as uninstalled in database")
            await self._mark_addon_as_uninstalled(env, addon_name)

            # Update registry after successful uninstallation
            await self.utilities.update_registry_after_action(env, addon_name, "uninstall")

            context.log_success()
            return True

        except RuntimeError as e:
            # Hook execution failed - log error and return False
            context.log_error(e, f"Hook execution failed during uninstallation of addon {addon_name}")
            return False
        except Exception as e:
            # Unexpected error during uninstallation
            context.log_error(e, f"Unexpected error during uninstallation of addon {addon_name}")
            return False

    async def upgrade_addon(self, addon_name: str, env: "Environment") -> bool:
        """
        Upgrade an addon by executing its upgrade hooks.

        Args:
            addon_name: Name of the addon to upgrade
            env: Environment for database operations

        Returns:
            True if upgrade successful, False otherwise
        """
        context = InstallationContext(addon_name, env, "upgrade")
        context.logger.info(f"⬆️ Upgrading addon: {addon_name}")

        # Get hook registry
        hook_registry = get_hook_registry()

        try:
            # Execute pre-upgrade hooks with fail-fast behavior
            context.log_step("Executing pre-upgrade hooks")
            await hook_registry.execute_hooks(HookType.PRE_UPGRADE, addon_name, env, fail_fast=True)

            # Execute post-upgrade hooks with fail-fast behavior
            context.log_step("Executing post-upgrade hooks")
            await hook_registry.execute_hooks(HookType.POST_UPGRADE, addon_name, env, fail_fast=True)

            # Update registry after successful upgrade
            await self.utilities.update_registry_after_action(env, addon_name, "upgrade")

            context.log_success()
            return True

        except RuntimeError as e:
            # Hook execution failed - log error and return False
            context.log_error(e, f"Hook execution failed during upgrade of addon {addon_name}")
            return False
        except Exception as e:
            # Unexpected error during upgrade
            context.log_error(e, f"Unexpected error during upgrade of addon {addon_name}")
            return False

    async def register_and_install_addon(
        self, addon_name: str, env: "Environment"
    ) -> bool:
        """
        Convenience method to register and install an addon in one call.
        This is useful for CLI operations where you want to do both steps.

        Args:
            addon_name: Name of the addon to register and install
            env: Environment for database operations

        Returns:
            True if both registration and installation successful, False otherwise
        """
        context = InstallationContext(addon_name, env, "register_and_install")
        context.logger.info(f"🔧 Registering and installing addon: {addon_name}")

        # Addon registry has been removed - install directly
        return await self.install_addon(addon_name, env)
