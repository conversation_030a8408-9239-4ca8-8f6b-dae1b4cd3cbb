"""
Tests for the lint CLI commands
"""

import argparse
import sys
from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from erp.cli.lint import LintCommand, LintCommandGroup


class TestLintCommand:
    """Test cases for LintCommand"""

    def test_lint_command_creation(self):
        """Test that LintCommand can be created"""
        cmd = LintCommand()
        assert cmd is not None
        assert cmd.name == "lint"

    def test_add_arguments(self):
        """Test that arguments are added correctly"""
        cmd = LintCommand()
        parser = argparse.ArgumentParser()

        cmd.add_arguments(parser)

        # Test parsing with default values
        args = parser.parse_args([])
        assert args.fix is False
        assert args.tool == "all"
        assert args.paths == ["erp/", "tests/", "examples/", "scripts/"]
        assert args.check_only is False

    def test_add_arguments_with_values(self):
        """Test parsing arguments with specific values"""
        cmd = LintCommand()
        parser = argparse.ArgumentParser()
        cmd.add_arguments(parser)

        args = parser.parse_args(
            ["--fix", "--tool", "flake8", "--paths", "erp/cli/", "--check-only"]
        )

        assert args.fix is True
        assert args.tool == "flake8"
        assert args.paths == ["erp/cli/"]
        assert args.check_only is True

    @patch("subprocess.run")
    def test_run_command_success(self, mock_run):
        """Test _run_command with successful execution"""
        cmd = LintCommand()

        # Mock successful subprocess run
        mock_result = Mock()
        mock_result.returncode = 0
        mock_result.stdout = "All good"
        mock_result.stderr = ""
        mock_run.return_value = mock_result

        exit_code, stdout, stderr = cmd._run_command(["echo", "test"], "test")

        assert exit_code == 0
        assert stdout == "All good"
        assert stderr == ""

    @patch("subprocess.run")
    def test_run_command_failure(self, mock_run):
        """Test _run_command with failed execution"""
        cmd = LintCommand()

        # Mock failed subprocess run
        mock_result = Mock()
        mock_result.returncode = 1
        mock_result.stdout = ""
        mock_result.stderr = "Error occurred"
        mock_run.return_value = mock_result

        exit_code, stdout, stderr = cmd._run_command(["false"], "test")

        assert exit_code == 1
        assert stdout == ""
        assert stderr == "Error occurred"

    @patch("subprocess.run")
    def test_run_command_timeout(self, mock_run):
        """Test _run_command with timeout"""
        cmd = LintCommand()

        # Mock timeout
        from subprocess import TimeoutExpired

        mock_run.side_effect = TimeoutExpired("cmd", 300)

        exit_code, stdout, stderr = cmd._run_command(["sleep", "1000"], "test")

        assert exit_code == 1
        assert stdout == ""
        assert stderr == "Timeout"

    @patch("subprocess.run")
    def test_run_command_file_not_found(self, mock_run):
        """Test _run_command with file not found"""
        cmd = LintCommand()

        # Mock file not found
        mock_run.side_effect = FileNotFoundError()

        exit_code, stdout, stderr = cmd._run_command(["nonexistent"], "test")

        assert exit_code == 1
        assert stdout == ""
        assert stderr == "Tool not found"

    def test_handle_no_valid_paths(self):
        """Test handle method with no valid paths"""
        cmd = LintCommand()

        # Create args with non-existent paths
        args = argparse.Namespace(
            fix=False, check_only=False, tool="all", paths=["nonexistent/path/"]
        )

        result = cmd.handle(args)
        assert result == 1  # Should fail with no valid paths

    @patch.object(LintCommand, "_run_flake8")
    @patch("pathlib.Path.exists")
    def test_handle_flake8_only(self, mock_exists, mock_flake8):
        """Test handle method with flake8 tool only"""
        cmd = LintCommand()

        # Mock path exists
        mock_exists.return_value = True

        # Mock flake8 success
        mock_flake8.return_value = 0

        args = argparse.Namespace(
            fix=False, check_only=False, tool="flake8", paths=["erp/cli/lint.py"]
        )

        result = cmd.handle(args)
        assert result == 0
        mock_flake8.assert_called_once()

    @patch.object(LintCommand, "_run_isort")
    @patch.object(LintCommand, "_run_black")
    @patch.object(LintCommand, "_run_flake8")
    @patch.object(LintCommand, "_run_mypy")
    @patch("pathlib.Path.exists")
    def test_handle_all_tools_success(
        self, mock_exists, mock_mypy, mock_flake8, mock_black, mock_isort
    ):
        """Test handle method with all tools succeeding"""
        cmd = LintCommand()

        # Mock path exists
        mock_exists.return_value = True

        # Mock all tools success
        mock_isort.return_value = 0
        mock_black.return_value = 0
        mock_flake8.return_value = 0
        mock_mypy.return_value = 0

        args = argparse.Namespace(
            fix=False, check_only=False, tool="all", paths=["erp/cli/lint.py"]
        )

        result = cmd.handle(args)
        assert result == 0

        # Verify all tools were called
        mock_isort.assert_called_once()
        mock_black.assert_called_once()
        mock_flake8.assert_called_once()
        mock_mypy.assert_called_once()


class TestLintCommandGroup:
    """Test cases for LintCommandGroup"""

    def test_lint_command_group_creation(self):
        """Test that LintCommandGroup can be created"""
        group = LintCommandGroup()
        assert group is not None
        assert "lint" in group.commands
        assert isinstance(group.commands["lint"], LintCommand)

    def test_add_commands(self):
        """Test that commands are added to subparsers"""
        group = LintCommandGroup()

        # Create mock subparsers
        mock_subparsers = Mock()
        mock_parser = Mock()
        mock_subparsers.add_parser.return_value = mock_parser

        # Create mock parent parser
        mock_parent = Mock()

        group.add_commands(mock_subparsers, mock_parent)

        # Verify add_parser was called with correct arguments
        mock_subparsers.add_parser.assert_called_once_with(
            "lint", help="Run code linting and formatting checks", parents=[mock_parent]
        )

        # Verify add_arguments was called on the parser
        mock_parser.add_arguments = Mock()
        group.commands["lint"].add_arguments = Mock()


if __name__ == "__main__":
    pytest.main([__file__])
