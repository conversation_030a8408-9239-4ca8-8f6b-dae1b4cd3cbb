"""
Dependency Analyzer for XML Data Loading

This module provides dependency analysis functionality to detect circular
dependencies and provide detailed error messages for XML data loading issues.
"""

import re
from collections import defaultdict, deque
from typing import Any, Dict, List, Optional, Set

from ..logging import get_logger


class DependencyNode:
    """Represents a node in the dependency graph"""

    def __init__(
        self, xml_id: str, model: str, file_path: str = None, line_number: int = None
    ):
        self.xml_id = xml_id
        self.model = model
        self.file_path = file_path
        self.line_number = line_number
        self.dependencies: Set[str] = set()
        self.dependents: Set[str] = set()

    def __repr__(self):
        return f"DependencyNode({self.xml_id}, {self.model})"


class DependencyAnalyzer:
    """
    Analyzes dependencies between XML records to detect circular dependencies
    and provide detailed error messages for resolution issues.
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.nodes: Dict[str, DependencyNode] = {}
        self.dependency_graph: Dict[str, Set[str]] = defaultdict(set)
        self.reverse_graph: Dict[str, Set[str]] = defaultdict(set)

    def add_record(
        self,
        xml_id: str,
        model: str,
        values: Dict[str, Any],
        file_path: str = None,
        line_number: int = None,
    ):
        """Add a record to the dependency graph"""
        if xml_id not in self.nodes:
            self.nodes[xml_id] = DependencyNode(xml_id, model, file_path, line_number)

        # Extract dependencies from field values
        dependencies = self._extract_dependencies(values)

        for dep_xml_id in dependencies:
            self._add_dependency(xml_id, dep_xml_id)

    def _extract_dependencies(self, values: Dict[str, Any]) -> Set[str]:
        """Extract XML ID dependencies from field values"""
        dependencies = set()

        for field_name, field_def in values.items():
            if isinstance(field_def, dict):
                field_type = field_def.get("type")
                field_value = field_def.get("value")

                if field_type == "ref" and field_value:
                    dependencies.add(field_value)
                elif field_type == "eval" and field_value:
                    # Extract ref() calls from eval expressions
                    ref_matches = re.findall(r"ref\(['\"]([^'\"]+)['\"]\)", field_value)
                    dependencies.update(ref_matches)

        return dependencies

    def _add_dependency(self, from_xml_id: str, to_xml_id: str):
        """Add a dependency relationship"""
        self.dependency_graph[from_xml_id].add(to_xml_id)
        self.reverse_graph[to_xml_id].add(from_xml_id)

        if from_xml_id in self.nodes:
            self.nodes[from_xml_id].dependencies.add(to_xml_id)
        if to_xml_id in self.nodes:
            self.nodes[to_xml_id].dependents.add(from_xml_id)

    def detect_circular_dependencies(self) -> List[List[str]]:
        """Detect circular dependencies using DFS"""
        visited = set()
        rec_stack = set()
        cycles = []

        def dfs(node: str, path: List[str]) -> bool:
            if node in rec_stack:
                # Found a cycle
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                cycles.append(cycle)
                return True

            if node in visited:
                return False

            visited.add(node)
            rec_stack.add(node)
            path.append(node)

            for neighbor in self.dependency_graph.get(node, []):
                if dfs(neighbor, path):
                    return True

            rec_stack.remove(node)
            path.pop()
            return False

        for node in self.nodes:
            if node not in visited:
                dfs(node, [])

        return cycles

    def get_dependency_chain(
        self, xml_id: str, target_xml_id: str
    ) -> Optional[List[str]]:
        """Find dependency chain from xml_id to target_xml_id using BFS"""
        if xml_id == target_xml_id:
            return [xml_id]

        queue = deque([(xml_id, [xml_id])])
        visited = {xml_id}

        while queue:
            current, path = queue.popleft()

            for neighbor in self.dependency_graph.get(current, []):
                if neighbor == target_xml_id:
                    return path + [neighbor]

                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append((neighbor, path + [neighbor]))

        return None

    def analyze_missing_dependency(
        self, xml_id: str, missing_dep: str
    ) -> Dict[str, Any]:
        """Analyze a missing dependency and provide detailed information"""
        analysis = {
            "xml_id": xml_id,
            "missing_dependency": missing_dep,
            "possible_causes": [],
            "suggestions": [],
            "dependency_info": {},
        }

        # Check if the missing dependency exists in our graph
        if missing_dep in self.nodes:
            node = self.nodes[missing_dep]
            analysis["dependency_info"] = {
                "model": node.model,
                "file_path": node.file_path,
                "line_number": node.line_number,
                "exists_in_graph": True,
            }

            # Check for circular dependency
            cycle_path = self.get_dependency_chain(missing_dep, xml_id)
            if cycle_path:
                analysis["possible_causes"].append("Circular dependency detected")
                analysis["suggestions"].append(
                    f"Break the circular dependency: {' -> '.join(cycle_path)}"
                )
            else:
                analysis["possible_causes"].append("Dependency ordering issue")
                analysis["suggestions"].append(
                    f"Move the definition of '{missing_dep}' before '{xml_id}' in the XML file"
                )
        else:
            analysis["dependency_info"]["exists_in_graph"] = False
            analysis["possible_causes"].extend(
                [
                    "XML ID is misspelled",
                    "Referenced record is in a different addon that hasn't been loaded",
                    "Referenced record doesn't exist",
                ]
            )
            analysis["suggestions"].extend(
                [
                    f"Check spelling of XML ID: {missing_dep}",
                    "Ensure the addon containing this record is loaded first",
                    "Verify the record exists in the expected location",
                ]
            )

        return analysis

    def get_loading_order(self) -> List[str]:
        """Get optimal loading order using topological sort"""
        in_degree = defaultdict(int)

        # Calculate in-degrees
        for node in self.nodes:
            in_degree[node] = 0

        for node in self.dependency_graph:
            for neighbor in self.dependency_graph[node]:
                in_degree[neighbor] += 1

        # Topological sort using Kahn's algorithm
        queue = deque([node for node in self.nodes if in_degree[node] == 0])
        result = []

        while queue:
            node = queue.popleft()
            result.append(node)

            for neighbor in self.dependency_graph.get(node, []):
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)

        return result

    def generate_dependency_report(self) -> Dict[str, Any]:
        """Generate a comprehensive dependency analysis report"""
        cycles = self.detect_circular_dependencies()
        loading_order = self.get_loading_order()

        report = {
            "total_records": len(self.nodes),
            "total_dependencies": sum(
                len(deps) for deps in self.dependency_graph.values()
            ),
            "circular_dependencies": cycles,
            "has_circular_dependencies": len(cycles) > 0,
            "suggested_loading_order": loading_order,
            "dependency_statistics": self._get_dependency_statistics(),
        }

        return report

    def _get_dependency_statistics(self) -> Dict[str, Any]:
        """Get statistics about dependencies"""
        dep_counts = [len(deps) for deps in self.dependency_graph.values()]
        dependent_counts = [len(deps) for deps in self.reverse_graph.values()]

        return {
            "max_dependencies": max(dep_counts) if dep_counts else 0,
            "avg_dependencies": sum(dep_counts) / len(dep_counts) if dep_counts else 0,
            "max_dependents": max(dependent_counts) if dependent_counts else 0,
            "avg_dependents": (
                sum(dependent_counts) / len(dependent_counts) if dependent_counts else 0
            ),
            "isolated_records": len(
                [
                    node
                    for node in self.nodes
                    if not self.dependency_graph.get(node)
                    and not self.reverse_graph.get(node)
                ]
            ),
        }
