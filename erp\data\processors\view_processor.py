"""
View Processor for ERP system

Specialized processor for handling UI views with proper inheritance,
field validation, and view architecture processing.
"""

import xml.etree.ElementTree as ET
from typing import Any, Dict, List, Optional

from ..sql_helpers import ModelSQLHelpers, SQLHelpers
from ..xmlid_manager import XMLIDManager
from .base import BaseDataProcessor, ProcessorError


class ViewProcessor(BaseDataProcessor):
    """
    Processor for UI views (ir.ui.view)

    Handles creation and updating of views with proper architecture validation,
    inheritance processing, and field references.
    """

    def __init__(self, db_manager, name: str = "ViewProcessor"):
        super().__init__(db_manager, name)

        # Initialize SQL helpers
        self.sql = SQLHelpers(db_manager)
        self.model_sql = ModelSQLHelpers(self.sql)
        self.xmlid_manager = XMLIDManager(db_manager)

        # Supported view types
        self.supported_view_types = {
            "form",
            "tree",
            "kanban",
            "calendar",
            "pivot",
            "graph",
            "search",
            "qweb",
            "activity",
            "map",
        }

        # View inheritance modes
        self.inheritance_modes = {
            "primary",
            "extension",
            "before",
            "after",
            "replace",
            "inside",
        }

    def can_process(self, item: Dict[str, Any]) -> bool:
        """Check if this processor can handle the given item"""
        if not isinstance(item, dict):
            return False

        model = item.get("model")
        return model == "ir.ui.view"

    def get_supported_models(self) -> List[str]:
        """Get list of models this processor supports"""
        return ["ir.ui.view"]

    def get_processing_order(self) -> int:
        """Get processing order (process views early)"""
        return 20

    async def _process_item(self, item: Dict[str, Any], **kwargs) -> bool:
        """Process a single view item"""
        xml_id = item.get("xml_id")
        values = item.get("values", {})
        noupdate = item.get("noupdate", False)

        try:
            # Validate view data
            validation_result = await self._validate_view_data(values)
            if not validation_result["valid"]:
                for error in validation_result["errors"]:
                    self.result.add_error(
                        f"View validation error for {xml_id}: {error}"
                    )
                return False

            # Process view-specific fields
            processed_values = await self._process_view_values(values)
            if processed_values is None:
                return False

            # Check if view exists
            existing_record_id = None
            if xml_id:
                existing_record_id = await self._find_record_by_xmlid(xml_id)

            if existing_record_id:
                # Update existing view
                if not noupdate:
                    success = await self._update_view(
                        existing_record_id, processed_values
                    )
                    if success:
                        self.logger.debug(f"Updated view {xml_id}")
                        return True
                else:
                    self.logger.debug(f"Skipped updating view {xml_id} (noupdate=True)")
                    return True
            else:
                # Create new view
                new_record_id = await self._create_view(processed_values)
                if new_record_id:
                    # Store XML ID mapping
                    if xml_id:
                        await self._store_xmlid_mapping(xml_id, new_record_id)

                    self.logger.debug(f"Created view {xml_id or 'no-id'}")
                    return True

            return False

        except Exception as e:
            error_msg = f"Failed to process view {xml_id or 'no-id'}: {e}"
            self.result.add_error(error_msg)
            return False

    async def _validate_view_data(self, values: Dict[str, Any]) -> Dict[str, Any]:
        """Validate view data structure"""
        result = {"valid": True, "errors": [], "warnings": []}

        # Check required fields
        required_fields = ["name", "model", "arch"]
        for field in required_fields:
            if field not in values:
                result["errors"].append(f"Missing required field: {field}")
                result["valid"] = False

        # Validate view type
        if "type" in values:
            view_type_def = values["type"]
            view_type = (
                view_type_def.get("value")
                if isinstance(view_type_def, dict)
                else view_type_def
            )
            if view_type and view_type not in self.supported_view_types:
                result["warnings"].append(f"Unknown view type: {view_type}")

        # Validate architecture if present
        if "arch" in values:
            arch_validation = await self._validate_view_architecture(values["arch"])
            result["errors"].extend(arch_validation["errors"])
            result["warnings"].extend(arch_validation["warnings"])
            if arch_validation["errors"]:
                result["valid"] = False

        return result

    async def _validate_view_architecture(self, arch_def: Any) -> Dict[str, Any]:
        """Validate view architecture XML"""
        result = {"errors": [], "warnings": []}

        try:
            # Get architecture content
            if isinstance(arch_def, dict):
                arch_content = arch_def.get("value", "")
            else:
                arch_content = str(arch_def)

            if not arch_content:
                result["errors"].append("Empty view architecture")
                return result

            # Parse XML architecture
            try:
                root = ET.fromstring(f"<root>{arch_content}</root>")

                # Validate structure
                for element in root.iter():
                    # Check for common issues
                    if element.tag in ["field", "button", "label"]:
                        if not element.get("name") and element.tag != "label":
                            result["warnings"].append(
                                f"Element {element.tag} missing 'name' attribute"
                            )

                    # Check for invalid attributes
                    if element.tag == "field":
                        attrs = element.attrib
                        if "invisible" in attrs and "readonly" in attrs:
                            result["warnings"].append(
                                "Field has both 'invisible' and 'readonly' attributes"
                            )

            except ET.ParseError as e:
                result["errors"].append(f"Invalid XML architecture: {e}")

        except Exception as e:
            result["errors"].append(f"Error validating architecture: {e}")

        return result

    async def _process_view_values(
        self, values: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Process view-specific field values"""
        processed = {}

        for field_name, field_def in values.items():
            try:
                if field_name == "arch":
                    # Special handling for architecture field
                    processed[field_name] = await self._process_architecture_field(
                        field_def
                    )
                elif field_name == "inherit_id":
                    # Special handling for inheritance
                    processed[field_name] = await self._process_inherit_field(field_def)
                else:
                    # Standard field processing
                    processed[field_name] = await self._process_standard_field(
                        field_def
                    )

            except Exception as e:
                error_msg = f"Error processing view field {field_name}: {e}"
                self.result.add_error(error_msg)
                return None

        return processed

    async def _process_architecture_field(self, arch_def: Any) -> str:
        """Process view architecture field"""
        if isinstance(arch_def, dict):
            arch_content = arch_def.get("value", "")
        else:
            arch_content = str(arch_def)

        # Clean up architecture content
        arch_content = arch_content.strip()

        # Validate and potentially transform architecture
        try:
            # Parse to validate XML
            ET.fromstring(f"<root>{arch_content}</root>")
        except ET.ParseError as e:
            raise ProcessorError(f"Invalid view architecture XML: {e}")

        return arch_content

    async def _process_inherit_field(self, inherit_def: Any) -> Optional[str]:
        """Process view inheritance field"""
        if isinstance(inherit_def, dict):
            if inherit_def.get("type") == "ref":
                # Reference to parent view
                ref_value = inherit_def.get("value")
                return await self._resolve_view_reference(ref_value)
            else:
                return inherit_def.get("value")
        else:
            return str(inherit_def) if inherit_def else None

    async def _process_standard_field(self, field_def: Any) -> Any:
        """Process standard field values"""
        if isinstance(field_def, dict):
            field_type = field_def.get("type")
            field_value = field_def.get("value")

            if field_type == "ref":
                return await self._resolve_reference(field_value)
            elif field_type == "eval":
                return self._evaluate_expression(field_value)
            else:
                return field_value
        else:
            return field_def

    async def _resolve_view_reference(self, ref_value: str) -> Optional[str]:
        """Resolve reference to another view"""
        try:
            return await self.xmlid_manager.resolve_xmlid_to_record_id(ref_value)
        except Exception as e:
            self.result.add_warning(
                f"Failed to resolve view reference {ref_value}: {e}"
            )
            return None

    async def _resolve_reference(self, ref_value: str) -> Optional[str]:
        """Resolve a general reference"""
        try:
            return await self.xmlid_manager.resolve_xmlid_to_record_id(
                ref_value,
                context=f"view reference in {self.context.get('current_model', 'unknown model')}",
            )
        except Exception as e:
            self.result.add_error(f"Failed to resolve reference {ref_value}: {e}")
            return None

    def _evaluate_expression(self, expression: str) -> Any:
        """Safely evaluate a Python expression"""
        try:
            if expression == "True":
                return True
            elif expression == "False":
                return False
            elif expression == "None":
                return None
            elif expression.startswith("'") and expression.endswith("'"):
                return expression[1:-1]
            elif expression.startswith('"') and expression.endswith('"'):
                return expression[1:-1]
            else:
                try:
                    return int(expression)
                except ValueError:
                    try:
                        return float(expression)
                    except ValueError:
                        return expression
        except Exception:
            return expression

    async def _find_record_by_xmlid(self, xml_id: str) -> Optional[str]:
        """Find a record ID by its XML ID"""
        try:
            return await self.xmlid_manager.resolve_xmlid_to_record_id(xml_id)
        except Exception:
            return None

    async def _create_view(self, values: Dict[str, Any]) -> Optional[str]:
        """Create a new view record"""
        try:
            return await self.model_sql.create_record("ir.ui.view", values)
        except Exception as e:
            self.result.add_error(f"Failed to create view: {e}")
            return None

    async def _update_view(self, record_id: str, values: Dict[str, Any]) -> bool:
        """Update an existing view record"""
        try:
            return await self.model_sql.update_record("ir.ui.view", record_id, values)
        except Exception as e:
            self.result.add_error(f"Failed to update view {record_id}: {e}")
            return False

    async def _store_xmlid_mapping(self, xml_id: str, record_id: str):
        """Store XML ID to record ID mapping"""
        try:
            if "." in xml_id:
                module, name = xml_id.split(".", 1)
            else:
                module = self.context.get("addon_name", "base")
                name = xml_id

            await self.xmlid_manager.create_xmlid_mapping(
                module=module, name=name, model="ir.ui.view", res_id=record_id
            )
        except Exception as e:
            self.result.add_warning(f"Failed to store XML ID mapping for {xml_id}: {e}")
