"""
Tests for the refactored modular CLI system
"""

import sys
from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON>, Mo<PERSON>, patch

import pytest

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from erp.cli.database import DatabaseCommandGroup, InitCommand, P<PERSON><PERSON><PERSON>mand
from erp.cli.manager import ERPCLIManager
from erp.cli.server import ServerCommandGroup, StartCommand
from erp.cli.utils import setup_python_path, validate_database_name


class TestDatabaseCommands:
    """Test database command functionality"""

    def setup_method(self):
        self.command_group = DatabaseCommandGroup()

    def test_command_registration(self):
        """Test that commands are properly registered"""
        assert "init" in self.command_group.commands
        assert "purge" in self.command_group.commands
        assert isinstance(self.command_group.commands["init"], InitCommand)
        assert isinstance(self.command_group.commands["purge"], PurgeCommand)

    @patch("erp.cli.database.asyncio.run")
    @patch("erp.cli.database.DatabaseRegistry")
    def test_init_command_basic(self, mock_registry, mock_asyncio_run):
        """Test basic init command functionality"""
        mock_asyncio_run.return_value = 0

        init_command = self.command_group.commands["init"]

        # Mock arguments
        args = Mock()
        args.db_name = "test_db"
        args.force = False
        args.demo = False
        args.no_create = False
        args.exit = True
        args.no_http = False
        args.verbose = False

        result = init_command.handle(args)
        assert mock_asyncio_run.called

    @patch("erp.cli.database.asyncio.run")
    def test_purge_command_basic(self, mock_asyncio_run):
        """Test basic purge command functionality"""
        mock_asyncio_run.return_value = 0

        purge_command = self.command_group.commands["purge"]

        # Mock arguments
        args = Mock()
        args.force = True
        args.verbose = False

        result = purge_command.handle(args)
        assert mock_asyncio_run.called


class TestServerCommands:
    """Test server command functionality"""

    def setup_method(self):
        self.command_group = ServerCommandGroup()

    def test_command_registration(self):
        """Test that commands are properly registered"""
        assert "start" in self.command_group.commands
        assert isinstance(self.command_group.commands["start"], StartCommand)

    @patch("erp.cli.server.asyncio.run")
    def test_start_command_basic(self, mock_asyncio_run):
        """Test basic start command functionality"""
        mock_asyncio_run.return_value = True

        start_command = self.command_group.commands["start"]

        # Mock arguments
        args = Mock()
        args.db_name = None
        args.host = None
        args.port = None
        args.reload = False
        args.verbose = False

        result = start_command.handle(args)
        assert mock_asyncio_run.called
        assert result == 0


class TestERPCLIManager:
    """Test the main CLI manager"""

    def setup_method(self):
        self.manager = ERPCLIManager()

    def test_command_groups_registered(self):
        """Test that all command groups are registered"""
        assert "database" in self.manager.command_groups
        assert "server" in self.manager.command_groups
        assert isinstance(self.manager.command_groups["database"], DatabaseCommandGroup)
        assert isinstance(self.manager.command_groups["server"], ServerCommandGroup)

    def test_command_mapping(self):
        """Test command to group mapping"""
        # Test that commands map to correct groups
        args = Mock()
        args.command = "init"

        # This would normally call the actual command, but we're just testing the mapping
        with patch.object(
            self.manager.command_groups["database"], "handle_command"
        ) as mock_handle:
            mock_handle.return_value = 0
            result = self.manager._handle_command(args)
            mock_handle.assert_called_once_with("init", args)

    @patch("erp.cli.manager.setup_python_path")
    @patch("erp.cli.manager.setup_logging")
    @patch("erp.cli.manager.print_system_info")
    def test_run_with_args(self, mock_print_info, mock_setup_logging, mock_setup_path):
        """Test running CLI with arguments"""
        mock_setup_logging.return_value = Mock()

        with patch.object(self.manager, "_handle_command") as mock_handle:
            mock_handle.return_value = 0
            result = self.manager.run(["init", "test_db", "--exit"])
            assert result == 0


class TestUtilities:
    """Test utility functions"""

    def test_setup_python_path(self):
        """Test Python path setup"""
        original_path = sys.path.copy()
        setup_python_path()
        # Path should be modified (exact test depends on current directory structure)
        assert len(sys.path) >= len(original_path)

    def test_validate_database_name(self):
        """Test database name validation"""
        # Valid names
        assert validate_database_name("mydb") == True
        assert validate_database_name("test_db") == True
        assert validate_database_name("db123") == True

        # Invalid names
        assert validate_database_name("") == False
        assert validate_database_name("123db") == False  # Can't start with number
        assert validate_database_name("my-db") == False  # No hyphens
        assert validate_database_name("my.db") == False  # No dots


if __name__ == "__main__":
    pytest.main([__file__])
