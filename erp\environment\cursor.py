"""
Database cursor wrapper for ERP environment
"""

from contextlib import asynccontextmanager
from typing import TYPE_CHECKING, Any, Optional

if TYPE_CHECKING:
    from ..database.connection import DatabaseManager


class DatabaseCursor:
    """
    Database cursor wrapper that provides Odoo-like interface
    """

    def __init__(self, db_manager: "DatabaseManager", db_name: str):
        self._db_manager = db_manager
        self.db_name = db_name
        self._connection = None
        self._in_transaction = False
        self._transaction_connection = None  # Dedicated connection for transactions

    @asynccontextmanager
    async def _get_connection(self):
        """Get database connection from pool for transaction context"""
        if self._transaction_connection is not None:
            # If we're in a transaction context, use the existing connection
            yield self._transaction_connection
            return

        # Otherwise, get a temporary connection from pool
        async with self._db_manager.acquire_connection() as conn:
            self._connection = conn
            try:
                yield conn
            finally:
                self._connection = None

    @asynccontextmanager
    async def transaction_context(self):
        """Context manager for maintaining connection throughout transaction"""
        if self._transaction_connection is not None:
            # Already in transaction context, just yield
            yield
            return

        # Acquire connection for transaction context
        async with self._db_manager.acquire_connection() as conn:
            self._transaction_connection = conn
            self._connection = conn

            # Share the connection with the database manager
            self._db_manager.set_shared_connection(conn)

            try:
                yield
            finally:
                # Clear the shared connection
                self._db_manager.clear_shared_connection()
                self._transaction_connection = None
                self._connection = None
                self._in_transaction = False

    async def execute(self, query: str, *args) -> str:
        """Execute a query"""
        if self._connection:
            return await self._connection.execute(query, *args)
        return await self._db_manager.execute(query, *args)

    async def fetch(self, query: str, *args) -> list:
        """Fetch multiple rows"""
        if self._connection:
            return await self._connection.fetch(query, *args)
        return await self._db_manager.fetch(query, *args)

    async def fetchrow(self, query: str, *args) -> Optional[dict]:
        """Fetch single row"""
        if self._connection:
            result = await self._connection.fetchrow(query, *args)
        else:
            result = await self._db_manager.fetchrow(query, *args)

        return dict(result) if result else None

    async def fetchval(self, query: str, *args) -> Any:
        """Fetch single value"""
        if self._connection:
            return await self._connection.fetchval(query, *args)
        return await self._db_manager.fetchval(query, *args)

    async def commit(self):
        """Commit transaction"""
        if self._transaction_connection and self._in_transaction:
            await self._transaction_connection.execute("COMMIT")
            self._in_transaction = False

    async def rollback(self):
        """Rollback transaction"""
        if self._transaction_connection and self._in_transaction:
            await self._transaction_connection.execute("ROLLBACK")
            self._in_transaction = False

    async def begin(self):
        """Begin transaction"""
        if self._transaction_connection and not self._in_transaction:
            await self._transaction_connection.execute("BEGIN")
            self._in_transaction = True

    async def savepoint(self, name: str):
        """Create a savepoint"""
        if self._transaction_connection and self._in_transaction:
            await self._transaction_connection.execute(f"SAVEPOINT {name}")

    async def release_savepoint(self, name: str):
        """Release a savepoint"""
        if self._transaction_connection and self._in_transaction:
            await self._transaction_connection.execute(f"RELEASE SAVEPOINT {name}")

    async def rollback_to_savepoint(self, name: str):
        """Rollback to a savepoint"""
        if self._transaction_connection and self._in_transaction:
            await self._transaction_connection.execute(f"ROLLBACK TO SAVEPOINT {name}")

    @property
    def in_transaction(self) -> bool:
        """Check if cursor is in transaction"""
        return self._in_transaction
