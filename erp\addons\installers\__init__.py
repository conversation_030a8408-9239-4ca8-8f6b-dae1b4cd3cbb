"""
Addon installers

This package provides installer classes for addon installation:
- AddonInstaller: For addon installation/uninstallation/upgrade
- InstallationContext: Context and utilities for installation operations
"""

from .addon_installer import AddonInstaller
from .installation_context import (
    InstallationContext,
    InstallationUtilities,
    RegistryExtractor,
)

__all__ = [
    "AddonInstaller",
    "InstallationContext",
    "InstallationUtilities",
    "RegistryExtractor",
]
