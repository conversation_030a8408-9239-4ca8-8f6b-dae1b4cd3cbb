"""
Template Caching - Performance optimizations and caching mechanisms
"""

import hashlib
import time
import xml.etree.ElementTree as ET
from typing import Any, Dict, Optional, Tuple


class TemplateCache:
    """Template compilation and rendering cache"""

    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        self.max_size = max_size
        self.ttl = ttl  # Time to live in seconds
        self._cache: Dict[str, Tuple[Any, float]] = {}  # key -> (value, timestamp)
        self._access_order: Dict[str, float] = {}  # key -> last_access_time

    def get(self, key: str) -> Optional[Any]:
        """Get item from cache"""
        if key not in self._cache:
            return None

        value, timestamp = self._cache[key]

        # Check if expired
        if time.time() - timestamp > self.ttl:
            self.remove(key)
            return None

        # Update access time
        self._access_order[key] = time.time()
        return value

    def set(self, key: str, value: Any) -> None:
        """Set item in cache"""
        current_time = time.time()

        # Remove oldest items if cache is full
        if len(self._cache) >= self.max_size and key not in self._cache:
            self._evict_oldest()

        self._cache[key] = (value, current_time)
        self._access_order[key] = current_time

    def remove(self, key: str) -> None:
        """Remove item from cache"""
        self._cache.pop(key, None)
        self._access_order.pop(key, None)

    def clear(self) -> None:
        """Clear all cache"""
        self._cache.clear()
        self._access_order.clear()

    def _evict_oldest(self) -> None:
        """Evict the least recently used item"""
        if not self._access_order:
            return

        oldest_key = min(self._access_order.keys(), key=lambda k: self._access_order[k])
        self.remove(oldest_key)

    def size(self) -> int:
        """Get current cache size"""
        return len(self._cache)

    def stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        current_time = time.time()
        expired_count = sum(
            1
            for _, timestamp in self._cache.values()
            if current_time - timestamp > self.ttl
        )

        return {
            "size": len(self._cache),
            "max_size": self.max_size,
            "expired_items": expired_count,
            "ttl": self.ttl,
        }


class CompiledTemplate:
    """Represents a compiled template for faster rendering"""

    def __init__(self, template_element: ET.Element, template_name: str):
        self.template_element = template_element
        self.template_name = template_name
        self.compiled_at = time.time()
        self.render_count = 0
        self.total_render_time = 0.0

        # Pre-analyze template structure for optimizations
        self.has_loops = self._has_directive(template_element, "t-foreach")
        self.has_conditionals = self._has_directive(template_element, "t-if")
        self.has_includes = self._has_directive(template_element, "t-include")
        self.has_inheritance = self._has_directive(template_element, "t-extend")
        self.complexity_score = self._calculate_complexity()

    def _has_directive(self, element: ET.Element, directive: str) -> bool:
        """Check if template has a specific directive"""
        if element.get(directive):
            return True

        for child in element:
            if self._has_directive(child, directive):
                return True

        return False

    def _calculate_complexity(self) -> int:
        """Calculate template complexity score for optimization decisions"""
        score = 0
        score += 10 if self.has_inheritance else 0
        score += 5 if self.has_includes else 0
        score += 3 if self.has_loops else 0
        score += 2 if self.has_conditionals else 0
        score += self._count_elements(self.template_element)
        return score

    def _count_elements(self, element: ET.Element) -> int:
        """Count total elements in template"""
        count = 1
        for child in element:
            count += self._count_elements(child)
        return count

    def record_render(self, render_time: float) -> None:
        """Record rendering statistics"""
        self.render_count += 1
        self.total_render_time += render_time

    @property
    def average_render_time(self) -> float:
        """Get average rendering time"""
        if self.render_count == 0:
            return 0.0
        return self.total_render_time / self.render_count

    def stats(self) -> Dict[str, Any]:
        """Get template statistics"""
        return {
            "name": self.template_name,
            "compiled_at": self.compiled_at,
            "render_count": self.render_count,
            "total_render_time": self.total_render_time,
            "average_render_time": self.average_render_time,
            "complexity_score": self.complexity_score,
            "has_loops": self.has_loops,
            "has_conditionals": self.has_conditionals,
            "has_includes": self.has_includes,
            "has_inheritance": self.has_inheritance,
        }


class PerformanceMonitor:
    """Monitor template rendering performance"""

    def __init__(self):
        self.render_times: Dict[str, list] = {}
        self.total_renders = 0
        self.total_time = 0.0
        self.slow_renders: list = []  # Track renders that take too long
        self.slow_threshold = 0.1  # 100ms threshold

    def record_render(
        self, template_name: str, render_time: float, context_size: int = 0
    ) -> None:
        """Record a template render"""
        self.total_renders += 1
        self.total_time += render_time

        if template_name not in self.render_times:
            self.render_times[template_name] = []

        self.render_times[template_name].append(render_time)

        # Track slow renders
        if render_time > self.slow_threshold:
            self.slow_renders.append(
                {
                    "template": template_name,
                    "time": render_time,
                    "context_size": context_size,
                    "timestamp": time.time(),
                }
            )

            # Keep only recent slow renders
            if len(self.slow_renders) > 100:
                self.slow_renders = self.slow_renders[-50:]

    def get_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        template_stats = {}
        for template_name, times in self.render_times.items():
            template_stats[template_name] = {
                "count": len(times),
                "total_time": sum(times),
                "average_time": sum(times) / len(times),
                "min_time": min(times),
                "max_time": max(times),
            }

        return {
            "total_renders": self.total_renders,
            "total_time": self.total_time,
            "average_time": (
                self.total_time / self.total_renders if self.total_renders > 0 else 0
            ),
            "template_stats": template_stats,
            "slow_renders_count": len(self.slow_renders),
            "slow_threshold": self.slow_threshold,
        }

    def get_slow_renders(self, limit: int = 10) -> list:
        """Get recent slow renders"""
        return sorted(self.slow_renders, key=lambda x: x["time"], reverse=True)[:limit]

    def reset(self) -> None:
        """Reset all statistics"""
        self.render_times.clear()
        self.total_renders = 0
        self.total_time = 0.0
        self.slow_renders.clear()


def generate_cache_key(*args) -> str:
    """Generate a cache key from arguments"""
    # Create a string representation of all arguments
    key_data = str(args)

    # Generate MD5 hash for consistent key length
    return hashlib.md5(key_data.encode("utf-8")).hexdigest()


def serialize_context(context: Dict[str, Any]) -> str:
    """Serialize context for caching (simplified)"""
    try:
        # Only include serializable values for cache key
        serializable_context = {}
        for key, value in context.items():
            if isinstance(value, (str, int, float, bool, list, dict, tuple)):
                serializable_context[key] = value
            else:
                # For non-serializable objects, use their type and id
                serializable_context[key] = f"{type(value).__name__}:{id(value)}"

        return str(sorted(serializable_context.items()))
    except Exception:
        # Fallback to simple string representation
        return str(id(context))


class CacheManager:
    """Manages all template caching operations"""

    def __init__(
        self, enable_template_cache: bool = True, enable_render_cache: bool = True
    ):
        self.enable_template_cache = enable_template_cache
        self.enable_render_cache = enable_render_cache

        # Different caches for different purposes
        self.template_cache = TemplateCache(
            max_size=500, ttl=3600
        )  # Compiled templates
        self.render_cache = TemplateCache(max_size=1000, ttl=300)  # Rendered output
        self.performance_monitor = PerformanceMonitor()

    def get_compiled_template(
        self, template_name: str, template_content: str
    ) -> Optional[CompiledTemplate]:
        """Get compiled template from cache"""
        if not self.enable_template_cache:
            return None

        cache_key = generate_cache_key("template", template_name, template_content)
        return self.template_cache.get(cache_key)

    def set_compiled_template(
        self,
        template_name: str,
        template_content: str,
        compiled_template: CompiledTemplate,
    ) -> None:
        """Store compiled template in cache"""
        if not self.enable_template_cache:
            return

        cache_key = generate_cache_key("template", template_name, template_content)
        self.template_cache.set(cache_key, compiled_template)

    def get_rendered_output(
        self, template_name: str, context: Dict[str, Any]
    ) -> Optional[str]:
        """Get rendered output from cache"""
        if not self.enable_render_cache:
            return None

        context_key = serialize_context(context)
        cache_key = generate_cache_key("render", template_name, context_key)
        return self.render_cache.get(cache_key)

    def set_rendered_output(
        self, template_name: str, context: Dict[str, Any], output: str
    ) -> None:
        """Store rendered output in cache"""
        if not self.enable_render_cache:
            return

        context_key = serialize_context(context)
        cache_key = generate_cache_key("render", template_name, context_key)
        self.render_cache.set(cache_key, output)

    def clear_all(self) -> None:
        """Clear all caches"""
        self.template_cache.clear()
        self.render_cache.clear()
        self.performance_monitor.reset()

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        return {
            "template_cache": self.template_cache.stats(),
            "render_cache": self.render_cache.stats(),
            "performance": self.performance_monitor.get_stats(),
            "slow_renders": self.performance_monitor.get_slow_renders(),
        }


# Global cache manager instance
cache_manager = CacheManager()


def get_cache_manager() -> CacheManager:
    """Get the global cache manager instance"""
    return cache_manager
