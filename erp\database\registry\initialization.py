"""
Database initialization logic
"""

from typing import TYPE_CHECKING

from ...logging import get_logger
from .database_registry import DatabaseRegistry

if TYPE_CHECKING:
    from ...addons import AddonManager
    from ...environment import Environment


class DatabaseInitializer:
    """Database initialization with addon system"""

    _logger = get_logger(__name__)

    @classmethod
    async def initialize_database(cls, db_name: str, install_addon: str = None) -> None:
        """Initialize database with required tables and data, optionally installing specified addon"""
        from ...addons import AddonManager
        from ...environment import EnvironmentManager

        try:
            # Ensure database connection exists
            await DatabaseRegistry.get_database(db_name)

            # Set as current database early for addon installation
            DatabaseRegistry.set_current_database(db_name)

            # Create environment for addon installation
            env = await EnvironmentManager.create_environment(
                db_name, 1
            )  # Use admin user

            # Create addon manager and install base addon
            manager = AddonManager()
            await manager.discover_addons()

            # Install base addon within a transaction context
            # This ensures proper transaction management for CLI operations
            async with EnvironmentManager.transaction(env):
                await manager.install_addon("base", force=True, env=env)
                cls._logger.info("✓ Base addon installed successfully")

                # Important: Update the registry WITHIN the transaction context
                # This ensures the base module is fully committed before registry update
                cls._logger.debug("Updating registry after base module installation...")
                from ...database.memory.registry_manager import MemoryRegistryManager

                # Update the registry - special handling for base module is in MemoryRegistryManager
                await MemoryRegistryManager.update_registry_after_module_action(
                    db_name, "base", "install"
                )
                cls._logger.debug(
                    "✅ Registry successfully updated for database '%s'", db_name
                )

                # Install specified addon if provided
                if install_addon:
                    await cls._install_specified_addon(manager, install_addon, env)

                cls._logger.info("Database %s initialized successfully", db_name)

        except Exception as e:
            cls._logger.error(f"Error initializing database {db_name}: {e}")
            # Rollback database creation on any error
            from .lifecycle import DatabaseLifecycle

            try:
                await DatabaseLifecycle.rollback_database_creation(db_name)
            except Exception as rollback_error:
                cls._logger.error(
                    "Failed to rollback database creation: %s", rollback_error
                )

            # Re-raise the original exception to ensure fail-fast behavior
            raise

    @classmethod
    async def _install_specified_addon(cls, manager: "AddonManager", addon_name: str, env: "Environment") -> None:
        """Install specified addon and its dependencies"""
        try:
            cls._logger.info(f"🔧 Installing addon '{addon_name}' and its dependencies...")

            # Check if addon exists
            addon_info = manager.state_manager.get_addon_info(addon_name)
            if not addon_info:
                # Get list of available addons for helpful error message
                available_addons = list(manager.state_manager.list_addons().keys())
                available_addons.sort()

                error_msg = f"Addon '{addon_name}' not found."
                if available_addons:
                    # Show similar addon names if any
                    similar_addons = [name for name in available_addons if addon_name.lower() in name.lower() or name.lower() in addon_name.lower()]
                    if similar_addons:
                        error_msg += f"\n\nDid you mean one of these?\n  • " + "\n  • ".join(similar_addons[:5])
                    else:
                        error_msg += f"\n\nAvailable addons:\n  • " + "\n  • ".join(available_addons[:10])
                        if len(available_addons) > 10:
                            error_msg += f"\n  ... and {len(available_addons) - 10} more"
                else:
                    error_msg += "\n\nNo addons are currently available. Please check your addons directory."

                raise ValueError(error_msg)

            # Check if addon is installable
            if not addon_info.manifest.installable:
                error_msg = f"Addon '{addon_name}' is not installable."
                error_msg += f"\n\nThis addon is marked as 'installable: False' in its manifest."
                error_msg += f"\nThis usually means it's a library addon or not ready for production use."
                raise ValueError(error_msg)

            # Get installation plan with dependencies
            installation_plan = manager.dependency_manager.get_enhanced_install_order([addon_name])

            # Log installation plan
            cls._logger.info(
                f"Installation plan: {' -> '.join(installation_plan.installation_order)}"
            )
            if installation_plan.dependency_addons:
                cls._logger.info(
                    f"Dependencies to install: {', '.join(installation_plan.dependency_addons)}"
                )

            # Install the addon (this will install dependencies automatically)
            await manager.install_addon(addon_name, force=False, env=env)

            # Update registry after addon installation
            from ...database.memory.registry_manager import MemoryRegistryManager
            await MemoryRegistryManager.update_registry_after_module_action(
                env.cr.db_name, addon_name, "install"
            )

            cls._logger.info(f"✓ Addon '{addon_name}' and dependencies installed successfully")

        except Exception as e:
            cls._logger.error(f"Error installing addon '{addon_name}': {e}")
            raise
