#!/usr/bin/env python3
"""
Comprehensive ERP System Examples
Demonstrates key features and usage patterns
"""
import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from erp.logging import get_logger

logger = get_logger(__name__)


# ============================================================================
# Model Examples
# ============================================================================


def example_model_definition():
    """Example of defining models with fields"""
    from erp.fields import <PERSON>olean, Char, Float, Integer, Selection, Text
    from erp.models import Model

    class ProductModel(Model):
        _name = "product.product"
        _description = "Product"

        # Basic fields
        name = Char(string="Product Name", required=True, size=100)
        description = Text(string="Description")
        price = Float(string="Price", digits=(10, 2))
        active = Boolean(string="Active", default=True)
        sequence = Integer(string="Sequence", default=10)

        # Selection field
        category = Selection(
            [
                ("electronics", "Electronics"),
                ("clothing", "Clothing"),
                ("books", "Books"),
            ],
            string="Category",
            default="electronics",
        )

        async def get_display_name(self):
            """Custom method example"""
            return f"{self.name} - ${self.price}"

    logger.info("✓ Model definition example")
    return ProductModel


async def example_model_operations():
    """Example of model CRUD operations"""
    try:

        # This would work with a real environment
        # env = Environment(cr, uid, context)
        # Product = env['product.product']

        # Create record
        # product = await Product.create({
        #     'name': 'Example Product',
        #     'price': 99.99,
        #     'category': 'electronics'
        # })

        # Search records
        # products = await Product.search([('active', '=', True)])

        # Update records
        # await products.write({'price': 89.99})

        # Delete records
        # await products.unlink()

        logger.info("✓ Model operations example (structure)")

    except Exception as e:
        logger.info(f"Model operations example (expected error): {e}")


# ============================================================================
# Controller Examples
# ============================================================================


def example_controller_definition():
    """Example of defining controllers with routes"""
    from erp.http import route
    from erp.http.controllers import Controller

    class ProductController(Controller):
        """Product management controller"""

        @route("/api/products", methods=["GET"], type="json")
        async def list_products(self, request):
            """List all products"""
            # In real implementation:
            # env = request.state.env
            # Product = env['product.product']
            # products = await Product.search([])

            return self.json_response(
                {
                    "products": [
                        {"id": 1, "name": "Product 1", "price": 99.99},
                        {"id": 2, "name": "Product 2", "price": 149.99},
                    ]
                }
            )

        @route("/api/products", methods=["POST"], type="json")
        async def create_product(self, request):
            """Create a new product"""
            # data = await request.json()
            # env = request.state.env
            # Product = env['product.product']
            # product = await Product.create(data)

            return self.json_response(
                {"success": True, "message": "Product created successfully"}
            )

        @route("/products", methods=["GET"])
        async def product_page(self, request):
            """Product listing page"""
            context = {
                "title": "Products",
                "products": [
                    {"name": "Product 1", "price": 99.99},
                    {"name": "Product 2", "price": 149.99},
                ],
            }
            # Use template name (from t-name attribute), not filename
            return self.render_template("product.list", context)

        @route("/products/<int:product_id>", methods=["GET"])
        async def product_detail(self, request):
            """Product detail page"""
            product_id = request.path_params["product_id"]

            context = {
                "title": f"Product {product_id}",
                "product": {"id": product_id, "name": f"Product {product_id}"},
            }
            # Use template name (from t-name attribute), not filename
            return self.render_template("product.detail", context)

    logger.info("✓ Controller definition example")
    return ProductController


# ============================================================================
# Template Examples
# ============================================================================


async def example_template_usage():
    """Example of template rendering"""
    try:
        from erp.templates.manager import get_template_manager

        manager = get_template_manager()

        # Example template content
        template_content = """
        <templates>
            <t t-name="product.list">
                <div class="product-list">
                    <h1 t-esc="title"/>
                    <div t-if="products">
                        <t t-foreach="products" t-as="product">
                            <div class="product-item">
                                <h3 t-esc="product.name"/>
                                <p>Price: $<span t-esc="product.price"/></p>
                                <p t-if="product.description" t-esc="product.description"/>
                            </div>
                        </t>
                    </div>
                    <div t-else="">
                        <p>No products found.</p>
                    </div>
                </div>
            </t>
        </templates>
        """

        # Render template
        context = {
            "title": "Product Catalog",
            "products": [
                {
                    "name": "Laptop",
                    "price": 999.99,
                    "description": "High-performance laptop",
                },
                {"name": "Mouse", "price": 29.99},
            ],
        }

        result = await manager.render_template_string(
            template_content, "product.list", context
        )

        logger.info("✓ Template rendering example")
        return result

    except Exception as e:
        logger.info(f"Template example (expected error): {e}")


# ============================================================================
# Addon Examples
# ============================================================================


def example_addon_structure():
    """Example of addon structure and manifest"""

    # Example manifest
    manifest = {
        "name": "Product Management",
        "version": "1.0.0",
        "description": "Complete product management system",
        "author": "ERP Team",
        "category": "Sales",
        "depends": ["base"],
        "data": [
            "data/product_categories.xml",
            "views/product_views.xml",
        ],
        "installable": True,
        "auto_install": False,
        "application": True,
    }

    # Example hooks
    def example_hooks():
        from erp.addons.hooks import post_install_hook, pre_install_hook

        @pre_install_hook("product_management", priority=50)
        async def pre_install(context):
            """Pre-installation hook"""
            logger = context.logger
            logger.info("Installing product management addon...")

            # Setup database tables, initial data, etc.
            return True

        @post_install_hook("product_management", priority=50)
        async def post_install(context):
            """Post-installation hook"""
            env = context.env
            logger = context.logger

            logger.info("Setting up default product categories...")

            # Create default categories
            # Category = env['product.category']
            # await Category.create({'name': 'Electronics'})
            # await Category.create({'name': 'Clothing'})

            return True

    logger.info("✓ Addon structure example")
    return manifest


# ============================================================================
# Database Examples
# ============================================================================


async def example_database_operations():
    """Example of database operations"""
    try:
        from erp.database import DatabaseRegistry, get_memory_registry_manager

        # Database registry
        db_registry = DatabaseRegistry()
        config = db_registry.get_database_config()
        logger.info(f"Database config: {config['host']}:{config['port']}")

        # Memory registry
        memory_manager = get_memory_registry_manager()
        registry = await memory_manager.get_registry("example_db")

        # Store and retrieve data
        await registry.set_model_data(
            "product.product",
            {"name": "Product Model", "fields": ["name", "price", "description"]},
        )

        model_data = await registry.get_model_data("product.product")
        logger.info(f"Model data: {model_data}")

        logger.info("✓ Database operations example")

    except Exception as e:
        logger.info(f"Database example (expected error): {e}")


# ============================================================================
# Environment Examples
# ============================================================================


async def example_environment_usage():
    """Example of environment usage"""
    try:
        from erp.context.manager import ContextManager

        # Context management
        context_manager = ContextManager()

        # Set context data
        await context_manager.set("user_id", 1)
        await context_manager.set("database", "example_db")
        await context_manager.set("lang", "en_US")

        # Get context data
        user_id = await context_manager.get("user_id")
        database = await context_manager.get("database")

        logger.info(f"Context - User: {user_id}, Database: {database}")
        logger.info("✓ Environment usage example")

    except Exception as e:
        logger.info(f"Environment example (expected error): {e}")


# ============================================================================
# HTTP Examples
# ============================================================================


def example_http_routes():
    """Example of various HTTP route patterns"""
    from erp.http import route

    # Basic HTTP route
    @route("/api/status", methods=["GET"])
    async def status_check(request):
        return {"status": "ok", "timestamp": "2025-01-01T00:00:00Z"}

    # JSON RPC route
    @route("/api/rpc", methods=["POST"], type="json")
    async def rpc_endpoint(request):
        return {"result": "success"}

    # Database-specific route
    @route("/db/info", methods=["GET"], database="main_db")
    async def database_info(request):
        return {"database": "main_db", "info": "Database-specific route"}

    # Route with parameters
    @route("/api/users/<int:user_id>", methods=["GET"])
    async def get_user(request):
        user_id = request.path_params["user_id"]
        return {"user_id": user_id, "name": f"User {user_id}"}

    logger.info("✓ HTTP routes example")


# ============================================================================
# Main Example Runner
# ============================================================================


async def run_all_examples():
    """Run all examples"""
    logger.info("🚀 ERP System Examples")
    logger.info("=" * 50)

    # Model examples
    logger.info("📋 Model Examples")
    example_model_definition()
    await example_model_operations()

    # Controller examples
    logger.info("\n🌐 Controller Examples")
    example_controller_definition()

    # Template examples
    logger.info("\n📝 Template Examples")
    await example_template_usage()

    # Addon examples
    logger.info("\n🧩 Addon Examples")
    example_addon_structure()

    # Database examples
    logger.info("\n🗄️ Database Examples")
    await example_database_operations()

    # Environment examples
    logger.info("\n🔄 Environment Examples")
    await example_environment_usage()

    # HTTP examples
    logger.info("\n🌐 HTTP Examples")
    example_http_routes()

    logger.info("\n" + "=" * 50)
    logger.info("✅ All examples completed!")


if __name__ == "__main__":
    asyncio.run(run_all_examples())
