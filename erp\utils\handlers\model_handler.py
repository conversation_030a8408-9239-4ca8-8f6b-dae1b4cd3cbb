"""
Model request handlers for ERP system
Contains handlers for CRUD operations on models
"""

from typing import Any, Dict, List, Optional

from fastapi import HTTPException

from ...logging import get_logger

logger = get_logger(__name__)


class ModelRequestHandler:
    """Handler for model-related requests"""

    def __init__(self, model_registry=None):
        self.model_registry = model_registry

    async def handle_create(
        self, model_name: str, data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle model create request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")

        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

        try:
            result = await model.create(data)
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error creating {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def handle_read(
        self, model_name: str, record_ids: List[str], fields: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Handle model read request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")

        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

        try:
            result = await model.browse(record_ids).read(fields)
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error reading {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def handle_write(
        self, model_name: str, record_ids: List[str], data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle model write request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")

        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

        try:
            result = await model.browse(record_ids).write(data)
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error writing {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def handle_unlink(
        self, model_name: str, record_ids: List[str]
    ) -> Dict[str, Any]:
        """Handle model unlink request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")

        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

        try:
            result = await model.browse(record_ids).unlink()
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error unlinking {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def handle_search(
        self, model_name: str, domain: List = None, limit: int = None, offset: int = 0
    ) -> Dict[str, Any]:
        """Handle model search request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")

        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

        try:
            result = await model.search(domain or [], limit=limit, offset=offset)
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error searching {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def handle_search_count(
        self, model_name: str, domain: List = None
    ) -> Dict[str, Any]:
        """Handle model search count request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")

        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

        try:
            result = await model.search_count(domain or [])
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error counting {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def handle_name_get(
        self, model_name: str, record_ids: List[str]
    ) -> Dict[str, Any]:
        """Handle model name_get request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")

        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

        try:
            result = await model.browse(record_ids).name_get()
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error getting names for {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def handle_name_search(
        self,
        model_name: str,
        name: str = "",
        domain: List = None,
        operator: str = "ilike",
        limit: int = 100,
    ) -> Dict[str, Any]:
        """Handle model name_search request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")

        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

        try:
            result = await model.name_search(name, domain or [], operator, limit)
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error name searching {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def handle_fields_get(
        self, model_name: str, allfields: List[str] = None, attributes: List[str] = None
    ) -> Dict[str, Any]:
        """Handle model fields_get request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")

        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

        try:
            result = await model.fields_get(allfields, attributes)
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error getting fields for {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def handle_default_get(
        self, model_name: str, fields_list: List[str]
    ) -> Dict[str, Any]:
        """Handle model default_get request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")

        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

        try:
            result = await model.default_get(fields_list)
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error getting defaults for {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def handle_copy(
        self, model_name: str, record_id: str, default: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Handle model copy request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")

        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

        try:
            result = await model.browse([record_id]).copy(default or {})
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error copying {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def handle_exists(
        self, model_name: str, record_ids: List[str]
    ) -> Dict[str, Any]:
        """Handle model exists request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")

        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

        try:
            result = await model.browse(record_ids).exists()
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error checking existence for {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def handle_custom_method(
        self,
        model_name: str,
        method_name: str,
        record_ids: List[str] = None,
        args: List = None,
        kwargs: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """Handle custom model method request"""
        if not self.model_registry:
            raise HTTPException(status_code=500, detail="Model registry not available")

        model = self.model_registry.get(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

        if not hasattr(model, method_name):
            raise HTTPException(
                status_code=404,
                detail=f"Method {method_name} not found on model {model_name}",
            )

        try:
            method = getattr(model, method_name)

            # If record_ids provided, call method on recordset
            if record_ids:
                recordset = model.browse(record_ids)
                method = getattr(recordset, method_name)

            # Call method with provided arguments
            if args is None:
                args = []
            if kwargs is None:
                kwargs = {}

            result = await method(*args, **kwargs)
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error calling {method_name} on {model_name}: {e}")
            raise HTTPException(status_code=500, detail=str(e))
