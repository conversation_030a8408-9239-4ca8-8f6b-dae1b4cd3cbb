"""
Test suite for database memory registry functionality

This module tests:
- AppRegistry initialization and components
- Module registration and management
- Cache operations and query caching
- Memory registry lifecycle management
"""

import pytest

from erp.database import AppRegistry
from erp.database.memory import CacheManager


class TestAppRegistry:
    """Test AppRegistry functionality"""

    def test_memory_registry_initialization(self):
        """Test memory registry initialization"""
        registry = AppRegistry("test_db")

        assert registry.db_name == "test_db"
        assert isinstance(registry.cache_manager, CacheManager)
        assert hasattr(registry, "addon_manager")
        assert hasattr(registry, "model_metadata_manager")
        assert hasattr(registry, "route_manager")
        assert hasattr(registry, "installed_modules")
        assert hasattr(registry, "environments")

    @pytest.mark.asyncio
    async def test_memory_registry_module_operations(self):
        """Test module registration and retrieval"""
        registry = AppRegistry("test_db")

        module_info = {
            "display_name": "Test Module",
            "version": "1.0.0",
            "state": "installed",
        }

        await registry.register_module("test_module", module_info)
        modules = await registry.get_installed_modules()

        assert "test_module" in modules
        assert modules["test_module"]["display_name"] == "Test Module"

    @pytest.mark.asyncio
    async def test_memory_registry_cache_operations(self):
        """Test cache functionality"""
        registry = DatabaseMemoryRegistry("test_db")

        # Test caching
        await registry.cache_query("test_key", {"data": "test_value"})
        result = await registry.get_cached_query("test_key")

        assert result == {"data": "test_value"}

    @pytest.mark.asyncio
    async def test_memory_registry_cached_query_execution(self):
        """Test cached query execution"""
        registry = AppRegistry("test_db")

        async def dummy_query():
            return {"result": "from_query"}

        # First call should execute query
        result1 = await registry.execute_query_cached("test_query", dummy_query)
        assert result1 == {"result": "from_query"}

        # Second call should return cached result
        result2 = await registry.execute_query_cached("test_query", dummy_query)
        assert result2 == {"result": "from_query"}
