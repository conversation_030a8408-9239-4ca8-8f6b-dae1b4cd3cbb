"""
Addon system constants

This module contains shared constants used throughout the addon system.
"""

from enum import Enum


class AddonState(Enum):
    """Addon installation states"""

    UNINSTALLED = "uninstalled"
    INSTALLED = "installed"
    TO_UPGRADE = "to_upgrade"
    TO_REMOVE = "to_remove"
    TO_INSTALL = "to_install"


class AddonCategory:
    """Standard addon categories"""

    ACCOUNTING = "Accounting"
    ADMINISTRATION = "Administration"
    APPS = "Apps"
    BASE = "Base"
    CRM = "Customer Relationship Management"
    ECOMMERCE = "eCommerce"
    HUMAN_RESOURCES = "Human Resources"
    INVENTORY = "Inventory"
    LOCALIZATION = "Localization"
    MANUFACTURING = "Manufacturing"
    MARKETING = "Marketing"
    POINT_OF_SALE = "Point of Sale"
    PROJECT = "Project"
    PURCHASES = "Purchases"
    SALES = "Sales"
    SERVICES = "Services"
    TOOLS = "Tools"
    UNCATEGORIZED = "Uncategorized"
    WEBSITE = "Website"


class ManifestKeys:
    """Standard manifest file keys"""

    NAME = "name"
    VERSION = "version"
    DESCRIPTION = "description"
    AUTHOR = "author"
    DEPENDS = "depends"
    DATA = "data"
    DEMO = "demo"
    INSTALLABLE = "installable"
    AUTO_INSTALL = "auto_install"
    CATEGORY = "category"
    SUMMARY = "summary"
    WEBSITE = "website"
    LICENSE = "license"
    EXTERNAL_DEPENDENCIES = "external_dependencies"
    IMAGES = "images"
    APPLICATION = "application"
    SEQUENCE = "sequence"


class DefaultValues:
    """Default values for addon properties"""

    VERSION = "1.0.0"
    AUTHOR = "ERP System"
    CATEGORY = AddonCategory.UNCATEGORIZED
    INSTALLABLE = True
    AUTO_INSTALL = False
    APPLICATION = False
    SEQUENCE = 100


class FileNames:
    """Standard addon file names"""

    MANIFEST = "__manifest__.py"
    INIT = "__init__.py"
    MODELS_INIT = "models/__init__.py"
    VIEWS_DIR = "views"
    DATA_DIR = "data"
    DEMO_DIR = "demo"
    STATIC_DIR = "static"
    SECURITY_DIR = "security"


class DatabaseTables:
    """Standard database table names"""

    IR_MODULE_MODULE = "ir_module_module"
    IR_MODEL = "ir_model"
    IR_MODEL_FIELDS = "ir_model_fields"
    IR_MODEL_CONSTRAINT = "ir_model_constraint"
    IR_MODEL_RELATION = "ir_model_relation"


class ModuleStates:
    """Module states in ir.module.module table"""

    UNINSTALLED = "uninstalled"
    UNINSTALLABLE = "uninstallable"
    INSTALLED = "installed"
    TO_UPGRADE = "to upgrade"
    TO_REMOVE = "to remove"
    TO_INSTALL = "to install"


class HookPriorities:
    """Standard hook execution priorities"""

    HIGHEST = 0
    HIGH = 25
    NORMAL = 50
    LOW = 75
    LOWEST = 100


class ValidationLevels:
    """Validation severity levels"""

    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    DEBUG = "debug"


class LogMessages:
    """Standard log message templates"""

    ADDON_INSTALLED = "✅ Successfully installed addon {addon_name} in {duration:.3f}s"
    ADDON_UNINSTALLED = (
        "✅ Successfully uninstalled addon {addon_name} in {duration:.3f}s"
    )
    ADDON_UPGRADED = "✅ Successfully upgraded addon {addon_name} in {duration:.3f}s"
    ADDON_INSTALL_FAILED = (
        "❌ Failed to install addon {addon_name} after {duration:.3f}s: {error}"
    )
    ADDON_UNINSTALL_FAILED = (
        "❌ Failed to uninstall addon {addon_name} after {duration:.3f}s: {error}"
    )
    ADDON_UPGRADE_FAILED = (
        "❌ Failed to upgrade addon {addon_name} after {duration:.3f}s: {error}"
    )

    HOOK_EXECUTING = "Executing {hook_type} hooks for {addon_name}"
    HOOK_EXECUTED = "✅ Executed {hook_type} hooks for {addon_name}"
    HOOK_FAILED = "❌ Failed to execute {hook_type} hooks for {addon_name}: {error}"

    REGISTRY_UPDATED = (
        "✅ Registry successfully updated after {action} for {addon_name}"
    )
    REGISTRY_UPDATE_FAILED = (
        "❌ Failed to update registry after {action} for {addon_name}: {error}"
    )

    VALIDATION_PASSED = "✅ Validation passed for {addon_name}"
    VALIDATION_FAILED = "❌ Validation failed for {addon_name}: {errors}"


class ErrorCodes:
    """Standard error codes for addon operations"""

    ADDON_NOT_FOUND = "ADDON_NOT_FOUND"
    ADDON_ALREADY_INSTALLED = "ADDON_ALREADY_INSTALLED"
    ADDON_NOT_INSTALLABLE = "ADDON_NOT_INSTALLABLE"
    DEPENDENCY_NOT_FOUND = "DEPENDENCY_NOT_FOUND"
    CIRCULAR_DEPENDENCY = "CIRCULAR_DEPENDENCY"
    MANIFEST_ERROR = "MANIFEST_ERROR"
    INSTALLATION_ERROR = "INSTALLATION_ERROR"
    UNINSTALLATION_ERROR = "UNINSTALLATION_ERROR"
    UPGRADE_ERROR = "UPGRADE_ERROR"
    HOOK_ERROR = "HOOK_ERROR"
    REGISTRY_ERROR = "REGISTRY_ERROR"
    VALIDATION_ERROR = "VALIDATION_ERROR"
