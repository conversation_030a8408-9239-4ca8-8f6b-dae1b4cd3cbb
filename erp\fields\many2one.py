"""
Many2One relational field implementation
"""

from .base import FieldValidationError, RelationalField


class Many2One(RelationalField):
    """Many-to-one relationship field (foreign key)"""

    def __init__(self, comodel_name, ondelete="set null", **kwargs):
        """
        Initialize Many2One field

        Args:
            comodel_name: Name of the related model
            ondelete: Action when referenced record is deleted
                     ('cascade', 'set null', 'restrict')
        """
        super().__init__(comodel_name, **kwargs)
        self.ondelete = ondelete

    def get_sql_type(self):
        """Get SQL type for foreign key"""
        return "UUID"  # References the id field of related model

    def _validate_value(self, value):
        """Validate Many2One field value"""
        if value is None:
            return None

        # Value can be:
        # 1. A string (UUID of the related record)
        # 2. A model instance
        # 3. A RecordSet with single record

        if isinstance(value, str):
            # Validate UUID format
            import uuid

            try:
                uuid.UUID(value)
                return value
            except ValueError:
                raise FieldValidationError(f"Invalid UUID format: {value}")

        elif hasattr(value, "id"):
            # Model instance - get its ID
            return value.id

        elif hasattr(value, "_records") and len(value._records) == 1:
            # RecordSet with single record
            return value._records[0].id

        elif hasattr(value, "_records") and len(value._records) == 0:
            # Empty RecordSet
            return None

        elif hasattr(value, "_records") and len(value._records) > 1:
            # RecordSet with multiple records
            raise FieldValidationError(
                "Cannot assign multiple records to Many2One field"
            )

        else:
            raise FieldValidationError(
                f"Invalid value type for Many2One field: {type(value)}"
            )

    def convert_to_cache(self, value):
        """Convert value for caching - store the ID"""
        if value is None:
            return None
        return str(value)

    def convert_to_record(self, value):
        """Convert cached value back to record value"""
        if value is None:
            return None
        # TODO: Implement proper RecordSet creation when model registry is available
        return value
