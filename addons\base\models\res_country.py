"""
Country and State models - Geographic location management

This module contains models for managing countries and their states/provinces
in the ERP system, similar to Odoo's res.country and res.country.state models.
"""

from erp.models import Model
from erp import fields


class ResCountry(Model):
    """Countries"""

    _name = 'res.country'
    _description = 'Country'

    name = fields.Char(string='Country Name', required=True, translate=True, size=64, index=True,
                help='Name of the country')

    code = fields.Char(string='Country Code', required=True, size=2, unique=True, index=True,
               help='ISO 3166-1 alpha-2 country code')

    address_format = fields.Text(string='Address Format',
                         help='Format for addresses in this country')

    currency_id = fields.Many2One('res.currency', string='Currency', index=True,
                          help='Default currency for this country')

    phone_code = fields.Integer(string='Country Calling Code',
                        help='Country calling code for phone numbers')

    def format_address(self, partner):
        """
        Format address using country-specific address format

        Args:
            partner: Partner record with address fields

        Returns:
            Formatted address string
        """
        if not self.address_format:
            return self._format_address_fallback(partner)

        # Prepare address data for formatting
        address_data = self._prepare_address_data(partner)

        try:
            # Use country's address format template
            formatted = self.address_format % address_data
            # Clean up empty lines and extra whitespace
            lines = [line.strip() for line in formatted.split('\n') if line.strip()]
            return '\n'.join(lines)
        except (KeyError, TypeError, ValueError):
            # Fallback if formatting fails
            return self._format_address_fallback(partner)

    def _prepare_address_data(self, partner):
        """
        Prepare address data dictionary for formatting

        Args:
            partner: Partner record with address fields

        Returns:
            Dictionary with address field values
        """
        # Get state information
        state_name = partner.state_id.name if partner.state_id else ''
        state_code = partner.state_id.code if partner.state_id else ''

        return {
            'name': partner.name or '',
            'street': partner.street or '',
            'street2': partner.street2 or '',
            'city': partner.city or '',
            'zip': partner.zip or '',
            'state_name': state_name,
            'state_code': state_code,
            'country_name': self.name or '',
            'country_code': self.code or '',
        }

    def _format_address_fallback(self, partner):
        """
        Fallback address formatting when country format is not available

        Args:
            partner: Partner record with address fields

        Returns:
            Simple formatted address string
        """
        address_parts = []

        if partner.name:
            address_parts.append(partner.name)
        if partner.street:
            address_parts.append(partner.street)
        if partner.street2:
            address_parts.append(partner.street2)

        # City, State, ZIP line
        city_line_parts = []
        if partner.city:
            city_line_parts.append(partner.city)
        if partner.state_id:
            city_line_parts.append(partner.state_id.name)
        if partner.zip:
            city_line_parts.append(partner.zip)

        if city_line_parts:
            address_parts.append(', '.join(city_line_parts))

        if self.name:
            address_parts.append(self.name)

        return '\n'.join(address_parts)


class ResCountryState(Model):
    """Country states/provinces"""

    _name = 'res.country.state'
    _description = 'Country State'

    name = fields.Char(string='State Name', required=True, translate=True, size=64, index=True,
                help='Name of the state or province')

    code = fields.Char(string='State Code', required=True, size=3, index=True,
               help='State or province code')

    country_id = fields.Many2One('res.country', string='Country', required=True, index=True,
                         help='Country this state belongs to')
