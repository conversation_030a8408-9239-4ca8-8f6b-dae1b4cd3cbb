"""
IR validation utilities
Contains validation logic for IR metadata integrity and consistency
"""

from typing import Any, Dict, List

from ...logging import get_logger
from .metadata import IRMetadataOperations

logger = get_logger(__name__)


class IRValidationManager:
    """
    Manager for validating IR metadata integrity and consistency
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.metadata_ops = IRMetadataOperations()

    async def validate_ir_population_prerequisites(self, db_manager) -> Dict[str, Any]:
        """
        Validate that all prerequisites for IR population are met.

        Args:
            db_manager: Database manager instance

        Returns:
            Dictionary with validation results
        """
        try:
            results = {
                "status": "success",
                "prerequisites_met": True,
                "missing_tables": [],
                "errors": [],
            }

            # Check if required IR tables exist
            required_tables = ["ir_model", "ir_model_fields"]

            for table in required_tables:
                exists = await db_manager.fetchval(
                    "SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = $1)",
                    table,
                )

                if not exists:
                    results["missing_tables"].append(table)
                    results["prerequisites_met"] = False

            if results["missing_tables"]:
                results["status"] = "error"
                results["errors"].append(
                    f"Missing required tables: {', '.join(results['missing_tables'])}"
                )

            return results

        except Exception as e:
            return {
                "status": "error",
                "prerequisites_met": False,
                "missing_tables": [],
                "errors": [f"Failed to validate prerequisites: {e}"],
            }

    async def validate_ir_population_integrity(
        self, db_manager, addon_name: str = None
    ) -> Dict[str, Any]:
        """
        Validate the integrity of IR population for an addon or all models.

        This method checks that all models have corresponding IR metadata
        and that the metadata is consistent with the actual model definitions.

        Args:
            db_manager: Database manager instance
            addon_name: Specific addon to validate, or None for all

        Returns:
            Dictionary containing validation results
        """
        try:
            self.logger.info(
                f"Validating IR population integrity for addon: {addon_name or 'all'}"
            )

            # Get models to validate
            models_to_validate = await self._get_models_for_validation(addon_name)

            results = {
                "status": "success",
                "models_validated": 0,
                "models_missing_ir": [],
                "models_inconsistent": [],
                "fields_missing": [],
                "errors": [],
                "addon_name": addon_name,
            }

            for model_name in models_to_validate:
                try:
                    # Check if model exists in ir.model
                    model_exists = await db_manager.fetchval(
                        "SELECT EXISTS(SELECT 1 FROM ir_model WHERE model = $1)",
                        model_name,
                    )

                    if not model_exists:
                        results["models_missing_ir"].append(model_name)
                        continue

                    # Check if model fields exist in ir.model.fields
                    from ..schema.generator import SchemaGenerator

                    model_schema = SchemaGenerator.get_model_schema(model_name)

                    if model_schema and "fields" in model_schema:
                        for field_name in model_schema["fields"].keys():
                            field_exists = await db_manager.fetchval(
                                "SELECT EXISTS(SELECT 1 FROM ir_model_fields WHERE model = $1 AND name = $2)",
                                model_name,
                                field_name,
                            )

                            if not field_exists:
                                results["fields_missing"].append(
                                    f"{model_name}.{field_name}"
                                )

                    results["models_validated"] += 1

                except Exception as e:
                    error_msg = f"Error validating model {model_name}: {e}"
                    results["errors"].append(error_msg)
                    self.logger.error(error_msg)

            # Determine overall status
            if (
                results["models_missing_ir"]
                or results["fields_missing"]
                or results["errors"]
            ):
                results["status"] = "issues_found"

            self.logger.info(
                f"IR integrity validation completed: {results['models_validated']} models validated, "
                f"{len(results['models_missing_ir'])} missing IR, "
                f"{len(results['fields_missing'])} missing fields"
            )

            return results

        except Exception as e:
            error_msg = f"Failed to validate IR population integrity: {e}"
            self.logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg,
                "models_validated": 0,
                "models_missing_ir": [],
                "models_inconsistent": [],
                "fields_missing": [],
                "errors": [error_msg],
                "addon_name": addon_name,
            }

    async def _get_models_for_validation(self, addon_name: str = None) -> List[str]:
        """
        Get list of models that need validation.

        Args:
            addon_name: Specific addon name, or None for all models

        Returns:
            List of model names to validate
        """
        try:
            from ...models.registry import create_addon_model_registry

            # Create a temporary registry for the addon
            model_registry = create_addon_model_registry(addon_name or "base")
            all_models = model_registry.all()

            if addon_name is None:
                # Return all registered models
                model_list = list(all_models.keys())
                self.logger.info(f"Found {len(model_list)} models for validation")
                return model_list

            # Filter models by addon (reuse logic from population manager)
            filtered_models = self._filter_models_by_addon(all_models, addon_name)

            if not filtered_models:
                self.logger.warning(
                    f"Could not filter models for addon '{addon_name}', using all models"
                )
                filtered_models = list(all_models.keys())

            self.logger.info(
                f"Found {len(filtered_models)} models for addon '{addon_name}' validation"
            )
            return filtered_models

        except Exception as e:
            self.logger.error(f"Failed to get models for validation: {e}")
            return []

    def _filter_models_by_addon(self, all_models: Dict, addon_name: str) -> List[str]:
        """
        Filter models by addon name based on model module information.
        (Reused from population manager for consistency)

        Args:
            all_models: Dictionary of all registered models
            addon_name: Name of the addon to filter by

        Returns:
            List of model names belonging to the specified addon
        """
        try:
            filtered_models = []

            for model_name, model_class in all_models.items():
                try:
                    # Try to determine addon from model class module
                    if hasattr(model_class, "__module__"):
                        module_parts = model_class.__module__.split(".")

                        # Check if module follows pattern: erp.addons.{addon_name}.models.*
                        # or addons.{addon_name}.models.*
                        if len(module_parts) >= 3:
                            if (
                                module_parts[0] == "erp"
                                and module_parts[1] == "addons"
                                and len(module_parts) >= 4
                                and module_parts[2] == addon_name
                            ):
                                filtered_models.append(model_name)
                            elif (
                                module_parts[0] == "addons"
                                and len(module_parts) >= 3
                                and module_parts[1] == addon_name
                            ):
                                filtered_models.append(model_name)

                    # Also check for base models when addon is 'base'
                    if addon_name == "base" and model_name.startswith("ir."):
                        if model_name not in filtered_models:
                            filtered_models.append(model_name)

                except Exception as e:
                    self.logger.debug(
                        f"Could not determine addon for model {model_name}: {e}"
                    )
                    continue

            return filtered_models

        except Exception as e:
            self.logger.error(f"Error filtering models by addon '{addon_name}': {e}")
            return []

    async def cleanup_orphaned_ir_metadata(self, db_manager) -> Dict[str, Any]:
        """
        Clean up orphaned IR metadata for models that no longer exist.

        Args:
            db_manager: Database manager instance

        Returns:
            Dictionary containing cleanup results
        """
        try:
            self.logger.info("Starting cleanup of orphaned IR metadata")

            # Get all models from IR tables
            ir_models = await db_manager.fetch("SELECT DISTINCT model FROM ir_model")
            ir_field_models = await db_manager.fetch(
                "SELECT DISTINCT model FROM ir_model_fields"
            )

            # Get currently registered models
            from ...models.registry import create_addon_model_registry

            model_registry = create_addon_model_registry(
                "base"
            )  # Use base to get all models
            registered_models = set(model_registry.all().keys())

            results = {
                "status": "success",
                "orphaned_models_removed": 0,
                "orphaned_fields_removed": 0,
                "errors": [],
            }

            # Clean up orphaned models
            for row in ir_models:
                model_name = row["model"]
                if model_name not in registered_models:
                    try:
                        await db_manager.execute(
                            "DELETE FROM ir_model WHERE model = $1", model_name
                        )
                        results["orphaned_models_removed"] += 1
                        self.logger.debug(f"Removed orphaned model: {model_name}")
                    except Exception as e:
                        error_msg = f"Failed to remove orphaned model {model_name}: {e}"
                        results["errors"].append(error_msg)
                        self.logger.error(error_msg)

            # Clean up orphaned fields
            for row in ir_field_models:
                model_name = row["model"]
                if model_name not in registered_models:
                    try:
                        deleted_count = await db_manager.execute(
                            "DELETE FROM ir_model_fields WHERE model = $1", model_name
                        )
                        results["orphaned_fields_removed"] += deleted_count
                        self.logger.debug(
                            f"Removed {deleted_count} orphaned fields for model: {model_name}"
                        )
                    except Exception as e:
                        error_msg = (
                            f"Failed to remove orphaned fields for {model_name}: {e}"
                        )
                        results["errors"].append(error_msg)
                        self.logger.error(error_msg)

            if results["errors"]:
                results["status"] = "partial"

            self.logger.info(
                f"IR metadata cleanup completed: {results['orphaned_models_removed']} models, "
                f"{results['orphaned_fields_removed']} fields removed"
            )

            return results

        except Exception as e:
            error_msg = f"Failed to cleanup orphaned IR metadata: {e}"
            self.logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg,
                "orphaned_models_removed": 0,
                "orphaned_fields_removed": 0,
                "errors": [error_msg],
            }

    async def validate_model_field_consistency(
        self, db_manager, model_name: str
    ) -> Dict[str, Any]:
        """
        Validate consistency between model definition and IR metadata for a specific model.

        Args:
            db_manager: Database manager instance
            model_name: Name of the model to validate

        Returns:
            Dictionary containing consistency validation results
        """
        try:
            # Get model schema from definition
            from ..schema.generator import SchemaGenerator

            model_schema = SchemaGenerator.get_model_schema(model_name)

            if not model_schema:
                return {
                    "status": "error",
                    "message": f"Could not get schema for model: {model_name}",
                }

            # Get IR metadata
            ir_model = await self.metadata_ops.get_model_metadata(
                db_manager, model_name
            )
            ir_fields = await self.metadata_ops.get_field_metadata(
                db_manager, model_name
            )

            results = {
                "status": "success",
                "model_name": model_name,
                "model_consistent": True,
                "field_inconsistencies": [],
                "missing_ir_fields": [],
                "extra_ir_fields": [],
                "errors": [],
            }

            if not ir_model:
                results["model_consistent"] = False
                results["errors"].append(f"Model {model_name} not found in IR metadata")
                return results

            # Check field consistency
            schema_fields = set(model_schema.get("fields", {}).keys())
            ir_field_names = {field["name"] for field in ir_fields}

            # Find missing IR fields
            results["missing_ir_fields"] = list(schema_fields - ir_field_names)

            # Find extra IR fields
            results["extra_ir_fields"] = list(ir_field_names - schema_fields)

            # Check field type consistency
            for field in ir_fields:
                field_name = field["name"]
                if field_name in model_schema.get("fields", {}):
                    schema_field = model_schema["fields"][field_name]
                    ir_field_type = field.get("ttype", "")
                    schema_field_type = schema_field.get("type", "")

                    # TODO: Add more sophisticated type comparison
                    if ir_field_type != schema_field_type:
                        results["field_inconsistencies"].append(
                            {
                                "field": field_name,
                                "ir_type": ir_field_type,
                                "schema_type": schema_field_type,
                            }
                        )

            # Determine overall consistency
            if (
                results["missing_ir_fields"]
                or results["extra_ir_fields"]
                or results["field_inconsistencies"]
                or results["errors"]
            ):
                results["status"] = "inconsistent"
                results["model_consistent"] = False

            return results

        except Exception as e:
            return {
                "status": "error",
                "message": f"Error validating model {model_name}: {e}",
                "model_name": model_name,
                "model_consistent": False,
                "errors": [str(e)],
            }
