"""
Model registry lifecycle management
"""

from typing import Any, Dict, List, Optional, Type

from ..logging import get_logger
from .base_model import BaseModel
from .model_discovery import ModelDiscovery
from .model_registration import ModelRegistration


class ModelRegistryLifecycleManager:
    """
    Lifecycle-bound model registry for addon operations

    This registry is temporary and addon-specific. It's created when an addon
    operation begins and disposed of when the operation completes.

    Key characteristics:
    - Lifecycle-bound: Only exists during addon operations
    - Temporary scope: Created and disposed per operation
    - Addon-specific: Tracks models only for the specific addon being processed
    """

    def __init__(self, addon_name: str):
        """
        Initialize lifecycle manager for a specific addon

        Args:
            addon_name: Name of the addon to manage models for
        """
        self.addon_name = addon_name
        self.logger = get_logger(f"{__name__}.{addon_name}")

        # Initialize components
        self._discovery = ModelDiscovery(addon_name)
        self._registration = ModelRegistration(addon_name)
        self._discovered = False

        self.logger.debug(
            f"Created ModelRegistryLifecycleManager for addon: {addon_name}"
        )

    def discover_models(self) -> None:
        """
        Discover all models in the addon and register them
        """
        if self._discovered:
            self.logger.debug(f"Models already discovered for addon: {self.addon_name}")
            return

        try:
            # Discover models using the discovery component
            models = self._discovery.discover_addon_models()

            # Register the discovered models
            self._registration.register_models(models)

            self._discovered = True
            self.logger.info(
                f"Discovered and registered {len(models)} models for addon: {self.addon_name}"
            )

        except Exception as e:
            self.logger.error(
                f"Failed to discover models for addon {self.addon_name}: {e}"
            )
            raise

    def all(self) -> Dict[str, Type[BaseModel]]:
        """
        Get all discovered models

        Returns:
            Dictionary mapping model names to model classes
        """
        if not self._discovered:
            self.discover_models()

        return self._registration.get_all_models()

    def get_models_by_type(
        self, model_type: str = "standard"
    ) -> Dict[str, Type[BaseModel]]:
        """
        Get models filtered by type

        Args:
            model_type: Type of models to return ('standard', 'abstract', 'transient', 'all')

        Returns:
            Dictionary mapping model names to model classes of the specified type
        """
        if not self._discovered:
            self.discover_models()

        return self._registration.get_models_by_type(model_type)

    def get(self, model_name: str) -> Optional[Type[BaseModel]]:
        """
        Get a specific model by name

        Args:
            model_name: Technical name of the model

        Returns:
            Model class or None if not found
        """
        if not self._discovered:
            self.discover_models()

        return self._registration.get_model(model_name)

    def get_model_names(self) -> List[str]:
        """
        Get list of all model names in this addon

        Returns:
            List of model names
        """
        if not self._discovered:
            self.discover_models()

        return self._registration.get_model_names()

    def get_model_fields(self, model_name: str) -> Dict[str, Any]:
        """
        Get all fields for a specific model

        Args:
            model_name: Technical name of the model

        Returns:
            Dictionary mapping field names to field objects
        """
        if not self._discovered:
            self.discover_models()

        return self._registration.get_model_fields(model_name)

    def get_all_models_with_fields(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all models with their field information

        Returns:
            Dictionary mapping model names to their field dictionaries
        """
        if not self._discovered:
            self.discover_models()

        return self._registration.get_all_models_with_fields()

    def has_models(self) -> bool:
        """
        Check if this addon has any models

        Returns:
            True if addon has models, False otherwise
        """
        if not self._discovered:
            self.discover_models()

        return self._registration.has_models()

    def clear(self) -> None:
        """
        Clear the registry (for cleanup)
        """
        self._registration.clear()
        self._discovered = False
        self.logger.debug(
            f"Cleared ModelRegistryLifecycleManager for addon: {self.addon_name}"
        )

    def __len__(self) -> int:
        """Get number of models in registry"""
        if not self._discovered:
            self.discover_models()
        return self._registration.get_model_count()

    def __contains__(self, model_name: str) -> bool:
        """Check if model exists in registry"""
        if not self._discovered:
            self.discover_models()
        return self._registration.model_exists(model_name)

    def __repr__(self) -> str:
        """String representation"""
        model_count = (
            self._registration.get_model_count() if self._discovered else "unknown"
        )
        return f"<ModelRegistryLifecycleManager(addon={self.addon_name}, models={model_count})>"


# Use the full name to avoid confusion
ModelRegistry = ModelRegistryLifecycleManager


def create_addon_model_registry(addon_name: str) -> ModelRegistryLifecycleManager:
    """
    Factory function to create a ModelRegistry for a specific addon

    Args:
        addon_name: Name of the addon to create registry for

    Returns:
        ModelRegistryLifecycleManager instance for the addon
    """
    return ModelRegistryLifecycleManager(addon_name)
