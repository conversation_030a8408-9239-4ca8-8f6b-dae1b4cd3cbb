"""
In-memory registry system for databases
"""

from .addon_manager import <PERSON>donManager
from .app_registry import AppRegistry
from .app_registry_route_manager import AppRegistryRouteManager
from .cache_manager import CacheManager, QueryCache
from .filter_processor import DatabaseFilterProcessor
from .model_metadata_manager import ModelMetadataManager
from .registry_manager import MemoryRegistryManager

__all__ = [
    "AppRegistry",
    "MemoryRegistryManager",
    "DatabaseFilterProcessor",
    "CacheManager",
    "QueryCache",
    "AddonManager",
    "ModelMetadataManager",
    "AppRegistryRouteManager",
]


# Convenience function for external access
def get_memory_registry_manager() -> MemoryRegistryManager:
    """
    Convenience function to get the MemoryRegistryManager class
    Provides easy access to the singleton registry manager
    """
    return MemoryRegistryManager
