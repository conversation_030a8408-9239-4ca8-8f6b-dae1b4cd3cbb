"""
Model for tracking XML ID mappings in the ERP system
"""
import os

# Import base model
from erp.models import Model
from erp import fields

# Ensure this addon is available via standardized import
from erp.addons import ensure_addon_import
addon_path = os.path.join(os.path.dirname(__file__), '..')
ensure_addon_import('base', addon_path)


class IrModelData(Model):
    """Model for storing XML ID to record ID mappings"""

    _name = 'ir.model.data'
    _description = 'Model Data'

    # Define composite unique constraints and checks
    _sql_constraints = [
        ('unique_module_name', 'UNIQUE (module, name)',
         'XML ID must be unique per module'),
        ('check_module_format',
         "CHECK (module ~ '^[a-z][a-z0-9_]*$')",
         'Module name must be valid identifier'),
        ('check_name_format',
         "CHECK (name ~ '^[a-z][a-z0-9_]*$')",
         'XML ID name must be valid identifier'),
        ('check_model_format',
         "CHECK (model ~ '^[a-z][a-z0-9_]*\\.[a-z][a-z0-9_]*$')",
         'Model name must follow format: module.name'),
        ('check_res_id_not_empty',
         "CHECK (res_id IS NOT NULL AND res_id != '')",
         'Record ID cannot be empty'),
    ]

    # Core fields for XML ID tracking
    module = fields.Char(
        string='Module', required=True, index=True, size=64,
        help='Module that defines this XML ID'
    )
    name = fields.Char(
        string='External Identifier', required=True, index=True, size=128,
        help='XML ID name (without module prefix)'
    )
    model = fields.Char(
        string='Model', required=True, index=True, size=64,
        help='Model name that this XML ID references'
    )
    res_id = fields.Char(
        string='Record ID', required=True, index=True, size=64,
        help='ID of the referenced record'
    )
    noupdate = fields.Boolean(
        string='No Update', default=False, index=True,
        help='If True, record will not be updated during module upgrade'
    )

    # Computed fields (not stored for performance)
    complete_name = fields.Char(
        string='Complete Name', compute='_compute_complete_name',
        store=False, index=False, size=192,
        help='Complete XML ID in module.name format'
    )
    display_name = fields.Char(
        string='Display Name', compute='_compute_display_name', store=False,
        help='Display name in format: module.name -> model (res_id)'
    )
    
    def _compute_complete_name(self):
        """Compute the complete XML ID (module.name)"""
        for record in self:
            if record.module and record.name:
                record.complete_name = f"{record.module}.{record.name}"
            else:
                record.complete_name = record.name or ''

    def _compute_display_name(self):
        """Compute display name for the XML ID"""
        for record in self:
            if record.module and record.name and record.model and record.res_id:
                record.display_name = (
                    f"{record.module}.{record.name} -> {record.model} ({record.res_id})"
                )
            elif record.module and record.name:
                record.display_name = f"{record.module}.{record.name}"
            else:
                record.display_name = record.name or "New XML ID"

    @classmethod
    async def xmlid_lookup(cls, xml_id: str):
        """
        Look up a record by its XML ID
        
        Args:
            xml_id: XML ID in format 'module.name' or just 'name'
            
        Returns:
            Dictionary with model and res_id, or None if not found
        """
        if '.' in xml_id:
            module, name = xml_id.split('.', 1)
        else:
            # If no module specified, assume current module context
            module = None
            name = xml_id
            
        domain = [('name', '=', name)]
        if module:
            domain.append(('module', '=', module))
            
        record = await cls.search(domain, limit=1)
        if record:
            return {
                'model': record.model,
                'res_id': record.res_id,
                'id': record.res_id  # For compatibility
            }
        return None

    @classmethod
    async def xmlid_to_res_id(cls, xml_id: str, raise_if_not_found: bool = True):
        """
        Convert XML ID to record ID
        
        Args:
            xml_id: XML ID in format 'module.name' or just 'name'
            raise_if_not_found: Whether to raise exception if not found
            
        Returns:
            Record ID as string, or None if not found and raise_if_not_found=False
        """
        result = await cls.xmlid_lookup(xml_id)
        if result:
            return result['res_id']
        elif raise_if_not_found:
            raise ValueError(f"XML ID not found: {xml_id}")
        return None

    @classmethod
    async def xmlid_to_object(cls, env, xml_id: str, raise_if_not_found: bool = True):
        """
        Convert XML ID to actual record object
        
        Args:
            env: Environment instance
            xml_id: XML ID in format 'module.name' or just 'name'
            raise_if_not_found: Whether to raise exception if not found
            
        Returns:
            Record object, or None if not found and raise_if_not_found=False
        """
        result = await cls.xmlid_lookup(xml_id)
        if result:
            model = env[result['model']]
            return await model.browse(result['res_id'])
        elif raise_if_not_found:
            raise ValueError(f"XML ID not found: {xml_id}")
        return None

    @classmethod
    async def create_or_update_xmlid(
        cls, module: str, name: str, model: str, res_id: str,
        noupdate: bool = False
    ):
        """
        Create or update an XML ID mapping
        
        Args:
            module: Module name
            name: XML ID name
            model: Model name
            res_id: Record ID
            noupdate: No update flag
            
        Returns:
            The ir.model.data record
        """
        # Check if XML ID already exists
        existing = await cls.search([('module', '=', module), ('name', '=', name)], limit=1)
        
        if existing:
            # Update existing record if noupdate is False
            if not existing.noupdate:
                await existing.write({
                    'model': model,
                    'res_id': res_id,
                    'noupdate': noupdate
                })
            return existing
        else:
            # Create new record
            return await cls.create({
                'module': module,
                'name': name,
                'model': model,
                'res_id': res_id,
                'noupdate': noupdate
            })

    @classmethod
    async def get_object_reference(cls, module: str, name: str):
        """
        Get object reference by module and name
        
        Args:
            module: Module name
            name: XML ID name
            
        Returns:
            Tuple of (model, res_id)
        """
        record = await cls.search([('module', '=', module), ('name', '=', name)], limit=1)
        if record:
            return (record.model, record.res_id)
        raise ValueError(f"No record found for XML ID: {module}.{name}")

    @classmethod
    async def unlink_xmlids(cls, module: str):
        """
        Remove all XML IDs for a specific module
        
        Args:
            module: Module name
        """
        records = await cls.search([('module', '=', module)])
        if records:
            await records.unlink()

# The model is automatically registered via the metaclass
