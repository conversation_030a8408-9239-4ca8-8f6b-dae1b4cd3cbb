"""
Base classes for ERP CLI commands
"""

import argparse
from abc import ABC, abstractmethod
from typing import Dict, Optional


class BaseCommand(ABC):
    """Base class for CLI commands"""

    def __init__(self):
        self.name = self.__class__.__name__.lower().replace("command", "")

    @abstractmethod
    def add_arguments(self, parser: argparse.ArgumentParser):
        """Add command-specific arguments to parser"""
        pass

    @abstractmethod
    def handle(self, args: argparse.Namespace) -> int:
        """Handle the command execution"""
        pass

    def print_success(self, message: str):
        """Print success message"""
        print(f"✓ {message}")

    def print_error(self, message: str):
        """Print error message"""
        print(f"✗ {message}")

    def print_warning(self, message: str):
        """Print warning message"""
        print(f"⚠ {message}")

    def print_info(self, message: str):
        """Print info message"""
        print(f"ℹ {message}")


class CommandGroup(ABC):
    """Base class for command groups"""

    def __init__(self):
        self.commands: Dict[str, BaseCommand] = {}

    @abstractmethod
    def add_commands(self, subparsers, parent_parser=None):
        """Add commands to subparsers"""
        pass

    def register_command(self, command: BaseCommand):
        """Register a command"""
        self.commands[command.name] = command

    def get_command(self, name: str) -> Optional[BaseCommand]:
        """Get command by name"""
        return self.commands.get(name)

    def handle_command(self, command_name: str, args: argparse.Namespace) -> int:
        """Handle command execution"""
        command = self.get_command(command_name)
        if command:
            return command.handle(args)
        else:
            print(f"Unknown command: {command_name}")
            return 1
