"""
System information utilities
Provides system information display and database info utilities
"""

import platform
import sys
from datetime import datetime
from typing import Any, Dict


class SystemInfo:
    """System information utilities"""

    @staticmethod
    def get_python_info() -> Dict[str, str]:
        """Get Python version and implementation info"""
        return {
            "version": sys.version,
            "version_info": ".".join(map(str, sys.version_info[:3])),
            "implementation": platform.python_implementation(),
            "executable": sys.executable,
        }

    @staticmethod
    def get_platform_info() -> Dict[str, str]:
        """Get platform and OS information"""
        return {
            "system": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "architecture": platform.architecture()[0],
            "node": platform.node(),
        }

    @staticmethod
    def get_erp_info() -> Dict[str, Any]:
        """Get ERP system information"""
        try:
            # Try to get version from package or config
            version = "1.0.0"  # Default version

            return {
                "version": version,
                "startup_time": datetime.now().isoformat(),
                "python_info": SystemInfo.get_python_info(),
                "platform_info": SystemInfo.get_platform_info(),
            }
        except Exception as e:
            return {
                "version": "unknown",
                "error": str(e),
                "startup_time": datetime.now().isoformat(),
            }


def print_startup_banner():
    """Print ERP system startup banner"""
    erp_info = SystemInfo.get_erp_info()
    python_info = erp_info.get("python_info", {})
    platform_info = erp_info.get("platform_info", {})

    print("=" * 60)
    print("🚀 ERP System Starting")
    print("=" * 60)
    print(f"Version: {erp_info.get('version', 'unknown')}")
    print(
        f"Python: {python_info.get('version_info', 'unknown')} ({python_info.get('implementation', 'unknown')})"
    )
    print(
        f"Platform: {platform_info.get('system', 'unknown')} {platform_info.get('release', '')}"
    )
    print(f"Architecture: {platform_info.get('architecture', 'unknown')}")
    print(f"Started at: {erp_info.get('startup_time', 'unknown')}")
    print("=" * 60)


async def get_database_info(db_manager) -> Dict[str, Any]:
    """
    Get database information and statistics

    Args:
        db_manager: Database manager instance

    Returns:
        Dictionary containing database information
    """
    try:
        # Get PostgreSQL version
        version_result = await db_manager.fetchrow("SELECT version()")
        pg_version = version_result["version"] if version_result else "unknown"

        # Get database name and size
        db_name = await db_manager.fetchval("SELECT current_database()")
        db_size_result = await db_manager.fetchrow(
            "SELECT pg_size_pretty(pg_database_size(current_database())) as size"
        )
        db_size = db_size_result["size"] if db_size_result else "unknown"

        # Get table count
        table_count = await db_manager.fetchval(
            """SELECT COUNT(*) FROM information_schema.tables 
               WHERE table_schema = 'public' AND table_type = 'BASE TABLE'"""
        )

        # Get connection info
        connection_info = await db_manager.fetchrow(
            """SELECT 
                current_user as username,
                inet_server_addr() as server_addr,
                inet_server_port() as server_port
            """
        )

        return {
            "database_name": db_name,
            "postgresql_version": pg_version,
            "database_size": db_size,
            "table_count": table_count or 0,
            "username": connection_info["username"] if connection_info else "unknown",
            "server_address": (
                connection_info["server_addr"] if connection_info else "unknown"
            ),
            "server_port": (
                connection_info["server_port"] if connection_info else "unknown"
            ),
            "connected": True,
        }

    except Exception as e:
        return {"error": str(e), "connected": False}


def print_database_info(db_info: Dict[str, Any]):
    """
    Print database information in a formatted way

    Args:
        db_info: Database information dictionary
    """
    print("\n" + "=" * 50)
    print("📊 Database Information")
    print("=" * 50)

    if db_info.get("connected"):
        print(f"Database: {db_info.get('database_name', 'unknown')}")
        print(f"PostgreSQL: {db_info.get('postgresql_version', 'unknown')}")
        print(f"Size: {db_info.get('database_size', 'unknown')}")
        print(f"Tables: {db_info.get('table_count', 0)}")
        print(f"User: {db_info.get('username', 'unknown')}")
        print(
            f"Server: {db_info.get('server_address', 'unknown')}:{db_info.get('server_port', 'unknown')}"
        )
        print("Status: ✅ Connected")
    else:
        print("Status: ❌ Not Connected")
        if "error" in db_info:
            print(f"Error: {db_info['error']}")

    print("=" * 50)


def get_memory_usage() -> Dict[str, Any]:
    """
    Get current memory usage information

    Returns:
        Dictionary containing memory usage info
    """
    try:
        import psutil

        process = psutil.Process()
        memory_info = process.memory_info()

        return {
            "rss": memory_info.rss,  # Resident Set Size
            "vms": memory_info.vms,  # Virtual Memory Size
            "rss_mb": round(memory_info.rss / 1024 / 1024, 2),
            "vms_mb": round(memory_info.vms / 1024 / 1024, 2),
            "percent": process.memory_percent(),
        }
    except ImportError:
        return {"error": "psutil not available"}
    except Exception as e:
        return {"error": str(e)}


def get_system_stats() -> Dict[str, Any]:
    """
    Get comprehensive system statistics

    Returns:
        Dictionary containing system statistics
    """
    stats = {
        "timestamp": datetime.now().isoformat(),
        "erp_info": SystemInfo.get_erp_info(),
        "memory_usage": get_memory_usage(),
    }

    try:
        import psutil

        # CPU information
        stats["cpu"] = {
            "count": psutil.cpu_count(),
            "percent": psutil.cpu_percent(interval=1),
            "load_avg": psutil.getloadavg() if hasattr(psutil, "getloadavg") else None,
        }

        # Memory information
        memory = psutil.virtual_memory()
        stats["system_memory"] = {
            "total": memory.total,
            "available": memory.available,
            "percent": memory.percent,
            "total_gb": round(memory.total / 1024 / 1024 / 1024, 2),
            "available_gb": round(memory.available / 1024 / 1024 / 1024, 2),
        }

        # Disk information
        disk = psutil.disk_usage("/")
        stats["disk"] = {
            "total": disk.total,
            "used": disk.used,
            "free": disk.free,
            "percent": (disk.used / disk.total) * 100,
            "total_gb": round(disk.total / 1024 / 1024 / 1024, 2),
            "used_gb": round(disk.used / 1024 / 1024 / 1024, 2),
            "free_gb": round(disk.free / 1024 / 1024 / 1024, 2),
        }

    except ImportError:
        stats["psutil_error"] = "psutil not available for detailed system stats"
    except Exception as e:
        stats["stats_error"] = str(e)

    return stats


def print_system_stats(stats: Dict[str, Any]):
    """
    Print system statistics in a formatted way

    Args:
        stats: System statistics dictionary
    """
    print("\n" + "=" * 50)
    print("📈 System Statistics")
    print("=" * 50)

    # ERP info
    erp_info = stats.get("erp_info", {})
    print(f"ERP Version: {erp_info.get('version', 'unknown')}")

    # Memory usage
    memory = stats.get("memory_usage", {})
    if "error" not in memory:
        print(
            f"Process Memory: {memory.get('rss_mb', 0)} MB ({memory.get('percent', 0):.1f}%)"
        )

    # System memory
    sys_memory = stats.get("system_memory", {})
    if sys_memory:
        print(
            f"System Memory: {sys_memory.get('available_gb', 0):.1f}GB / {sys_memory.get('total_gb', 0):.1f}GB available"
        )

    # CPU
    cpu = stats.get("cpu", {})
    if cpu:
        print(f"CPU: {cpu.get('count', 0)} cores, {cpu.get('percent', 0):.1f}% usage")

    # Disk
    disk = stats.get("disk", {})
    if disk:
        print(
            f"Disk: {disk.get('free_gb', 0):.1f}GB / {disk.get('total_gb', 0):.1f}GB free"
        )

    print(f"Timestamp: {stats.get('timestamp', 'unknown')}")
    print("=" * 50)
