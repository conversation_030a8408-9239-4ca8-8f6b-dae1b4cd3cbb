"""
Database transaction management
"""

from .pool import ConnectionPool


class TransactionManager:
    """Database transaction manager"""

    def __init__(self, connection_pool: ConnectionPool):
        self.pool = connection_pool

    async def begin_transaction(self):
        """Begin a transaction"""
        async with self.pool.acquire_connection() as conn:
            return await conn.execute("BEGIN")

    async def commit_transaction(self):
        """Commit current transaction"""
        async with self.pool.acquire_connection() as conn:
            return await conn.execute("COMMIT")

    async def rollback_transaction(self):
        """Rollback current transaction"""
        async with self.pool.acquire_connection() as conn:
            return await conn.execute("ROLLBACK")
