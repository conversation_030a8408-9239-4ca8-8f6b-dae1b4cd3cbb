"""
AsyncLocalStorage implementation for ERP context management
"""

import contextvars
from contextlib import asynccontextmanager
from typing import Awaitable, Callable, Generic, TypeVar

T = TypeVar("T")


class AsyncLocalStorage(Generic[T]):
    """
    Python equivalent of Node.js AsyncLocalStorage
    Uses contextvars to maintain context across async operations
    """

    def __init__(self, name: str = None):
        self._context_var: contextvars.ContextVar[T] = contextvars.ContextVar(
            name or f"async_local_storage_{id(self)}"
        )

    def get(self, default: T = None) -> T:
        """Get the current context value"""
        try:
            return self._context_var.get()
        except LookupError:
            return default

    def set(self, value: T) -> contextvars.Token:
        """Set the context value and return a token"""
        return self._context_var.set(value)

    def reset(self, token: contextvars.Token) -> None:
        """Reset the context to a previous state using a token"""
        self._context_var.reset(token)

    @asynccontextmanager
    async def run(self, value: T):
        """Run code with a specific context value"""
        token = self.set(value)
        try:
            yield value
        finally:
            self.reset(token)

    def run_sync(self, value: T, func: Callable[..., T], *args, **kwargs) -> T:
        """Run a synchronous function with a specific context value"""
        token = self.set(value)
        try:
            return func(*args, **kwargs)
        finally:
            self.reset(token)

    async def run_async(
        self, value: T, func: Callable[..., Awaitable[T]], *args, **kwargs
    ) -> T:
        """Run an async function with a specific context value"""
        async with self.run(value):
            return await func(*args, **kwargs)
